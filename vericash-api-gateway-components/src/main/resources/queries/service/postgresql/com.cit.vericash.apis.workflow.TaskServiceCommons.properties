ValidateApprovalNotMaker=SELECT SERVICE_ID FROM ACTIVITY_TRAIL WHERE ACTION = 'Make' and USER_ID = %1$s \
  AND TASK_ID = %2$s
EligibleForApproval=SELECT BE_ROLE.APPROVE_SERVICE,ACTIVITY_TRAIL.STATUS FROM BE_ROLE \
  INNER JOIN BE_ROLE_APPROVAL_SERVICES ON BE_ROLE_APPROVAL_SERVICES.ROLE_ID = BE_ROLE. ID \
  INNER JOIN ACTIVITY_TRAIL ON ACTIVITY_TRAIL.SERVICE_ID = BE_ROLE_APPROVAL_SERVICES.SERVICE_TYPE_ID \
  INNER JOIN BE_CUSTOMER ON BE_CUSTOMER. ROLE = BE_ROLE. ID \
  WHERE ACTIVITY_TRAIL.TASK_ID =  %1$s AND \
  BE_ROLE_APPROVAL_SERVICES.SERVICE_TYPE_ID = ACTIVITY_TRAIL.SERVICE_ID AND \
  BE_CUSTOMER.PARENT_CUSTOMER_ID = %2$S

