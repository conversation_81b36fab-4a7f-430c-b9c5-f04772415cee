<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">




    <changeSet failOnError="false" author="fatma" id="Validate Customer and default PM SERVICE">
        <sql>
            INSERT INTO "SERVICE_CONFIG_MAP" 
			("SERVICE_TYPE_ID", "IS_GENERIC_SERVICE","SERVICE_TYPE_NAME", "SERVICE_MODE", "SERVICE_CODE", "IS_INDEMNITY", "SEPARATE_QUEUE", "SEPARATE_FRAMEWORK_QUEUE", "HAS_WORKFLOW", "SERVICE_CATEGORY", "ROLE_BASED") 
			VALUES ('8890971', '0', 'Validate Customer and default PM', '0', '8890971', '1', '0', '0', '0', '0', '0');
   
 
        </sql>
    </changeSet>
    <changeSet failOnError="true" author="fatma" id="AllowedPAYMENTMETHODS">
        <sql>
            INSERT INTO "SERVICES_PAYMENTMETHOD" ("ID", "API_CODE", "PAYMENMETHODTYPE", "LOOKUP_ENUM", "USER_TYPE") VALUES ((select  max( ID ) +1   from "SERVICES_PAYMENTMETHOD"), '334', '842', '4', '2');
             INSERT INTO "SERVICES_PAYMENTMETHOD" ("ID", "API_CODE", "PAYMENMETHODTYPE", "LOOKUP_ENUM", "USER_TYPE") VALUES ((select  max( ID ) +1   from "SERVICES_PAYMENTMETHOD"), '333', '842', '4', '2');


            INSERT INTO "SERVICES_PAYMENTMETHOD" ("ID", "API_CODE", "PAYMENMETHODTYPE", "LOOKUP_ENUM", "USER_TYPE") VALUES ((select  max( ID ) +1   from "SERVICES_PAYMENTMETHOD"), '334', '848', '4', '2');
             INSERT INTO "SERVICES_PAYMENTMETHOD" ("ID", "API_CODE", "PAYMENMETHODTYPE", "LOOKUP_ENUM", "USER_TYPE") VALUES ((select  max( ID ) +1   from "SERVICES_PAYMENTMETHOD"), '333', '848', '4', '2');
			  </sql>
    </changeSet>
    
	
	 <changeSet failOnError="true" author="fatma" id="ScreenPerPAYMENTMETHODS">
        <sql>
            INSERT INTO "SERVICES_PAYMENTMETHOD" ("ID", "API_CODE", "PAYMENMETHODTYPE", "LOOKUP_ENUM", "USER_TYPE") VALUES ((select  max( ID ) +1   from "SERVICES_PAYMENTMETHOD"), '334', '842', '1', '2');
             INSERT INTO "SERVICES_PAYMENTMETHOD" ("ID", "API_CODE", "PAYMENMETHODTYPE", "LOOKUP_ENUM", "USER_TYPE") VALUES ((select  max( ID ) +1   from "SERVICES_PAYMENTMETHOD"), '333', '842', '1', '2');


        
			  </sql>
    </changeSet>
    


  
</databaseChangeLog>