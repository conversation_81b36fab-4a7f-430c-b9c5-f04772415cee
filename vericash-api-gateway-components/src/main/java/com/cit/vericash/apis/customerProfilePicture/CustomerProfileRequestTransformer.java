package com.cit.vericash.apis.customerProfilePicture;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.customerProfilePicture.CustomerProfileRequestTransformer")
public class CustomerProfileRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

    }

}
