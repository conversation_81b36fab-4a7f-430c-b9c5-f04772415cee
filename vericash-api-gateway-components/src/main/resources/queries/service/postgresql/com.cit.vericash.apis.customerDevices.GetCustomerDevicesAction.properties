devicesQuerySb=SELECT CUSTOMER_DEVICE_ID , CUSTOMER_DEVICES.NAME AS DEVICE_NAME , CUSTOMER_DEVICES.STATUS , \
  DEVICE_STATUS.NAME AS STATUS_STR , LAST_ACCESS , APP_VERSION , IMEI , DEVICE_MANUFACTURER , \
  MODEL, PLATFORM , DEVICE_VDI , DEVICE_ODI , REGISTRATION_ID \
  FROM CUSTOMER_DEVICES LEFT JOIN DEVICE_STATUS ON CUSTOMER_DEVICES.STATUS = DEVICE_STATUS.ID \
  WHERE CUSTOMER_ID = ? 
devicesQuerySb_condition=AND STATUS = ?
devicesQuerySb_condition_2=AND STATUS <> ?
getAllowedActions=SELECT DEVICE_STATUS,\"ACTION\" FROM DEVICE_ALLOWED_ACTIONS