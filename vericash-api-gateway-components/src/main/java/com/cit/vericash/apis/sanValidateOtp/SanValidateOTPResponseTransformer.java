package com.cit.vericash.apis.sanValidateOtp;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.sanValidateOtp.SanValidateOTPResponseTransformer")
public class SanValidateOTPResponseTransformer implements MuleResponseTransformer {


    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, String> result = new HashMap<>();
        System.out.println("HAAAAAAAAAAAAAAAAAAAI");
        try {
            HashMap smsContent = (HashMap) businessMessage.getParameters().get("SendSMSWithRedemptionCode");

            Integer value = (Integer)  smsContent.get("redemptionCode");
            System.out.println("value: " + value);
            result.put("registrationStatus", value.toString());
            System.out.println(result.toString());
        } catch (Exception e) {

        }

        return result;
    }
}
