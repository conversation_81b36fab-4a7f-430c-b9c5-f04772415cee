package com.cit.vericash.apis.AdsService.FilterResponsabilityChain;

import com.cit.vericash.api.entity.Ad;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.dto.request.Request;
import com.mastercard.api.core.exception.ApiException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class PublishDateFilterTest {

    private PublishDateFilter publishDateFilter;

    @Mock
    private AdsFilterHandler nextHandler;

    @Mock
    private Request request;

    private LinkedHashMap<String, Object> requiredData = new LinkedHashMap<>();

    @BeforeEach
    void setUp() {
        publishDateFilter = new PublishDateFilter(requiredData);
    }

    @Test
    void handleRequest_shouldIncludeRealtimeAds() throws APIException, ApiException {
        // Arrange
        Ad realtimeAd = new Ad();
        realtimeAd.setExecutionModeId(1); // REALTIME_ID

        Stream<Ad> adStream = Stream.of(realtimeAd);

        // Act
        Stream<Ad> result = publishDateFilter.handleRequest(request, adStream);

        // Assert
        assertEquals(1, result.count());
    }

    @Test
    void handleRequest_shouldIncludeScheduledAdsWithinDateRange() throws APIException, ApiException {
        // Arrange
        Ad scheduledAd = new Ad();
        scheduledAd.setExecutionModeId(2); // SCHEDULED_ID
        scheduledAd.setPublishDate(LocalDateTime.now().minusDays(1));
        scheduledAd.setExpiryDate(LocalDateTime.now().plusDays(1));

        Stream<Ad> adStream = Stream.of(scheduledAd);

        // Act
        Stream<Ad> result = publishDateFilter.handleRequest(request, adStream);

        // Assert
        assertEquals(1, result.count());
    }

    @Test
    void handleRequest_shouldExcludeExpiredAds() throws APIException, ApiException {
        // Arrange
        Ad expiredAd = new Ad();
        expiredAd.setExecutionModeId(2); // SCHEDULED_ID
        expiredAd.setPublishDate(LocalDateTime.now().minusDays(2));
        expiredAd.setExpiryDate(LocalDateTime.now().minusDays(1));

        Stream<Ad> adStream = Stream.of(expiredAd);

        // Act
        Stream<Ad> result = publishDateFilter.handleRequest(request, adStream);

        // Assert
        assertEquals(0, result.count());
    }


    @Test
    void handleRequest_shouldThrowAPIExceptionOnError() {
        // Arrange
        PublishDateFilter filter = new PublishDateFilter(requiredData) {
            @Override
            Stream<Ad> filterAdsByPublishDateAndTime(Stream<Ad> adStream) {
                throw new RuntimeException("Test exception");
            }
        };

        // Act & Assert
        assertThrows(APIException.class, () -> filter.handleRequest(request, Stream.empty()));
    }
}