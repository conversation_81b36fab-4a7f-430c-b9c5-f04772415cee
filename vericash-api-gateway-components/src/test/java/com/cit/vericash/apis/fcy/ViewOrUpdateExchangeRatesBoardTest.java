package com.cit.vericash.apis.fcy;

import com.cit.preIntegration.IntegrationService;
import com.cit.vericash.api.entity.GeneralLookups;
import com.cit.vericash.api.repository.GeneralLookupsRepository;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.cacheUtil.CacheControlService;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.integrationfremwork.logger.VCExternalLoggerComponent;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentLinkedQueue;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ViewOrUpdateExchangeRatesBoardTest {
    public static final String CUSTOMER_ID = "101566";
    public static final String CURRENCY_ID = "104";
    public static final String APITOKEN = "8g48hTJgMUMgXyofXJrwg51kYPud65uQ+uvPHQZ5/Dc=";
    private static final Logger log = LoggerFactory.getLogger(ViewOrUpdateExchangeRatesBoardTest.class);
    @Mock
    CacheControlService cacheControlService;
    @Mock
    ConnectionUtil connectionUtil;
    @Mock
    IntegrationService integrationService;
    @Mock
    GeneralLookupsRepository generalLookupsRepository;
    @Mock
    PropertyLoaderComponent propertyLoaderComponent;
    @Mock
    VCExternalLoggerComponent externalLoggerComponent;
    @InjectMocks
    ViewOrUpdateExchangeRatesBoard viewOrUpdateExchangeRatesBoard;

    private final Header header = new Header();
    private final Payload payload = new Payload();
    private final Message message = new Message();
    private final Request request = new Request();
    private final List<GeneralLookups> lookups = new ArrayList<>();
    private final GeneralLookups generalLookup = new GeneralLookups();
    private final List<Record> currencyCodeRecordList = new ArrayList<>();
    private final List<Record> currencyConfigsList = new ArrayList<>();
    private final List<Record> customerFavorites = new ArrayList<>();

    @Before
    public void setUp() throws Exception {

        Record currencyCodeRecord = new Record();
        currencyCodeRecord.put("CODE", "NGN");

        currencyCodeRecordList.add(currencyCodeRecord);


        Record configRecord1 = new Record();
        configRecord1.put("ID", 149);
        configRecord1.put("CODE", "USD");
        configRecord1.put("BASE", 1);
        configRecord1.put("STATUS", 1);

        Record configRecord2 = new Record();
        configRecord2.put("ID", 44);
        configRecord2.put("CODE", "EUR");
        configRecord2.put("BASE", 0);
        configRecord2.put("STATUS", 1);

        Record configRecord3 = new Record();
        configRecord3.put("ID", 47);
        configRecord3.put("CODE", "GBP");
        configRecord3.put("BASE", 1);
        configRecord3.put("STATUS", 1);

        Record configRecord4 = new Record();
        configRecord4.put("ID", 28);
        configRecord4.put("CODE", "CHF");
        configRecord4.put("BASE", 0);
        configRecord4.put("STATUS", 1);

        Record configRecord5 = new Record();
        configRecord5.put("ID", 122);
        configRecord5.put("CODE", "SAR");
        configRecord5.put("BASE", 0);
        configRecord5.put("STATUS", 1);

        Record configRecord6 = new Record();
        configRecord6.put("ID", 78);
        configRecord6.put("CODE", "KWD");
        configRecord6.put("BASE", 1);
        configRecord6.put("STATUS", 1);

        currencyConfigsList.add(configRecord1);
        currencyConfigsList.add(configRecord2);
        currencyConfigsList.add(configRecord3);
        currencyConfigsList.add(configRecord4);
        currencyConfigsList.add(configRecord5);
        currencyConfigsList.add(configRecord6);


        Record favoriteRecord1= new Record();
        favoriteRecord1.put("CURRENCY_ID", 15);

        Record favoriteRecord2 = new Record();
        favoriteRecord2.put("CURRENCY_ID", 15);

        Record favoriteRecord3 = new Record();
        favoriteRecord3.put("CURRENCY_ID", 15);

        customerFavorites.add(favoriteRecord1);
        customerFavorites.add(favoriteRecord2);
        customerFavorites.add(favoriteRecord3);

    }

    @Test
    public void returnDefaultCurrencyRatesChangeBaseDisabledNoCustomerIdNotCachedSuccess() throws Exception {
        payload.put("currencyId", CURRENCY_ID);
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(connectionUtil.executeSelect(eq("SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?"), anyList()))
                .thenReturn(List.of());
        when(cacheControlService.getCachedObjectOrNull(anyString(), any(Class.class)))
                .thenReturn(null);
        when(propertyLoaderComponent.getPropertyAsLong(eq("viewExchangeExpireCacheEvery")))
                .thenReturn(300L);
        when(connectionUtil.executeSelect(eq("SELECT CODE FROM CURRENCY c WHERE c.ID=?"), anyList()))
                .thenReturn(currencyCodeRecordList);
        when(integrationService.execute(any(), eq("getRates")))
                .thenReturn("{\"payload\":{\"baseCurrency\":\"NGN\"},\"rates\":[{\"sellRate\":47.12,\"currencyID\":\"USD\",\"buyRate\":47.02},{\"sellRate\":50.8708,\"currencyID\":\"EUR\",\"buyRate\":50.7064},{\"sellRate\":59.1921,\"currencyID\":\"GBP\",\"buyRate\":59.0242},{\"sellRate\":51.9,\"currencyID\":\"CHF\",\"buyRate\":51.7386},{\"sellRate\":12.5647,\"currencyID\":\"SAR\",\"buyRate\":12.4755},{\"sellRate\":153.3355,\"currencyID\":\"KWD\",\"buyRate\":148.3061},{\"sellRate\":1.3355,\"currencyID\":\"AFN\",\"buyRate\":1.3061},{\"sellRate\":0.4291,\"currencyID\":\"JPY\",\"buyRate\":0.4234},{\"sellRate\":0.6429,\"currencyID\":\"INR\",\"buyRate\":0.6382},{\"sellRate\":7.0038,\"currencyID\":\"CNY\",\"buyRate\":6.9754},{\"sellRate\":35.6278,\"currencyID\":\"AUD\",\"buyRate\":35.4819},{\"sellRate\":36.1655,\"currencyID\":\"CAD\",\"buyRate\":36.0348},{\"sellRate\":32.8301,\"currencyID\":\"NZD\",\"buyRate\":32.7103},{\"sellRate\":6.0847,\"currencyID\":\"HKD\",\"buyRate\":6.0572}],\"header\":{\"apiCode\":\"889101\",\"serviceCode\":\"889101\",\"walletShortCode\":\"2020\",\"timestamp\":**********,\"channelCode\":\"test_1\"},\"error\":{},\"extraFields\":\"{\\\"integrationLogs\\\":[{\\\"logId\\\":\\\"c9551876-20f8-4607-a788-eae31724f2a2\\\",\\\"stackId\\\":\\\"b2c9df8f-0d56-4daa-a13c-dd3feab42de1\\\",\\\"request\\\":\\\"{\\\\\\\"baseCurrency\\\\\\\":\\\\\\\"NGN\\\\\\\"}\\\",\\\"requestDate\\\":1716281225565,\\\"response\\\":\\\"{\\\\r\\\\n\\\\t\\\\\\\"disclaimer\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/terms\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"license\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/license\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"timestamp\\\\\\\": **********,\\\\r\\\\n\\\\t\\\\\\\"base\\\\\\\": \\\\\\\"NGN\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"rates\\\\\\\": [\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"USD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 47.02,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 47.12\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"EUR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 50.7064,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 50.8708\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"GBP\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 59.0242,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 59.1921\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CHF\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 51.7386,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 51.9\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"SAR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 12.4755,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 12.5647\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"KWD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 148.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 153.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AFN\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 1.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 1.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"JPY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.4234,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.4291\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"INR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.6382,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.6429\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CNY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.9754,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 7.0038\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AUD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 35.4819,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 35.6278\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CAD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 36.0348,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 36.1655\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"NZD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 32.7103,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 32.8301\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"HKD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.0572,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 6.0847\\\\r\\\\n\\\\t\\\\t}\\\\r\\\\n\\\\t]\\\\r\\\\n}\\\",\\\"responseDate\\\":1716281225758}]}\",\"status\":{\"statusMsg\":\"\",\"errorFlag\":false,\"statusCode\":\"\"}}\n");
        when(connectionUtil.executeSelect(eq("SELECT c.ID,c.CODE,cc.BASE,cc.STATUS FROM CURRENCY c JOIN CURRENCY_CONFIG cc ON c.ID = cc.CURRENCY_ID WHERE c.CODE IS NOT NULL AND cc.BASE != 0 OR cc.STATUS != 0"), anyList()))
                .thenReturn(currencyConfigsList);

        Map<String, Object> response = (Map<String, Object>) viewOrUpdateExchangeRatesBoard.process(request);

        ConcurrentLinkedQueue<Object> rates =(ConcurrentLinkedQueue<Object>) response.get("rates");
        ConcurrentLinkedQueue<String> base = (ConcurrentLinkedQueue<String>) response.get("allowedBase");
        ConcurrentLinkedQueue<String> favorites = (ConcurrentLinkedQueue<String>) response.get("favorites");


        assertEquals(6, rates.size());
        assertEquals(0, base.size());
        assertEquals(0, favorites.size());

    }

    @Test
    public void integrationApiFailed() throws Exception {
        payload.put("currencyId", CURRENCY_ID);
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(connectionUtil.executeSelect(eq("SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?"), anyList()))
                .thenReturn(List.of());
        when(cacheControlService.getCachedObjectOrNull(anyString(), any(Class.class)))
                .thenReturn(null);
        when(propertyLoaderComponent.getPropertyAsLong(eq("viewExchangeExpireCacheEvery")))
                .thenReturn(300L);
        when(connectionUtil.executeSelect(eq("SELECT CODE FROM CURRENCY c WHERE c.ID=?"), anyList()))
                .thenReturn(currencyCodeRecordList);
        when(integrationService.execute(any(), eq("getRates")))
                .thenReturn("{\"payload\":{\"baseCurrency\":\"NGN\"},\"rates\":null,\"header\":{\"apiCode\":\"889101\",\"serviceCode\":\"889101\",\"walletShortCode\":\"2020\",\"timestamp\":**********,\"channelCode\":\"test_1\"},\"error\":{ \"code\": \"ITG85615\"},\"extraFields\":\"{\\\"integrationLogs\\\":[{\\\"logId\\\":\\\"c9551876-20f8-4607-a788-eae31724f2a2\\\",\\\"stackId\\\":\\\"b2c9df8f-0d56-4daa-a13c-dd3feab42de1\\\",\\\"request\\\":\\\"{\\\\\\\"baseCurrency\\\\\\\":\\\\\\\"NGN\\\\\\\"}\\\",\\\"requestDate\\\":1716281225565,\\\"response\\\":\\\"{\\\\r\\\\n\\\\t\\\\\\\"disclaimer\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/terms\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"license\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/license\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"timestamp\\\\\\\": **********,\\\\r\\\\n\\\\t\\\\\\\"base\\\\\\\": \\\\\\\"NGN\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"rates\\\\\\\": [\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"USD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 47.02,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 47.12\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"EUR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 50.7064,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 50.8708\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"GBP\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 59.0242,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 59.1921\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CHF\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 51.7386,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 51.9\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"SAR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 12.4755,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 12.5647\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"KWD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 148.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 153.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AFN\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 1.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 1.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"JPY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.4234,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.4291\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"INR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.6382,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.6429\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CNY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.9754,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 7.0038\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AUD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 35.4819,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 35.6278\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CAD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 36.0348,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 36.1655\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"NZD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 32.7103,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 32.8301\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"HKD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.0572,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 6.0847\\\\r\\\\n\\\\t\\\\t}\\\\r\\\\n\\\\t]\\\\r\\\\n}\\\",\\\"responseDate\\\":1716281225758}]}\",\"status\":{\"statusMsg\":\"\",\"errorFlag\":false,\"statusCode\":\"\"}}\n");

        APIException exception = assertThrows(APIException.class, () -> viewOrUpdateExchangeRatesBoard.process(request));

        assertEquals("VAL186208040", exception.getErrorCode());

    }

    @Test
    public void integrationApiCustomErrorFailed() throws Exception {
        payload.put("currencyId", CURRENCY_ID);
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(connectionUtil.executeSelect(eq("SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?"), anyList()))
                .thenReturn(List.of());
        when(cacheControlService.getCachedObjectOrNull(anyString(), any(Class.class)))
                .thenReturn(null);
        when(propertyLoaderComponent.getPropertyAsLong(eq("viewExchangeExpireCacheEvery")))
                .thenReturn(300L);
        when(connectionUtil.executeSelect(eq("SELECT CODE FROM CURRENCY c WHERE c.ID=?"), anyList()))
                .thenReturn(currencyCodeRecordList);
        when(integrationService.execute(any(), eq("getRates")))
                .thenReturn("{\"payload\":{\"baseCurrency\":\"NGN\"},\"rates\":null,\"header\":{\"apiCode\":\"889101\",\"serviceCode\":\"889101\",\"walletShortCode\":\"2020\",\"timestamp\":**********,\"channelCode\":\"test_1\"},\"error\":{ \"code\": \"ITG546886\"},\"extraFields\":\"{\\\"integrationLogs\\\":[{\\\"logId\\\":\\\"c9551876-20f8-4607-a788-eae31724f2a2\\\",\\\"stackId\\\":\\\"b2c9df8f-0d56-4daa-a13c-dd3feab42de1\\\",\\\"request\\\":\\\"{\\\\\\\"baseCurrency\\\\\\\":\\\\\\\"NGN\\\\\\\"}\\\",\\\"requestDate\\\":1716281225565,\\\"response\\\":\\\"{\\\\r\\\\n\\\\t\\\\\\\"disclaimer\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/terms\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"license\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/license\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"timestamp\\\\\\\": **********,\\\\r\\\\n\\\\t\\\\\\\"base\\\\\\\": \\\\\\\"NGN\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"rates\\\\\\\": [\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"USD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 47.02,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 47.12\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"EUR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 50.7064,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 50.8708\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"GBP\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 59.0242,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 59.1921\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CHF\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 51.7386,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 51.9\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"SAR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 12.4755,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 12.5647\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"KWD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 148.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 153.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AFN\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 1.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 1.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"JPY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.4234,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.4291\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"INR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.6382,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.6429\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CNY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.9754,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 7.0038\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AUD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 35.4819,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 35.6278\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CAD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 36.0348,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 36.1655\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"NZD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 32.7103,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 32.8301\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"HKD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.0572,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 6.0847\\\\r\\\\n\\\\t\\\\t}\\\\r\\\\n\\\\t]\\\\r\\\\n}\\\",\\\"responseDate\\\":1716281225758}]}\",\"status\":{\"statusMsg\":\"\",\"errorFlag\":false,\"statusCode\":\"\"}}\n");

        APIException exception = assertThrows(APIException.class, () -> viewOrUpdateExchangeRatesBoard.process(request));

        assertEquals("VAL186208041", exception.getErrorCode());
    }

    @Test
    public void integrationApiGeneralErrorFailed() throws Exception {
        payload.put("currencyId", CURRENCY_ID);
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");


        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(connectionUtil.executeSelect(eq("SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?"), anyList()))
                .thenReturn(List.of());
        when(cacheControlService.getCachedObjectOrNull(anyString(), any(Class.class)))
                .thenReturn(null);
        when(propertyLoaderComponent.getPropertyAsLong(eq("viewExchangeExpireCacheEvery")))
                .thenReturn(300L);
        when(connectionUtil.executeSelect(eq("SELECT CODE FROM CURRENCY c WHERE c.ID=?"), anyList()))
                .thenReturn(currencyCodeRecordList);
        when(integrationService.execute(any(), eq("getRates")))
                .thenReturn("{\"payload\":{\"baseCurrency\":\"NGN\"},\"rates\":null,\"header\":{\"apiCode\":\"889101\",\"serviceCode\":\"889101\",\"walletShortCode\":\"2020\",\"timestamp\":**********,\"channelCode\":\"test_1\"},\"error\":{ \"code\": \"ITG1982886\"},\"extraFields\":\"{\\\"integrationLogs\\\":[{\\\"logId\\\":\\\"c9551876-20f8-4607-a788-eae31724f2a2\\\",\\\"stackId\\\":\\\"b2c9df8f-0d56-4daa-a13c-dd3feab42de1\\\",\\\"request\\\":\\\"{\\\\\\\"baseCurrency\\\\\\\":\\\\\\\"NGN\\\\\\\"}\\\",\\\"requestDate\\\":1716281225565,\\\"response\\\":\\\"{\\\\r\\\\n\\\\t\\\\\\\"disclaimer\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/terms\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"license\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/license\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"timestamp\\\\\\\": **********,\\\\r\\\\n\\\\t\\\\\\\"base\\\\\\\": \\\\\\\"NGN\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"rates\\\\\\\": [\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"USD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 47.02,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 47.12\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"EUR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 50.7064,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 50.8708\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"GBP\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 59.0242,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 59.1921\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CHF\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 51.7386,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 51.9\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"SAR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 12.4755,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 12.5647\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"KWD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 148.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 153.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AFN\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 1.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 1.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"JPY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.4234,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.4291\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"INR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.6382,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.6429\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CNY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.9754,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 7.0038\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AUD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 35.4819,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 35.6278\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CAD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 36.0348,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 36.1655\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"NZD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 32.7103,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 32.8301\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"HKD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.0572,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 6.0847\\\\r\\\\n\\\\t\\\\t}\\\\r\\\\n\\\\t]\\\\r\\\\n}\\\",\\\"responseDate\\\":1716281225758}]}\",\"status\":{\"statusMsg\":\"\",\"errorFlag\":false,\"statusCode\":\"\"}}\n");

        APIException exception = assertThrows(APIException.class, () -> viewOrUpdateExchangeRatesBoard.process(request));

        assertEquals("VAL186208042", exception.getErrorCode());
    }


    @Test
    public void returnDefaultCurrencyRatesChangeBaseDisabledNoCustomerIdCachedSuccess() throws Exception {
        payload.put("currencyId", CURRENCY_ID);
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(connectionUtil.executeSelect(eq("SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?"), anyList()))
                .thenReturn(List.of());
        when(cacheControlService.getCachedObjectOrNull(anyString(), any(Class.class)))
                .thenReturn(Map.of("lastUpdatedAt", "2024-05-21 13:29:15.18",
                        "USD", Map.of("sellRate", 47.12, "buyRate", 47.02),
                        "EUR", Map.of("sellRate", 50.8708, "buyRate", 50.7064),
                        "CHF", Map.of("sellRate", 51.9, "buyRate", 51.7386),
                        "GBP", Map.of("sellRate", 59.1921, "buyRate", 59.0242),
                        "KWD", Map.of("sellRate", 153.3355, "buyRate", 148.3061),
                        "SAR", Map.of("sellRate", 12.5647, "buyRate", 12.4755)));

        when(connectionUtil.executeSelect(eq("SELECT c.ID,c.CODE,cc.BASE,cc.STATUS FROM CURRENCY c JOIN CURRENCY_CONFIG cc ON c.ID = cc.CURRENCY_ID WHERE c.CODE IS NOT NULL AND cc.BASE != 0 OR cc.STATUS != 0"), anyList()))
                .thenReturn(currencyConfigsList);

        Map<String, Object> response = (Map<String, Object>) viewOrUpdateExchangeRatesBoard.process(request);

        ConcurrentLinkedQueue<Object> rates =(ConcurrentLinkedQueue<Object>) response.get("rates");
        ConcurrentLinkedQueue<String> base = (ConcurrentLinkedQueue<String>) response.get("allowedBase");
        ConcurrentLinkedQueue<String> favorites = (ConcurrentLinkedQueue<String>) response.get("favorites");


        assertEquals(6, rates.size());
        assertEquals(0, base.size());
        assertEquals(0, favorites.size());

    }

    @Test
    public void returnDefaultCurrencyRatesChangeBaseEnabledNoCustomerIdNotCachedSuccess() throws Exception {
        payload.put("currencyId", CURRENCY_ID);
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("1");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(connectionUtil.executeSelect(eq("SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?"), anyList()))
                .thenReturn(List.of());
        when(cacheControlService.getCachedObjectOrNull(anyString(), any(Class.class)))
                .thenReturn(null);
        when(propertyLoaderComponent.getPropertyAsLong(eq("viewExchangeExpireCacheEvery")))
                .thenReturn(300L);
        when(connectionUtil.executeSelect(eq("SELECT CODE FROM CURRENCY c WHERE c.ID=?"), anyList()))
                .thenReturn(currencyCodeRecordList);
        when(integrationService.execute(any(), eq("getRates")))
                .thenReturn("{\"payload\":{\"baseCurrency\":\"NGN\"},\"rates\":[{\"sellRate\":47.12,\"currencyID\":\"USD\",\"buyRate\":47.02},{\"sellRate\":50.8708,\"currencyID\":\"EUR\",\"buyRate\":50.7064},{\"sellRate\":59.1921,\"currencyID\":\"GBP\",\"buyRate\":59.0242},{\"sellRate\":51.9,\"currencyID\":\"CHF\",\"buyRate\":51.7386},{\"sellRate\":12.5647,\"currencyID\":\"SAR\",\"buyRate\":12.4755},{\"sellRate\":153.3355,\"currencyID\":\"KWD\",\"buyRate\":148.3061},{\"sellRate\":1.3355,\"currencyID\":\"AFN\",\"buyRate\":1.3061},{\"sellRate\":0.4291,\"currencyID\":\"JPY\",\"buyRate\":0.4234},{\"sellRate\":0.6429,\"currencyID\":\"INR\",\"buyRate\":0.6382},{\"sellRate\":7.0038,\"currencyID\":\"CNY\",\"buyRate\":6.9754},{\"sellRate\":35.6278,\"currencyID\":\"AUD\",\"buyRate\":35.4819},{\"sellRate\":36.1655,\"currencyID\":\"CAD\",\"buyRate\":36.0348},{\"sellRate\":32.8301,\"currencyID\":\"NZD\",\"buyRate\":32.7103},{\"sellRate\":6.0847,\"currencyID\":\"HKD\",\"buyRate\":6.0572}],\"header\":{\"apiCode\":\"889101\",\"serviceCode\":\"889101\",\"walletShortCode\":\"2020\",\"timestamp\":**********,\"channelCode\":\"test_1\"},\"error\":{},\"extraFields\":\"{\\\"integrationLogs\\\":[{\\\"logId\\\":\\\"c9551876-20f8-4607-a788-eae31724f2a2\\\",\\\"stackId\\\":\\\"b2c9df8f-0d56-4daa-a13c-dd3feab42de1\\\",\\\"request\\\":\\\"{\\\\\\\"baseCurrency\\\\\\\":\\\\\\\"NGN\\\\\\\"}\\\",\\\"requestDate\\\":1716281225565,\\\"response\\\":\\\"{\\\\r\\\\n\\\\t\\\\\\\"disclaimer\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/terms\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"license\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/license\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"timestamp\\\\\\\": **********,\\\\r\\\\n\\\\t\\\\\\\"base\\\\\\\": \\\\\\\"NGN\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"rates\\\\\\\": [\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"USD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 47.02,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 47.12\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"EUR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 50.7064,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 50.8708\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"GBP\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 59.0242,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 59.1921\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CHF\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 51.7386,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 51.9\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"SAR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 12.4755,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 12.5647\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"KWD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 148.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 153.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AFN\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 1.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 1.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"JPY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.4234,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.4291\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"INR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.6382,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.6429\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CNY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.9754,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 7.0038\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AUD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 35.4819,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 35.6278\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CAD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 36.0348,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 36.1655\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"NZD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 32.7103,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 32.8301\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"HKD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.0572,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 6.0847\\\\r\\\\n\\\\t\\\\t}\\\\r\\\\n\\\\t]\\\\r\\\\n}\\\",\\\"responseDate\\\":1716281225758}]}\",\"status\":{\"statusMsg\":\"\",\"errorFlag\":false,\"statusCode\":\"\"}}\n");
        when(connectionUtil.executeSelect(eq("SELECT c.ID,c.CODE,cc.BASE,cc.STATUS FROM CURRENCY c JOIN CURRENCY_CONFIG cc ON c.ID = cc.CURRENCY_ID WHERE c.CODE IS NOT NULL AND cc.BASE != 0 OR cc.STATUS != 0"), anyList()))
                .thenReturn(currencyConfigsList);

        Map<String, Object> response = (Map<String, Object>) viewOrUpdateExchangeRatesBoard.process(request);

        ConcurrentLinkedQueue<Object> rates =(ConcurrentLinkedQueue<Object>) response.get("rates");
        ConcurrentLinkedQueue<String> base = (ConcurrentLinkedQueue<String>) response.get("allowedBase");
        ConcurrentLinkedQueue<String> favorites = (ConcurrentLinkedQueue<String>) response.get("favorites");


        assertEquals(6, rates.size());
        assertEquals(3, base.size());
        assertEquals(0, favorites.size());

    }

    @Test
    public void returnDefaultCurrencyRatesChangeBaseEnabledWithCustomerIdNotCachedSuccess() throws Exception {
        payload.put("customerId", CUSTOMER_ID);
        payload.put("currencyId", CURRENCY_ID);
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("1");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        generalLookup.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        generalLookup.setLookupvalue("1");

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(generalLookupsRepository.findLookupvalueByLookupkey(anyString()))
                .thenReturn(Optional.of(generalLookup));
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(connectionUtil.executeSelect(eq("SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?"), anyList()))
                .thenReturn(List.of());
        when(cacheControlService.getCachedObjectOrNull(anyString(), any(Class.class)))
                .thenReturn(null);
        when(propertyLoaderComponent.getPropertyAsLong(eq("viewExchangeExpireCacheEvery")))
                .thenReturn(300L);
        when(connectionUtil.executeSelect(eq("SELECT CODE FROM CURRENCY c WHERE c.ID=?"), anyList()))
                .thenReturn(currencyCodeRecordList);
        when(integrationService.execute(any(), eq("getRates")))
                .thenReturn("{\"payload\":{\"baseCurrency\":\"NGN\"},\"rates\":[{\"sellRate\":47.12,\"currencyID\":\"USD\",\"buyRate\":47.02},{\"sellRate\":50.8708,\"currencyID\":\"EUR\",\"buyRate\":50.7064},{\"sellRate\":59.1921,\"currencyID\":\"GBP\",\"buyRate\":59.0242},{\"sellRate\":51.9,\"currencyID\":\"CHF\",\"buyRate\":51.7386},{\"sellRate\":12.5647,\"currencyID\":\"SAR\",\"buyRate\":12.4755},{\"sellRate\":153.3355,\"currencyID\":\"KWD\",\"buyRate\":148.3061},{\"sellRate\":1.3355,\"currencyID\":\"AFN\",\"buyRate\":1.3061},{\"sellRate\":0.4291,\"currencyID\":\"JPY\",\"buyRate\":0.4234},{\"sellRate\":0.6429,\"currencyID\":\"INR\",\"buyRate\":0.6382},{\"sellRate\":7.0038,\"currencyID\":\"CNY\",\"buyRate\":6.9754},{\"sellRate\":35.6278,\"currencyID\":\"AUD\",\"buyRate\":35.4819},{\"sellRate\":36.1655,\"currencyID\":\"CAD\",\"buyRate\":36.0348},{\"sellRate\":32.8301,\"currencyID\":\"NZD\",\"buyRate\":32.7103},{\"sellRate\":6.0847,\"currencyID\":\"HKD\",\"buyRate\":6.0572}],\"header\":{\"apiCode\":\"889101\",\"serviceCode\":\"889101\",\"walletShortCode\":\"2020\",\"timestamp\":**********,\"channelCode\":\"test_1\"},\"error\":{},\"extraFields\":\"{\\\"integrationLogs\\\":[{\\\"logId\\\":\\\"c9551876-20f8-4607-a788-eae31724f2a2\\\",\\\"stackId\\\":\\\"b2c9df8f-0d56-4daa-a13c-dd3feab42de1\\\",\\\"request\\\":\\\"{\\\\\\\"baseCurrency\\\\\\\":\\\\\\\"NGN\\\\\\\"}\\\",\\\"requestDate\\\":1716281225565,\\\"response\\\":\\\"{\\\\r\\\\n\\\\t\\\\\\\"disclaimer\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/terms\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"license\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/license\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"timestamp\\\\\\\": **********,\\\\r\\\\n\\\\t\\\\\\\"base\\\\\\\": \\\\\\\"NGN\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"rates\\\\\\\": [\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"USD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 47.02,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 47.12\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"EUR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 50.7064,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 50.8708\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"GBP\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 59.0242,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 59.1921\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CHF\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 51.7386,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 51.9\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"SAR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 12.4755,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 12.5647\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"KWD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 148.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 153.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AFN\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 1.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 1.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"JPY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.4234,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.4291\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"INR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.6382,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.6429\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CNY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.9754,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 7.0038\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AUD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 35.4819,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 35.6278\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CAD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 36.0348,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 36.1655\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"NZD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 32.7103,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 32.8301\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"HKD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.0572,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 6.0847\\\\r\\\\n\\\\t\\\\t}\\\\r\\\\n\\\\t]\\\\r\\\\n}\\\",\\\"responseDate\\\":1716281225758}]}\",\"status\":{\"statusMsg\":\"\",\"errorFlag\":false,\"statusCode\":\"\"}}\n");
        when(connectionUtil.executeSelect(eq("SELECT c.ID,c.CODE,cc.BASE,cc.STATUS FROM CURRENCY c JOIN CURRENCY_CONFIG cc ON c.ID = cc.CURRENCY_ID WHERE c.CODE IS NOT NULL AND cc.BASE != 0 OR cc.STATUS != 0"), anyList()))
                .thenReturn(currencyConfigsList);

        Map<String, Object> response = (Map<String, Object>) viewOrUpdateExchangeRatesBoard.process(request);

        ConcurrentLinkedQueue<Object> rates =(ConcurrentLinkedQueue<Object>) response.get("rates");
        ConcurrentLinkedQueue<String> base = (ConcurrentLinkedQueue<String>) response.get("allowedBase");
        ConcurrentLinkedQueue<String> favorites = (ConcurrentLinkedQueue<String>) response.get("favorites");


        assertEquals(6, rates.size());
        assertEquals(3, base.size());
        assertEquals(0, favorites.size());

    }

    @Test
    public void differentCurrencyRequestedChangeBaseFlagDisabledFailed(){
        payload.put("customerId", CUSTOMER_ID);
        payload.put("currencyId", "149");
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(connectionUtil.executeSelect(eq("SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?"), anyList()))
                .thenReturn(List.of());

        APIException exception = assertThrows(APIException.class, () -> viewOrUpdateExchangeRatesBoard.process(request));

        assertEquals("VAL3546498", exception.getErrorCode());
    }

    @Test
    public void differentCurrencyRequestedChangeBaseFlagEnabledSuccess() throws Exception {
        payload.put("customerId", CUSTOMER_ID);
        payload.put("currencyId", "149");
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("1");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");


        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        Record isDefaultActive = new Record();
        isDefaultActive.put("BASE", "1");

        generalLookup.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        generalLookup.setLookupvalue("1");

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(generalLookupsRepository.findLookupvalueByLookupkey(anyString()))
                .thenReturn(Optional.of(generalLookup));
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(connectionUtil.executeSelect(eq("SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?"), anyList()))
                .thenReturn(List.of(isDefaultActive));
        when(cacheControlService.getCachedObjectOrNull(anyString(), any(Class.class)))
                .thenReturn(null);
        when(propertyLoaderComponent.getPropertyAsLong(eq("viewExchangeExpireCacheEvery")))
                .thenReturn(300L);
        when(connectionUtil.executeSelect(eq("SELECT CODE FROM CURRENCY c WHERE c.ID=?"), anyList()))
                .thenReturn(currencyCodeRecordList);
        when(integrationService.execute(any(), eq("getRates")))
                .thenReturn("{\"payload\":{\"baseCurrency\":\"USD\"},\"rates\":[{\n" +
                        "\t\t\t\"currencyID\": \"NGN\",\n" +
                        "\t\t\t\"buyRate\": 0.02,\n" +
                        "\t\t\t\"sellRate\": 0.021\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"EUR\",\n" +
                        "\t\t\t\"buyRate\": 50.7064,\n" +
                        "\t\t\t\"sellRate\": 50.8708\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"GBP\",\n" +
                        "\t\t\t\"buyRate\": 59.0242,\n" +
                        "\t\t\t\"sellRate\": 59.1921\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"CHF\",\n" +
                        "\t\t\t\"buyRate\": 51.7386,\n" +
                        "\t\t\t\"sellRate\": 51.9\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"SAR\",\n" +
                        "\t\t\t\"buyRate\": 12.4755,\n" +
                        "\t\t\t\"sellRate\": 12.5647\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"KWD\",\n" +
                        "\t\t\t\"buyRate\": 148.3061,\n" +
                        "\t\t\t\"sellRate\": 153.3355\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"AFN\",\n" +
                        "\t\t\t\"buyRate\": 1.3061,\n" +
                        "\t\t\t\"sellRate\": 1.3355\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"JPY\",\n" +
                        "\t\t\t\"buyRate\": 0.4234,\n" +
                        "\t\t\t\"sellRate\": 0.4291\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"INR\",\n" +
                        "\t\t\t\"buyRate\": 0.6382,\n" +
                        "\t\t\t\"sellRate\": 0.6429\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"CNY\",\n" +
                        "\t\t\t\"buyRate\": 6.9754,\n" +
                        "\t\t\t\"sellRate\": 7.0038\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"AUD\",\n" +
                        "\t\t\t\"buyRate\": 35.4819,\n" +
                        "\t\t\t\"sellRate\": 35.6278\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"CAD\",\n" +
                        "\t\t\t\"buyRate\": 36.0348,\n" +
                        "\t\t\t\"sellRate\": 36.1655\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"NZD\",\n" +
                        "\t\t\t\"buyRate\": 32.7103,\n" +
                        "\t\t\t\"sellRate\": 32.8301\n" +
                        "\t\t},\n" +
                        "\t\t{\n" +
                        "\t\t\t\"currencyID\": \"HKD\",\n" +
                        "\t\t\t\"buyRate\": 6.0572,\n" +
                        "\t\t\t\"sellRate\": 6.0847\n" +
                        "\t\t}],\"header\":{\"apiCode\":\"889101\",\"serviceCode\":\"889101\",\"walletShortCode\":\"2020\",\"timestamp\":**********,\"channelCode\":\"test_1\"},\"error\":{},\"extraFields\":\"{\\\"integrationLogs\\\":[{\\\"logId\\\":\\\"c9551876-20f8-4607-a788-eae31724f2a2\\\",\\\"stackId\\\":\\\"b2c9df8f-0d56-4daa-a13c-dd3feab42de1\\\",\\\"request\\\":\\\"{\\\\\\\"baseCurrency\\\\\\\":\\\\\\\"NGN\\\\\\\"}\\\",\\\"requestDate\\\":1716281225565,\\\"response\\\":\\\"{\\\\r\\\\n\\\\t\\\\\\\"disclaimer\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/terms\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"license\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/license\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"timestamp\\\\\\\": **********,\\\\r\\\\n\\\\t\\\\\\\"base\\\\\\\": \\\\\\\"NGN\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"rates\\\\\\\": [\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"USD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 47.02,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 47.12\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"EUR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 50.7064,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 50.8708\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"GBP\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 59.0242,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 59.1921\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CHF\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 51.7386,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 51.9\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"SAR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 12.4755,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 12.5647\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"KWD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 148.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 153.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AFN\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 1.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 1.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"JPY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.4234,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.4291\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"INR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.6382,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.6429\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CNY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.9754,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 7.0038\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AUD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 35.4819,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 35.6278\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CAD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 36.0348,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 36.1655\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"NZD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 32.7103,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 32.8301\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"HKD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.0572,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 6.0847\\\\r\\\\n\\\\t\\\\t}\\\\r\\\\n\\\\t]\\\\r\\\\n}\\\",\\\"responseDate\\\":1716281225758}]}\",\"status\":{\"statusMsg\":\"\",\"errorFlag\":false,\"statusCode\":\"\"}}\n");
        when(connectionUtil.executeSelect(eq("SELECT c.ID,c.CODE,cc.BASE,cc.STATUS FROM CURRENCY c JOIN CURRENCY_CONFIG cc ON c.ID = cc.CURRENCY_ID WHERE c.CODE IS NOT NULL AND cc.BASE != 0 OR cc.STATUS != 0"), anyList()))
                .thenReturn(currencyConfigsList);

        Map<String, Object> response = (Map<String, Object>) viewOrUpdateExchangeRatesBoard.process(request);

        ConcurrentLinkedQueue<Object> rates =(ConcurrentLinkedQueue<Object>) response.get("rates");
        ConcurrentLinkedQueue<String> base = (ConcurrentLinkedQueue<String>) response.get("allowedBase");
        ConcurrentLinkedQueue<String> favorites = (ConcurrentLinkedQueue<String>) response.get("favorites");


        assertEquals(5, rates.size());
        assertEquals(3, base.size());
        assertEquals(0, favorites.size());
    }

    @Test
    public void currencyCodeNotFoundERROR() throws Exception {
        payload.put("currencyId", CURRENCY_ID);
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");


        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(connectionUtil.executeSelect(eq("SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?"), anyList()))
                .thenReturn(List.of());
        when(cacheControlService.getCachedObjectOrNull(anyString(), any(Class.class)))
                .thenReturn(null);
        when(propertyLoaderComponent.getPropertyAsLong(eq("viewExchangeExpireCacheEvery")))
                .thenReturn(300L);
        when(connectionUtil.executeSelect(eq("SELECT CODE FROM CURRENCY c WHERE c.ID=?"), anyList()))
                .thenReturn(List.of());

        APIException exception = assertThrows(APIException.class, () -> viewOrUpdateExchangeRatesBoard.process(request));

        assertEquals("VAL186208043", exception.getErrorCode());

    }



    @Test
    public void testForceUpdateSuccess() throws Exception {
        payload.put("apiToken", APITOKEN);
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);


        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(propertyLoaderComponent.getPropertyAsLong(eq("viewExchangeExpireCacheEvery")))
                .thenReturn(300L);
        when(connectionUtil.executeSelect(eq("SELECT CODE FROM CURRENCY c WHERE c.ID=?"), anyList()))
                .thenReturn(currencyCodeRecordList);
        when(integrationService.execute(any(), eq("getRates")))
                .thenReturn("{\"payload\":{\"baseCurrency\":\"NGN\"},\"rates\":[{\"sellRate\":47.12,\"currencyID\":\"USD\",\"buyRate\":47.02},{\"sellRate\":50.8708,\"currencyID\":\"EUR\",\"buyRate\":50.7064},{\"sellRate\":59.1921,\"currencyID\":\"GBP\",\"buyRate\":59.0242},{\"sellRate\":51.9,\"currencyID\":\"CHF\",\"buyRate\":51.7386},{\"sellRate\":12.5647,\"currencyID\":\"SAR\",\"buyRate\":12.4755},{\"sellRate\":153.3355,\"currencyID\":\"KWD\",\"buyRate\":148.3061},{\"sellRate\":1.3355,\"currencyID\":\"AFN\",\"buyRate\":1.3061},{\"sellRate\":0.4291,\"currencyID\":\"JPY\",\"buyRate\":0.4234},{\"sellRate\":0.6429,\"currencyID\":\"INR\",\"buyRate\":0.6382},{\"sellRate\":7.0038,\"currencyID\":\"CNY\",\"buyRate\":6.9754},{\"sellRate\":35.6278,\"currencyID\":\"AUD\",\"buyRate\":35.4819},{\"sellRate\":36.1655,\"currencyID\":\"CAD\",\"buyRate\":36.0348},{\"sellRate\":32.8301,\"currencyID\":\"NZD\",\"buyRate\":32.7103},{\"sellRate\":6.0847,\"currencyID\":\"HKD\",\"buyRate\":6.0572}],\"header\":{\"apiCode\":\"889101\",\"serviceCode\":\"889101\",\"walletShortCode\":\"2020\",\"timestamp\":**********,\"channelCode\":\"test_1\"},\"error\":{},\"extraFields\":\"{\\\"integrationLogs\\\":[{\\\"logId\\\":\\\"c9551876-20f8-4607-a788-eae31724f2a2\\\",\\\"stackId\\\":\\\"b2c9df8f-0d56-4daa-a13c-dd3feab42de1\\\",\\\"request\\\":\\\"{\\\\\\\"baseCurrency\\\\\\\":\\\\\\\"NGN\\\\\\\"}\\\",\\\"requestDate\\\":1716281225565,\\\"response\\\":\\\"{\\\\r\\\\n\\\\t\\\\\\\"disclaimer\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/terms\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"license\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/license\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"timestamp\\\\\\\": **********,\\\\r\\\\n\\\\t\\\\\\\"base\\\\\\\": \\\\\\\"NGN\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"rates\\\\\\\": [\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"USD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 47.02,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 47.12\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"EUR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 50.7064,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 50.8708\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"GBP\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 59.0242,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 59.1921\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CHF\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 51.7386,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 51.9\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"SAR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 12.4755,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 12.5647\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"KWD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 148.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 153.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AFN\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 1.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 1.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"JPY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.4234,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.4291\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"INR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.6382,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.6429\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CNY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.9754,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 7.0038\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AUD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 35.4819,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 35.6278\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CAD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 36.0348,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 36.1655\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"NZD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 32.7103,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 32.8301\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"HKD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.0572,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 6.0847\\\\r\\\\n\\\\t\\\\t}\\\\r\\\\n\\\\t]\\\\r\\\\n}\\\",\\\"responseDate\\\":1716281225758}]}\",\"status\":{\"statusMsg\":\"\",\"errorFlag\":false,\"statusCode\":\"\"}}\n");

        String response = (String) viewOrUpdateExchangeRatesBoard.process(request);

        assertEquals("SUCCESS", response);


    }

    @Test
    public void testForceUpdateFailed() throws Exception {
        payload.put("apiToken", APITOKEN);
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");
        when(propertyLoaderComponent.getPropertyAsLong(eq("viewExchangeExpireCacheEvery")))
                .thenReturn(300L);
        when(connectionUtil.executeSelect(eq("SELECT CODE FROM CURRENCY c WHERE c.ID=?"), anyList()))
                .thenReturn(currencyCodeRecordList);
        when(integrationService.execute(any(), eq("getRates")))
                .thenReturn("{\"payload\":{\"baseCurrency\":\"NGN\"},\"rates\":null,\"header\":{\"apiCode\":\"889101\",\"serviceCode\":\"889101\",\"walletShortCode\":\"2020\",\"timestamp\":**********,\"channelCode\":\"test_1\"},\"error\":{ \"code\": \"ITG85615\"},\"extraFields\":\"{\\\"integrationLogs\\\":[{\\\"logId\\\":\\\"c9551876-20f8-4607-a788-eae31724f2a2\\\",\\\"stackId\\\":\\\"b2c9df8f-0d56-4daa-a13c-dd3feab42de1\\\",\\\"request\\\":\\\"{\\\\\\\"baseCurrency\\\\\\\":\\\\\\\"NGN\\\\\\\"}\\\",\\\"requestDate\\\":1716281225565,\\\"response\\\":\\\"{\\\\r\\\\n\\\\t\\\\\\\"disclaimer\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/terms\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"license\\\\\\\": \\\\\\\"https:\\/\\/example.org\\/license\\/\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"timestamp\\\\\\\": **********,\\\\r\\\\n\\\\t\\\\\\\"base\\\\\\\": \\\\\\\"NGN\\\\\\\",\\\\r\\\\n\\\\t\\\\\\\"rates\\\\\\\": [\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"USD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 47.02,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 47.12\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"EUR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 50.7064,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 50.8708\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"GBP\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 59.0242,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 59.1921\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CHF\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 51.7386,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 51.9\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"SAR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 12.4755,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 12.5647\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"KWD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 148.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 153.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AFN\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 1.3061,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 1.3355\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"JPY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.4234,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.4291\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"INR\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 0.6382,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 0.6429\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CNY\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.9754,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 7.0038\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"AUD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 35.4819,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 35.6278\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"CAD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 36.0348,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 36.1655\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"NZD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 32.7103,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 32.8301\\\\r\\\\n\\\\t\\\\t},\\\\r\\\\n\\\\t\\\\t{\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"currencyID\\\\\\\": \\\\\\\"HKD\\\\\\\",\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"buyRate\\\\\\\": 6.0572,\\\\r\\\\n\\\\t\\\\t\\\\t\\\\\\\"sellRate\\\\\\\": 6.0847\\\\r\\\\n\\\\t\\\\t}\\\\r\\\\n\\\\t]\\\\r\\\\n}\\\",\\\"responseDate\\\":1716281225758}]}\",\"status\":{\"statusMsg\":\"\",\"errorFlag\":false,\"statusCode\":\"\"}}\n");

        APIException exception = assertThrows(APIException.class, () -> viewOrUpdateExchangeRatesBoard.process(request));

        assertEquals("VAL186208040", exception.getErrorCode());

    }

    @Test
    public void testForceUpdateIncorrectApiTokenFailed() throws Exception {
        payload.put("apiToken", "anyRandomToken");
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        when(propertyLoaderComponent.getPropertyAsString(eq("ViewExchangeNAMPESPACE_PREFIX")))
                .thenReturn("base:currency:");

        APIException exception = assertThrows(APIException.class, () -> viewOrUpdateExchangeRatesBoard.process(request));

        assertEquals("VAL186208044", exception.getErrorCode());
    }

    @Test
    public void testMissingPayloadFieldsFailed() throws Exception {
        message.setHeader(header);

        message.setPayload(payload);

        request.setMessage(message);

        GeneralLookups allowChangeBaseCurrency = new GeneralLookups();
        allowChangeBaseCurrency.setLookupkey("FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY");
        allowChangeBaseCurrency.setLookupvalue("0");

        GeneralLookups fcyDefaultBaseCurrency = new GeneralLookups();
        fcyDefaultBaseCurrency.setLookupkey("FCY_BOARD_DEFAULT_BASE_CURRENCY");
        fcyDefaultBaseCurrency.setLookupvalue("104");

        GeneralLookups fcyDefaultIconBaseURL = new GeneralLookups();
        fcyDefaultIconBaseURL.setLookupkey("FCY_BOARD_ICONS_URL");
        fcyDefaultIconBaseURL.setLookupvalue("http://192.168.4.103/images/icons/");

        GeneralLookups fcyDefaultIconExtension = new GeneralLookups();
        fcyDefaultIconExtension.setLookupkey("FCY_BOARD_ICONS_EXTENSION");
        fcyDefaultIconExtension.setLookupvalue("PNG");

        lookups.add(allowChangeBaseCurrency);
        lookups.add(fcyDefaultBaseCurrency);
        lookups.add(fcyDefaultIconBaseURL);
        lookups.add(fcyDefaultIconExtension);

        when(generalLookupsRepository.findByLookupKeys(anyList()))
                .thenReturn(lookups);
        APIException exception = assertThrows(APIException.class, () -> viewOrUpdateExchangeRatesBoard.process(request));

        assertEquals("VAL186208045", exception.getErrorCode());
    }

}
