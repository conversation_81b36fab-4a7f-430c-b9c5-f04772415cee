package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.model.group.transfers.GroupTransferHistory;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.SalaryDisbursementHistoryComponent")
public class SalaryDisbursementHistoryComponent implements VericashAction {
    @Autowired
    ConnectionUtil connectionUtil;

    @Override
    public Object process(Request request) throws Exception {
        List<Parameter> parameters = new ArrayList<Parameter>();
        List<String> fields = new ArrayList<String>();
        Long groupSetupId = request.getMessage().getPayload().getAttributeAsLong("groupSetupId");
        Long customerId = request.getMessage().getPayload().getAttributeAsLong("customerId");
        String startDate = request.getMessage().getPayload().getAttributeAsString("startDate");
        String endDate = request.getMessage().getPayload().getAttributeAsString("endDate");

        fields = setFields(fields);

        //Returns all salary disbursements list
        if (groupSetupId != null && customerId != null && startDate != null && endDate != null) {
            StringBuilder query = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("SalaryDisbursementHistoryComponent_process",this.getClass()
                    ,String.valueOf(groupSetupId),String.valueOf(customerId),startDate,endDate));
            List<Record> records = connectionUtil.executeSelect(query.toString(), parameters, fields);
            List<GroupTransferHistory> groupTransferSetupList = new ArrayList<>();
            Record record = null;
            GroupTransferHistory groupTransferSetup = null;
            for (int i = 0; i < records.size(); i++) {
                record = records.get(i);
                groupTransferSetup = buildGroupTransferHistory(record);
                groupTransferSetupList.add(groupTransferSetup);
            }
            return groupTransferSetupList;
        } else {
            throw new GeneralFailureException("groupSetupId, customerId, startDate, endDate is mandatory");
        }
    }

    public GroupTransferHistory buildGroupTransferHistory(Record record) throws ParseException {
        GroupTransferHistory groupTransferHistory = new GroupTransferHistory();

        if (record.getValueAsLong("TRANSFER_HISTORY_ID") != null)
            groupTransferHistory.setTransferHistoryID(record.getValueAsLong("TRANSFER_HISTORY_ID"));
        if (record.getValueAsLong("CUSTOMER_ID") != null)
            groupTransferHistory.setCustomerId(record.getValueAsLong("CUSTOMER_ID"));
        if (record.getValueAsDate("TRANSFER_DATE") != null)
            groupTransferHistory.setDate(new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(record.getValueAsDate("TRANSFER_DATE").toString()));
        if (record.getValueAsBigDecimal("TOTAL_FEE") != null)
            groupTransferHistory.setTotalFee(record.getValueAsBigDecimal("TOTAL_FEE"));
        if (record.getValueAsBigDecimal("TOTAL_NUMBER_OF_FAILURE") != null)
            groupTransferHistory.setTotalNumberOfFailure(record.getValueAsBigDecimal("TOTAL_NUMBER_OF_FAILURE").intValue());

        if (record.getValueAsBigDecimal("TOTAL_NUMBER_OF_SUCCESS") != null)
            groupTransferHistory.setTotalNumberOfSuccess(record.getValueAsBigDecimal("TOTAL_NUMBER_OF_SUCCESS").intValue());

        if (record.getValueAsBigDecimal("TOTAL_PAYOUT_AMOUNT") != null)
            groupTransferHistory.setTotalPayoutAmount(record.getValueAsBigDecimal("TOTAL_PAYOUT_AMOUNT"));

        if (record.getValueAsBigDecimal("TOTAL_VAT") != null)
            groupTransferHistory.setTotalVAT(record.getValueAsBigDecimal("TOTAL_VAT"));


        if (record.getValueAsString("NAME") != null)
            groupTransferHistory.setName(record.getValueAsString("NAME"));

        if (record.getValueAsLong("GROUP_SETUP_ID") != null)
            groupTransferHistory.setGroupSetupId(record.getValueAsLong("GROUP_SETUP_ID"));

        if (record.getValueAsBigDecimal("NUM_OF_RECIPIENTS") != null)
            groupTransferHistory.setNumOfRecipients(record.getValueAsBigDecimal("NUM_OF_RECIPIENTS"));

        if (record.getValueAsString("STATUS") != null)
            groupTransferHistory.setStatus(record.getValueAsString("STATUS"));

        if (record.getValueAsLong("SERVICE_LOG_ID") != null)
            groupTransferHistory.setServiceLogId(record.getValueAsLong("SERVICE_LOG_ID"));

        if (record.getValueAsBigDecimal("TOTAL_NUMBER_OF_INPROGRESS") != null)
            groupTransferHistory.setTotalNumberOfInProgress(record.getValueAsBigDecimal("TOTAL_NUMBER_OF_INPROGRESS").intValue());

        if (record.getValueAsString("TRANSFER_SENDER_ACCOUNT") != null)
            groupTransferHistory.setTransferSenderAccount(record.getValueAsString("TRANSFER_SENDER_ACCOUNT"));

        return groupTransferHistory;
    }

    public List<String> setFields(List<String> fields) {
        fields.add("TRANSFER_HISTORY_ID");
        fields.add("CUSTOMER_ID");
        fields.add("TRANSFER_DATE");
        fields.add("TOTAL_FEE");
        fields.add("TOTAL_NUMBER_OF_FAILURE");
        fields.add("TOTAL_NUMBER_OF_SUCCESS");
        fields.add("TOTAL_PAYOUT_AMOUNT");
        fields.add("TOTAL_VAT");
        fields.add("NAME");
        fields.add("GROUP_SETUP_ID");
        fields.add("NUM_OF_RECIPIENTS");
        fields.add("STATUS");
        fields.add("SERVICE_LOG_ID");
        fields.add("TOTAL_NUMBER_OF_INPROGRESS");
        fields.add("TRANSFER_SENDER_ACCOUNT");
        return fields;
    }
}
