package com.cit.vericash.apis.addnewsalaryreceiver;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.addnewsalaryreceive.AddNewSalaryReceiverRequestTransformer")
public class AddNewSalaryReceiverRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    private final String setupGroupTransferId = "setupGroupTransferId";
    private CacheableFieldParameters cacheableFieldParameters;

    public AddNewSalaryReceiverRequestTransformer(CacheableFieldParameters cacheableFieldParameters) {
        this.cacheableFieldParameters = cacheableFieldParameters;
    }

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        try {
//            //String groupIdKey = cacheableFieldParameters.getRequestParameters(setupGroupTransferId);
//            Long groupId = message.getPayload().getAttributeAsLong("groupId");
//            ObjectMapper mapper = new ObjectMapper();
//            HashMap group = (HashMap) message.getPayload().getAttribute("groupTransfer");
//            GroupTransferDTO grouptTransfer = mapper.convertValue(group, new TypeReference<GroupTransferDTO>() {});
//            List<SetupGroupTransferDTO> myGroupTransfers = new ArrayList<SetupGroupTransferDTO>();
//            SetupGroupTransferDTO setupGroupTransferDTO = new SetupGroupTransferDTO();
//            setupGroupTransferDTO.setId(groupId);
//            myGroupTransfers.add(setupGroupTransferDTO);
//            message.getPayload().put("myGroupTransfersList", myGroupTransfers);
        } catch (Exception e) {
            throw e;
        }
    }
}
