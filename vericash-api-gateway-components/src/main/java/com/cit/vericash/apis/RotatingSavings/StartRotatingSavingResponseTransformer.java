package com.cit.vericash.apis.RotatingSavings;


import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.StartRotatingSavingResponseTransformer")
public class StartRotatingSavingResponseTransformer implements MuleResponseTransformer {
    Map<String, Object> result = new HashMap<String, Object>();

    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {

        return null;
    }
}