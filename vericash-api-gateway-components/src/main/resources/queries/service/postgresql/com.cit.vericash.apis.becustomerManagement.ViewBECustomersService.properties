getQuery=SELECT * FROM ( SELECT bca.FIRST_NAME, \
   bca.MIDDLE_NAME, \
   bca.LAST_NAME, \
   bc.ID ,\
   bcs.NAME AS becustomerStatusName , \
   bcs.ID AS Becustomeractivitystatus , \
   as2.NAME AS approvalstatus, \
   as2.ID AS approvalstatusId , \
   bcs.NAME , c.PROFILE_PICTURE_LINK ,\
   BR.NAME AS ROLE , \
   bca.TASK_ID AS TASK_ID , \
   bca.ROLE_ID AS ROLE_ID , \
   at2.TASK_DATE , \
   ROW_NUMBER () OVER ( order BY at2.TASK_DATE ) rn \
   FROM BE_CUSTOMER_ACTIVITY bca \
   LEFT JOIN BE_CUSTOMER bc ON bca.BE_CUSTOMER_ID = bc.ID \
   JOIN ACTIVITY_TRAIL at2 ON at2.TASK_ID = bca.TASK_ID \
   JOIN APPROVAL_STATUS as2 ON at2.STATUS = as2.ID \
   LEFT JOIN BE_CUSTOMER_STATUS bcs ON bcs.ID = bc.STATUS \
   LEFT JOIN CUSTOMER c ON bca.BE_CUSTOMER_ID = c.BE_CUSTOMER_ID \
   JOIN BE_ROLE BR ON bca.ROLE_ID = BR.ID \
   WHERE bca.CORPORATE_ID = ?

getQuery_name_condition= and bca.FIRST_NAME = ?
getQuery_role_condition= and bca.FIRST_NAME = ?
getQuery_status_condition= and bca.FIRST_NAME = ?
getQuery_after_condition=) WHERE rn BETWEEN %1$s AND %2$s

getQuery_pendingApproval_condition= SELECT * FROM \
   ( SELECT bca.FIRST_NAME, \
   bca.MIDDLE_NAME, \
   bca.LAST_NAME, \
   bc.ID, \
   bcs.NAME AS becustomerStatusName ,\
   bcs.ID AS Becustomeractivitystatus ,\
   as2.NAME AS approvalstatus,\
   as2.ID AS approvalstatusId ,\
   bcs.NAME ,\
   bca.ROLE_ID AS ROLE_ID , \
   c.PROFILE_PICTURE_LINK,\
   BR.NAME AS ROLE , \
   bca.TASK_ID AS TASK_ID , \
   at2.TASK_DATE , \
   ROW_NUMBER () OVER ( ORDER BY at2.TASK_DATE )rn \
   FROM BE_CUSTOMER_ACTIVITY bca \
   LEFT JOIN BE_CUSTOMER bc ON bca.BE_CUSTOMER_ID = bc.ID \
   JOIN ACTIVITY_TRAIL at2 ON at2.TASK_ID = bca.TASK_ID \
   JOIN APPROVAL_STATUS as2 ON at2.STATUS = as2.ID \
   LEFT JOIN BE_CUSTOMER_STATUS bcs ON bcs.ID = bc.STATUS \
   LEFT JOIN APPROVED_BY ab ON at2.GROUP_ID = ab.GROUP_ID \
   LEFT JOIN CUSTOMER c ON bca.BE_CUSTOMER_ID = c.BE_CUSTOMER_ID \
   JOIN BE_ROLE BR ON bca.ROLE_ID = BR.ID \
   WHERE bca.CORPORATE_ID = ? \
   AND at2.STATUS = ? \
   AND ab.USER_ID= ? \
   AND ab.APPROVAL_STATUS = ? \
   GROUP BY bca.FIRST_NAME, at2.TASK_DATE,bca.MIDDLE_NAME, bca.LAST_NAME,bca.ROLE_ID, bc.ID , bca.TASK_ID, bcs.NAME , bcs.ID , as2.NAME, as2.ID , bcs.NAME, c.PROFILE_PICTURE_LINK , BR.NAME )

isHome_condition= WHERE rn BETWEEN 1  AND %1$s
else_condition= WHERE rn BETWEEN %1$s AND %2$s