getSupportedDestinationPaymentMethods=SELECT PM.NAME, PM.ID, PM.TYPE,pt.NAME AS TYPENAME , PM.DESTINATION_GROUP_ID, DG.DESTINATION_GROUP_NAME  \
   , ps.ID PARAMETER_CODE,ps.PARAMETER_NAME, ps.DATATYPE, ps.MANDATORY, ps.MINIMUM_LENGTH, ps.MAXIMUM_LENGTH, ps.EXACT_LENGTH, ps.DEFAULT_VALUE, ps.MINIMUM_VALUE, ps.MAXIMUM_VALUE, ps.VALUE_LIST, ps.IS_UNIQUE, ps.FORMAT,pc.GROUP_ID,pc.GROUP_NAME ,ps.PARAM_ORDER ,ps.DECIMAL_PLACES \
   FROM DYNAMIC_PARAMETER_GROUP pc  \
   JOIN PAYMENT_METHOD PM ON PM.DYNAMIC_PARAMETER_GROUP_ID = pc.GROUP_ID  \
   LEFT JOIN DESTINATION_GROUP DG ON PM.DESTINATION_GROUP_ID = DG.DESTINATION_GROUP_ID \
  JOIN PAYMENT_METHOD_TYPE pt ON PM.TYPE =  pt.ID  \
  JOIN DYNAMIC_PARAMETERS ps ON ps. DYNAMIC_PARAMETER_GROUP_ID = pc.GROUP_ID  \
  JOIN DEST_PAYMENT_METHOD_CUST_PROF pcg ON PM.ID = pcg.PAYMENT_METHOD_ID  \
  JOIN CUSTOMER C ON C.CUSTOMER_TYPE_ID=pcg.CUSTOMER_TYPE_ID  \
  WHERE C.USER_ID = ?