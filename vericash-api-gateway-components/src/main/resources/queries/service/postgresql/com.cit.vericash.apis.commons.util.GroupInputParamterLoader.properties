loadGroupInputParamters=SELECT IP.LABEL, IPV.DATATYPE, IPV.DECIMAL_PLACES, IPV.DEFAULT_VALUE, IPV.EXACT_LENGTH, IPV.IDENTIFIER, IPV.IS_ACCOUNT_IDENTIFIER, \
  IPV.IS_MANDATORY, IPV.IS_MASKED, IPV.IS_STORABLE, IPV.IS_UNIQUE, IPV.IS_UPDATEABLE, IPV.IS_VIEWABLE, IPV.MASK_PATTERN, IPV.MASKED_ON_STORE,\
  \ IPV.MASKED_ON_VIEW, IPV.MAX_LENGTH, IPV.MAX_VALUE, IPV.MIN_LENGTH, IPV.MIN_VALUE, IPV.REGEX_PATTERN, IPV.SOURCE, IPV.VALUE_LIST, IPVD.DATATYPE AS DATATYPE_DF, \
  IPVD.DECIMAL_PLACES AS DECIMAL_PLACES_DF, IPVD.DEFAULT_VALUE AS DEFAULT_VALUE_DF, IPVD.EXACT_LENGTH AS EXACT_LENGTH_DF , IPVD.IDENTIFIER AS IDENTIFIER_DF, \
  IPVD.IS_ACCOUNT_IDENTIFIER AS IS_ACCOUNT_IDENTIFIER_DF, IPVD.IS_MANDATORY AS IS_MANDATORY_DF, IPVD.IS_MASKED AS IS_MASKED_DF, IPVD.IS_STORABLE AS IS_STORABLE_DF , \
  IPVD.IS_UNIQUE AS IS_UNIQUE_DF, IPVD.IS_UPDATEABLE AS IS_UPDATEABLE_DF, IPVD.IS_VIEWABLE AS IS_VIEWABLE_DF, IPVD.MASK_PATTERN AS MASK_PATTERN_DF, \
  IPVD.MASKED_ON_STORE AS MASKED_ON_STORE_DF, IPVD.MASKED_ON_VIEW AS MASKED_ON_VIEW_DF, IPVD.MAX_LENGTH AS MAX_LENGTH_DF, IPVD.MAX_VALUE AS MAX_VALUE_DF,\
  \ IPVD.MIN_LENGTH AS MIN_LENGTH_DF, IPVD.MIN_VALUE AS MIN_VALUE_DF, IPVD.REGEX_PATTERN AS REGEX_PATTERN_DF , IPVD.SOURCE AS SOURCE_DF, IPVD.VALUE_LIST AS VALUE_LIST_DF, \
  IPD.DEFAULT_VALIDATION_ID, IP.VALIDATION_ID, IPD.CODE, IPD.NAME \
  FROM INPUT_PARAM_GROUP_WALLET IW \
  LEFT JOIN INPUT_PARAM_GROUP_DEF GD ON IW.GROUP_DEF_ID = GD.ID \
  LEFT JOIN INPUT_PARAMS_GROUPS IPG ON IPG.GROUP_WALLET_ID = IW.ID \
  LEFT JOIN INPUT_PARAMS IP ON IP.ID = IPG.INPUT_PARAM_ID \
  LEFT JOIN INPUT_PARAM_DICTIONARY IPD ON IPD.ID = IP.DICTIONARY_ID \
  LEFT JOIN INPUT_PARAM_VALIDATION IPV ON IPV.ID = IP.VALIDATION_ID \
  LEFT JOIN INPUT_PARAM_VALIDATION IPVD ON IPVD.ID = IPD.DEFAULT_VALIDATION_ID \
  WHERE GD.GROUP_CODE = %1$s AND IW.WALLET_SHORT_CODE = %2$s