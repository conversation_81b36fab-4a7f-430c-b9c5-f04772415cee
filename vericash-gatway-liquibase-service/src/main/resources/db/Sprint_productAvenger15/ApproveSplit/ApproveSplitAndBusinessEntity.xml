<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet failOnError="false" author="Abubakr" id="add TRANSACTION_TYPE for Wallet _To _InternalBE">
        <sql>
            INSERT INTO TRANSACTION_TYPE (TRANSACTION_TYPE_ID, IS_GENERIC_SERVICE, TRANSACTION_TYPE_NAME, ORGANIZATION_ID, TRANSACTION_TYPE_CODE, CODE, ORGANIZATION) VALUES(1187, 0, 'Wallet _To _InternalBE', NULL, 'Wallet _To _InternalBE', 'InternalBE_TO_BusinessEntity', NULL);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="add TRANSACTION_TYPE for External _To _InternalBE">
        <sql>
            INSERT INTO TRANSACTION_TYPE (TRANSACTION_TYPE_ID, IS_GENERIC_SERVICE, TRANSACTION_TYPE_NAME, ORGANIZATION_ID, TRANSACTION_TYPE_CODE, CODE, ORGANIZATION) VALUES(1188, 0, 'Wallet _To _InternalBE', NULL, 'Wallet _To _InternalBE', 'InternalBE_TO_BusinessEntity', NULL);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Abubakr" id="add TRANS_DEF_SUMMARY for Wallet _To _InternalBE">
        <sql>
            INSERT INTO TRANS_DEF_SUMMARY (TRANS_DEF_SUMMARY_ID, DEFAULT_DEFINITION, IS_MULTI_WALLET, TRANSACTION_NAME, ORGANIZATION_ID, BUSINESS_ENTITY_ID, TRANSACTION_TYPE_ID) VALUES(********, 0, 0, 'Wallet _To _InternalBE', NULL, 2421, 1187);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="add TRANS_DEF_SUMMARY for External _To _InternalBE">
        <sql>
            INSERT INTO TRANS_DEF_SUMMARY (TRANS_DEF_SUMMARY_ID, DEFAULT_DEFINITION, IS_MULTI_WALLET, TRANSACTION_NAME, ORGANIZATION_ID, BUSINESS_ENTITY_ID, TRANSACTION_TYPE_ID) VALUES(********, 0, 0, 'External_To _InternalBE', NULL, 2421, 1188);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Abubakr" id="add TRANS_DEF_STEP for Wallet _To _InternalBE">
        <sql>
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES((select max(TRANS_DEF_STEP_ID) +1 from TRANS_DEF_STEP), 0, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, NULL, NULL, 0, NULL, 0, 0, NULL, 0, 1, NULL, 1, NULL, NULL, NULL, 2, ********, NULL, 1);
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES((select max(TRANS_DEF_STEP_ID) +1 from TRANS_DEF_STEP), 0, NULL, NULL, 0, 1, 1, 0, 0, 0, 2, NULL, NULL, 0, NULL, 0, 0, NULL, 0, 3, NULL, 1, NULL, NULL, NULL, 1, ********, NULL, 0);
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES((select max(TRANS_DEF_STEP_ID) +1 from TRANS_DEF_STEP), 0, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, NULL, NULL, 2, NULL, 0, 0, NULL, 0, 2, NULL, 1, NULL, NULL, NULL, 2, ********, NULL, 1);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Abubakr" id="add TRANS_DEF_STEP for `External_To _InternalBE">
        <sql>
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES((select max(TRANS_DEF_STEP_ID) +1 from TRANS_DEF_STEP), 0, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, NULL, NULL, 0, NULL, 0, 0, NULL, 0, 1, NULL, 1, NULL, NULL, NULL, 2, ********, NULL, 1);
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES((select max(TRANS_DEF_STEP_ID) +1 from TRANS_DEF_STEP), 0, NULL, NULL, 7, 1, 1, 0, 0, 0, 1, NULL, NULL, 2, NULL, 0, 0, NULL, 0, 4, NULL, 1, NULL, NULL, NULL, 2, ********, NULL, 0);
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES((select max(TRANS_DEF_STEP_ID) +1 from TRANS_DEF_STEP), 0, NULL, NULL, 7, 1, 1, 0, 0, 0, 1, NULL, NULL, 0, NULL, 0, 0, NULL, 0, 2, NULL, 1, NULL, NULL, NULL, 2, ********, NULL, 0);
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES((select max(TRANS_DEF_STEP_ID) +1 from TRANS_DEF_STEP), 0, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, NULL, NULL, 2, NULL, 0, 0, NULL, 0, 3, NULL, 1, NULL, NULL, NULL, 2, ********, NULL, 1);
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES((select max(TRANS_DEF_STEP_ID) +1 from TRANS_DEF_STEP), 0, NULL, NULL, 0, 1, 1, 0, 0, 0, 2, NULL, NULL, 0, NULL, 0, 0, NULL, 0, 5, NULL, 1, NULL, NULL, NULL, 1, ********, NULL, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Abubakr" id="add HIERARCHY_TYPE for Split Type">
        <sql>
            INSERT INTO HIERARCHY_TYPE (HIERARCHY_TYPE_ID, HIERARCHY_TYPE_NAME, IS_PUBLIC_TYPE, "TYPE", PARENT_HIERARCHY_TYPE) VALUES(1001, 'SplitHolder', 1, 2, NULL);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="add BUSINESS_ENTITY_HIERARCHY for Split">
        <sql>
            INSERT INTO BUSINESS_ENTITY_HIERARCHY (BUSINESS_ENTITY_ID, AGENCY_STATUS, BLOCKING_CLOSED_REASON, CITY, STREET, ZIPCODE, BUSINESS_ENTITY_NAME, BUSINESS_ENTITY_STATUS, CLOSED_DATE, CONTACT_PERSON_EMAIL, CONTACT_PERSON_NAME, CONTACT_PERSON_PHONE, CORPORATE_NAME, CREATION_DATE, HIERARCHY_LEVEL, HUB, LAST_ACCESS_DATE, LAST_MODIFIED_DATE, LOGO_URL, ENTITY_PUBLIC_ID, REGISTRATION_NUMBER, REP_BANK_ACCOUNT, REP_ENABLED, SHORT_CODE, "TYPE", AGENT_TYPE_ID, BANK_ID, BANK_TRANSFER_CONFIG_ID, BUSINESS_HIERARCHY_TYPE, COMMISSION_PROFILE_ID, COUNTRY, CREATED_BY, DISBURSEMENTCONFIG, FEE_PROFILE_ID, ORGANIZATION_ID, PARENT_BUSINESS_ENTITY, REGION_ID, RISK_PROFILE_ID, USERS_GROUP_ID, WALLET, CBJ_STATUS, EXTRA_INFO, ADDRESS1, ADDRESS2, ADDRESS3, CURRENCY, "LANGUAGE", INSTITUTION, WALLET_PROFILE_ID, SOL_ID, FEEPROFILE, COMM_ROLLUP_SCHEDULER_PATTERN, STATE_ID, COMMISSION_DISTRIBUTION_ID, PARENT_CUSTOMER_ID, BE_MODEL, ALLOW_COMMISSION, COMMISSION_TYPE_ID, VOUCHER_PARTNER, IMAGE_URL, PARTNER_DESCRIPTION) VALUES(********, 0, NULL, 'Congo Street Vericash', 'Vericash Free Zone', '11381', 'Split', 1, NULL, '<EMAIL>', 'Shimaa El Sawy', '+*************', 'Split', TIMESTAMP '2024-07-22 12:40:06.000000', 1, 0, NULL, TIMESTAMP '2024-07-22 12:40:06.000000', NULL, NULL, '12020', NULL, 0, NULL, 2, NULL, NULL, NULL, 1001, NULL, 49, NULL, NULL, ********, NULL, 2421, NULL, ********, NULL, 2421, NULL, NULL, NULL, NULL, NULL, 162, NULL, 0, NULL, NULL, NULL, 3, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Abubakr" id="add SERVICE for Split">
        <sql>
            INSERT INTO SERVICE (ID, DESCRIPTION, IS_MIGRATED, SERVICE_LEVEL, NAME, OWNER_SERVICE, SERVICE_REVERSAL_ID, SERVICE_STACK_ID, SERVICE_STACK_TYPE, CATEGORY_ID, FLOW_MIGRATION_FLAG, CLIENT_SERVICE_NAME, DESTINATION, SUPPORTED_CHANNELS) VALUES(166565657780022266, 'executeApproveAllMoneyRequest', 1, 0, 'executeApproveAllMoneyRequest', NULL, NULL, 'executeApproveAllMoneyRequest', 0, NULL, 1, NULL, NULL, 'ALL');
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Abubakr" id="add BUSINESS_SERVICE_STEP for Split">
        <sql>
            INSERT INTO BUSINESS_SERVICE_STEP (ID, DESCRIPTION, IS_MIGRATED, STEP_ORDER, "TYPE", BUSINESS_SERVICE_CONFIG_ID, SERVICE, TRANSACTION_DEF) VALUES((select max(ID) +1 from BUSINESS_SERVICE_STEP), 'executeApproveAllMoneyRequest', 0, 14, 1, ***************, 166565657780022266, NULL);
        </sql>
    </changeSet>


    <changeSet failOnError="false" author="Abubakr" id="add AMOUNT_TYPE for ShadowAccountFees">
        <sql>
            INSERT INTO AMOUNT_TYPE (ID, AMOUNT_TYPE) VALUES(8, 'ShadowAccountFees');
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Abubakr" id="Update ACCOUNT_OWNER_TYPE">
        <sql>
            UPDATE ACCOUNT_OWNER_TYPE SET ACCOUNT_OWNER_TYPE='Sender' WHERE ACCOUNT_OWNER_TYPE_ID=1;
            UPDATE ACCOUNT_OWNER_TYPE SET ACCOUNT_OWNER_TYPE='Receiver' WHERE ACCOUNT_OWNER_TYPE_ID=2;
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="add SERVICE_CONFIG_MAP for `Approve Split">
        <sql>
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Money Request - Approve Split - From Wallet by Customer', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Money Request - Approve Split - From CIB by Customer', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Money Request - Approve Split - From Prepaid by Customer', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Money Request - Approve Split - From VC by Customer', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Money Request - Approve Split - From Debit by Customer', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="add BUSINESS_SERVICE_CONFIG for `Approve Split">
        <sql>
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES((select max(ID) +1 from BUSINESS_SERVICE_CONFIG), 0, 0, 1, 0, 0, 'Money Request - Approve Split - From Wallet by Customer', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES((select max(ID) +1 from BUSINESS_SERVICE_CONFIG), 0, 0, 1, 0, 0, 'Money Request - Approve Split - From Debit by Customer', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES((select max(ID) +1 from BUSINESS_SERVICE_CONFIG), 0, 0, 1, 0, 0, 'Money Request - Approve Split - From CIB by Customer', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES((select max(ID) +1 from BUSINESS_SERVICE_CONFIG), 0, 0, 1, 0, 0, 'Money Request - Approve Split - From Prepaid by Customer', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES((select max(ID) +1 from BUSINESS_SERVICE_CONFIG), 0, 0, 1, 0, 0, 'Money Request - Approve Split - From VC by Customer', *********, 3, 2421, NULL, 1, NULL, NULL);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="add SERVICE for approve collect money">
        <sql>
            INSERT INTO SERVICE (ID, DESCRIPTION, IS_MIGRATED, SERVICE_LEVEL, NAME, OWNER_SERVICE, SERVICE_REVERSAL_ID, SERVICE_STACK_ID, SERVICE_STACK_TYPE, CATEGORY_ID, FLOW_MIGRATION_FLAG, CLIENT_SERVICE_NAME, DESTINATION, SUPPORTED_CHANNELS) VALUES(166565657780022253, 'UpdateStatusForMoneyRequest', 1, 0, 'UpdateStatusForMoneyRequest', NULL, NULL, 'UpdateStatusForMoneyRequest', 0, NULL, 1, NULL, NULL, 'ALL');
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Abubakr" id="add BUSINESS_SERVICE_STEP for Approve collect money">
        <sql>
            INSERT INTO BUSINESS_SERVICE_STEP (ID, DESCRIPTION, IS_MIGRATED, STEP_ORDER, "TYPE", BUSINESS_SERVICE_CONFIG_ID, SERVICE, TRANSACTION_DEF) VALUES((select max(ID) +1 from BUSINESS_SERVICE_STEP), 'UpdateStatusForMoneyRequest', 0, 15, 1, 137197842877515, 166565657780022253, NULL);
        </sql>
    </changeSet>


</databaseChangeLog>