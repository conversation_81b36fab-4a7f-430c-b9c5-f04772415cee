package com.cit.vericash.apis.payBillPayment;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.paymentmethod.SourcePaymentMethodDTO;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class PayBillPaymentResponseTransformer implements MuleResponseTransformer {
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashedMap();
        ObjectMapper mapper = new ObjectMapper();
        // String balance = businessMessage.getSourcePaymentMethod().getPaymentBalance();
        // result.put("paymentBalance", balance);

        SourcePaymentMethodDTO sourcePaymentMethod = businessMessage.getSourcePaymentMethod();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        sourcePaymentMethod.setDynamicGroup(null);
        //sourcePaymentMethod.setIntegrationType(Enums.IntegrationType.EXTERNAL);
        result.put("sourcePaymentMethod", sourcePaymentMethod);
        return result;
    }

}
