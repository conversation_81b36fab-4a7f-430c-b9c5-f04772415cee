package com.cit.vericash.util;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class WalletTypeUtils {
    private static final String WALLET_BASE="Wallet";
    private static final String DIGITAL_BASE="Digital";
    private static final Integer MONEY_REQUEST_SPLIT_MODE=3;
    public static final String MONEY_REQUEST_TYPE = "moneyRequestType";
    public static final String IS_WALLET_BASE = "isWalletBase";
    public static final String GET_WALLET_TYPE = "getWalletType";
    private final ConnectionUtil connectionUtil;

    public void enrichMessage(String walletShortCode, Message originalMessage) throws GeneralFailureException {
        originalMessage.getPayload().put(MONEY_REQUEST_TYPE, MONEY_REQUEST_SPLIT_MODE);

        String type = getWalletType(walletShortCode);
        if (type.equals(WALLET_BASE) || type.equals(DIGITAL_BASE)) {
            originalMessage.getPayload().put(IS_WALLET_BASE, type.equals(WALLET_BASE));
        }
    }
    public String getWalletType(String shortCode) throws GeneralFailureException {
        String sqlQuery= ServiceQueryEngine.getQueryStringToExecute(GET_WALLET_TYPE,this.getClass(), shortCode);
        List<Parameter> parameters = new ArrayList<Parameter>();
        List<Record> records = connectionUtil.executeSelect(sqlQuery,parameters);

        if (records == null || records.isEmpty()) {
            throw new GeneralFailureException(GeneralFailureException.GENERAL_ERROR,
                    Map.of("error message : ", "no results"));
        }
        if (records.size() > 1) {
            throw new GeneralFailureException(GeneralFailureException.GENERAL_ERROR,
                    Map.of("error message : ", "more than one result"));
        }
        return records.get(0)!=null ? records.get(0).getValueAsString("TYPE"):"";
    }
}
