<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet failOnError="false" author="Abubakr" id="add LOOKUP_FRAMEWORK_CONFIG Record for GetMoneyRequestMonthsAndStatusLookup">
      <sql>
      INSERT INTO LOOKUP_FRAMEWORK_CONFIG (ID, CACHING_KEY_PATTERN, CACHING_TYPE, IS_CACHED_LOOKUP, LOOKUP_NAME, LOOKUP_TYPE, RESPONSE_TRANSFORMER_MAPPING, CUSTOME_RESPONSE_TRANSFORMER, CUSTOM_TRANSFORMER_CONFIG_FILE, ENDPOINT_NAME, SERVICE_NAME) VALUES((select max(ID) +1 from LOOKUP_FRAMEWORK_CONFIG), '0', 0, 0, 'moneyRequest.get.status', 0, NULL, '', NULL, NULL, NULL);
      INSERT INTO LOOKUP_FRAMEWORK_CONFIG (ID, CACHING_KEY_PATTERN, CACHING_TYPE, IS_CACHED_LOOKUP, LOOKUP_NAME, LOOKUP_TYPE, RESPONSE_TRANSFORMER_MAPPING, CUSTOME_RESPONSE_TRANSFORMER, CUSTOM_TRANSFORMER_CONFIG_FILE, ENDPOINT_NAME, SERVICE_NAME) VALUES((select max(ID) +1 from LOOKUP_FRAMEWORK_CONFIG), '0', 0, 0, 'moneyRequest.get.months', 0, NULL, 'moneyRequestGetMonths', NULL, NULL, NULL);
		</sql>
    </changeSet>
	
	<changeSet failOnError="false" author="Abubakr" id="add PORTAL_LOOKUPS_CONFIG Record for GetMoneyRequestMonthsAndStatusLookup">
		<sql>
        	INSERT INTO PORTAL_LOOKUPS_CONFIG (ID, LOOKUP_NAME, LOOKUP_KEY, LOOKUP_QUERY, CACHEABLE) VALUES((select max(ID) +1 from PORTAL_LOOKUPS_CONFIG), 'Request Money View Status', 'moneyRequest.get.status', 'select ID ,STATUS_NAME  from MONEY_REQUEST_STATUS', 0);
      		INSERT INTO PORTAL_LOOKUPS_CONFIG (ID, LOOKUP_NAME, LOOKUP_KEY, LOOKUP_QUERY, CACHEABLE) VALUES((select max(ID) +1 from PORTAL_LOOKUPS_CONFIG), 'Request Money View Months', 'moneyRequest.get.months', 'select DISPLAY_REQUEST_HISTORY  from MONEY_REQUEST_ADMIN_CONFIG mrac  where MRAC ."TYPE" =''${filters.getAttributeAsLong(''moneyRequestTypeId'')}''', 0);
		</sql>
	</changeSet>


</databaseChangeLog>