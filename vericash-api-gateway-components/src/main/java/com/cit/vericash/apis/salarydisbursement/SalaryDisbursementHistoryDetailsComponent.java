package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.model.group.transfers.GroupTransferHistoryDetail;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.SalaryDisbursementHistoryDetailsComponent")
public class SalaryDisbursementHistoryDetailsComponent implements VericashAction {
    @Autowired
    ConnectionUtil connectionUtil;
    @Autowired
    SalaryDisbursementHistoryComponent salaryDisbursementHistoryComponent;
    List<Parameter> parameters = new ArrayList<Parameter>();
    List<String> fields = new ArrayList<String>();

    @Override
    public Object process(Request request) throws Exception {

        Long transferHistoryId = request.getMessage().getPayload().getAttributeAsLong("transferHistoryId");

        fields = setFields(fields);

        //Returns all salary disbursements list
        if (transferHistoryId != null) {
            StringBuilder query = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("SalaryDisbursementHistoryDetailsComponent_process",this.getClass(),String.valueOf(transferHistoryId)));
            List<Record> records = connectionUtil.executeSelect(query.toString(), parameters, fields);
            List<GroupTransferHistoryDetail> groupTransferHistoryDetailList = new ArrayList<>();
            Record record = null;
            GroupTransferHistoryDetail groupTransferHistoryDetail = null;
            for (int i = 0; i < records.size(); i++) {
                record = records.get(i);
                groupTransferHistoryDetail = buildGroupTransferHistoryDetails(record);
                groupTransferHistoryDetailList.add(groupTransferHistoryDetail);
            }
            return groupTransferHistoryDetail;
        } else {
            throw new GeneralFailureException("transferHistoryId is mandatory");
        }
    }

    private GroupTransferHistoryDetail buildGroupTransferHistoryDetails(Record record) throws ParseException {
        GroupTransferHistoryDetail groupTransferHistoryDetail = new GroupTransferHistoryDetail();

        if (record.getValueAsLong("DETAIL_ID") != null)
            groupTransferHistoryDetail.setHistoryDetailID(record.getValueAsLong("DETAIL_ID"));
        if (record.getValueAsLong("ACCOUNT_NUMBER") != null)
            groupTransferHistoryDetail.setAccountNumber(record.getValueAsString("ACCOUNT_NUMBER"));

        if (record.getValueAsDate("TRANSFER_DATE") != null)
            groupTransferHistoryDetail.setDate(record.getValueAsDate("TRANSFER_DATE"));

        if (record.getValueAsBigDecimal("AMOUNT") != null)
            groupTransferHistoryDetail.setAmount(record.getValueAsBigDecimal("AMOUNT").doubleValue());
        if (record.getValueAsString("BANK_NAME") != null)
            groupTransferHistoryDetail.setBankName(record.getValueAsString("BANK_NAME"));

        if (record.getValueAsString("BRANCH_NAME") != null)
            groupTransferHistoryDetail.setBranchName(record.getValueAsString("BRANCH_NAME"));

        if (record.getValueAsString("COUNTRY") != null)
            groupTransferHistoryDetail.setCountry(record.getValueAsString("COUNTRY"));

        if (record.getValueAsString("EXECUTION_RESULT") != null)
            groupTransferHistoryDetail.setExecutionResult(record.getValueAsString("EXECUTION_RESULT"));


        if (record.getValueAsString("NAME") != null)
            groupTransferHistoryDetail.setName(record.getValueAsString("NAME"));

        if (record.getValueAsString("FAILLURE_REASON") != null)
            groupTransferHistoryDetail.setFaillureReason(record.getValueAsString("FAILLURE_REASON"));

        if (record.getValueAsString("PREPAID_CARD") != null)
            groupTransferHistoryDetail.setPrepaidCard(record.getValueAsString("PREPAID_CARD"));

        if (record.getValueAsBigDecimal("TRANSFER_HISTORY_ID") != null) {
            StringBuilder query = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("buildGroupTransferHistoryDetails"
                    ,this.getClass(),String.valueOf(record.getValueAsBigDecimal("TRANSFER_HISTORY_ID"))));
            List<String> transferFields = new ArrayList<String>();
            transferFields = salaryDisbursementHistoryComponent.setFields(transferFields);
            List<Record> records = connectionUtil.executeSelect(query.toString(), parameters, transferFields);

            List<GroupTransferHistoryDetail> groupTransferHistoryDetailList = new ArrayList<>();
            Record transferRecord = null;
            if (records != null) {
                transferRecord = records.get(0);
            }
            groupTransferHistoryDetail.setGroupTransferHistory(salaryDisbursementHistoryComponent.buildGroupTransferHistory(transferRecord));
        }

        if (record.getValueAsString("BRANCH_CODE") != null)
            groupTransferHistoryDetail.setBranchCode(record.getValueAsString("BRANCH_CODE"));

        if (record.getValueAsString("CARD_NUMBER") != null)
            groupTransferHistoryDetail.setCardNumber(record.getValueAsString("CARD_NUMBER"));

        if (record.getValueAsString("COUNTRY_NAME") != null)
            groupTransferHistoryDetail.setCountryName(record.getValueAsString("COUNTRY_NAME"));
        if (record.getValueAsString("INSTITUTION_CODE") != null)
            groupTransferHistoryDetail.setInstitutionCode(record.getValueAsString("INSTITUTION_CODE"));
        if (record.getValueAsString("INSTITUTION_NAME") != null)
            groupTransferHistoryDetail.setInstitutionName(record.getValueAsString("INSTITUTION_NAME"));
        if (record.getValueAsString("TRANSFER_TYPE") != null)
            groupTransferHistoryDetail.setTransferType(record.getValueAsString("TRANSFER_TYPE"));
        if (record.getValueAsString("TRANSFER_TYPE_CATEGORY") != null)
            groupTransferHistoryDetail.setTransferTypeCategory(record.getValueAsString("TRANSFER_TYPE_CATEGORY"));
        if (record.getValueAsString("WALLET_SHORT_CODE") != null)
            groupTransferHistoryDetail.setWalletShortCode(record.getValueAsString("WALLET_SHORT_CODE"));

        return groupTransferHistoryDetail;
    }

    private List<String> setFields(List<String> fields) {
        fields.add("DETAIL_ID");
        fields.add("ACCOUNT_NUMBER");
        fields.add("TRANSFER_DATE");
        fields.add("AMOUNT");
        fields.add("BANK_NAME");
        fields.add("BRANCH_NAME");
        fields.add("COUNTRY");
        fields.add("EXECUTION_RESULT");
        fields.add("NAME");
        fields.add("FAILLURE_REASON");
        fields.add("PREPAID_CARD");
        fields.add("TRANSFER_HISTORY_ID");
        fields.add("BRANCH_CODE");
        fields.add("CARD_NUMBER");
        fields.add("COUNTRY_NAME");
        fields.add("INSTITUTION_CODE");
        fields.add("INSTITUTION_NAME");
        fields.add("TRANSFER_TYPE");
        fields.add("TRANSFER_TYPE_CATEGORY");
        fields.add("WALLET_SHORT_CODE");
        return fields;
    }
}
