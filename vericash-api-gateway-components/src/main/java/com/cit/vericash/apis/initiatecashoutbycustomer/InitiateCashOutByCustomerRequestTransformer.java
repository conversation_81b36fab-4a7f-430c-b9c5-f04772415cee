package com.cit.vericash.apis.initiatecashoutbycustomer;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.initiatecashoutbycustomer.InitiateCashOutByCustomerRequestTransformer")
public class InitiateCashOutByCustomerRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    private final String API_CODE = "api_code";
    private final String SERVICE_TYPE = "service_type";
    private final String INTIATED_SERVICE_ONLY = "intaited_Service_Only";
    @Autowired
    private CachableVericashApi vericashApiCachable;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

//    public InitiateCashOutByCustomerRequestTransformer() {
//        vericashApiCachable = new CacheableVericashAPiImpl();
//        cacheableFieldParameters = CacheableFieldParametersImpl.getInstance();
//
//    }

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        try {
            populateServiceTypeForFee(message);
        } catch (Exception e) {
            throw e;
        }
    }


    private void populateServiceTypeForFee(Message message) throws Exception {
        String apiCode = message.getHeader().getAttributeAsString(cacheableFieldParameters.getRequestParameters(API_CODE));
        VericashAPI vericashAPI = vericashApiCachable.getVericashAPI(Long.parseLong(apiCode));
        if (vericashAPI == null) {
            throw new APIException("Unable to load VericashAPI",
                    ErrorCode.UNABLE_TO_LOAD_VERICASH_API.getErrorCode());
        }
        message.getPayload().put(cacheableFieldParameters.getRequestParameters(SERVICE_TYPE), vericashAPI.getServiceCode());
        message.getPayload().put(cacheableFieldParameters.getRequestParameters(INTIATED_SERVICE_ONLY), true);
    }


}