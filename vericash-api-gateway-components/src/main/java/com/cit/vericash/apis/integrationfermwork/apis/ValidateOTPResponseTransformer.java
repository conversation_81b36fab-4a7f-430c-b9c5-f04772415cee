package com.cit.vericash.apis.integrationfermwork.apis;

import com.cit.vericash.apis.commons.api.IntegrationResponseTransformer;
import com.cit.vericash.apis.commons.exceptions.IntegrationException;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.integrationfermwork.apis.ValidateOTPResponseTransformer")

public class ValidateOTPResponseTransformer implements IntegrationResponseTransformer {
    private final String statusCode="API_0551";
    private final String statusMsg="Invalid OTP Authentication";
    @Override
    public Object transform(Payload payload) throws Exception {
        Map<String, Object> result = new HashedMap();

        if(payload.get("response")!=null)
        {

            Map integ_response=(HashMap<String ,Object>) payload.get("response");

            result.put("ValidateOtp", integ_response);

            boolean flag= (boolean) integ_response.get("flag");

            if (!flag)
                throw new IntegrationException(statusCode,statusMsg);

        }
        return result ;

    }
}
