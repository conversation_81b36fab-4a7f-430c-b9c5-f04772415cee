package com.cit.vericash.util;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class LanguageUtil {

    public static final String GET_ALL_LANGUAGE = "getAllLanguage";
    public static final String GET_ALL_ANOTHER_LANGUAGE = "getAllAnotherLanguage";
    private final ConnectionUtil connectionUtil;
    private Logger log = LoggerFactory.getLogger(LanguageUtil.class);

    public LanguageUtil(ConnectionUtil connectionUtil) {
        this.connectionUtil = connectionUtil;
    }

    public void validateOnSupportedLanguage(String language){
        String query = ServiceQueryEngine.getQueryStringToExecute(GET_ALL_LANGUAGE, this.getClass());
        List<Parameter> parameters = new ArrayList<>();
        List<Record> records = connectionUtil.executeSelect(query, parameters);
        if (records != null && !records.isEmpty()) {
            boolean isSupported = records.stream()
                    .map(record -> record.getValueAsString("LANGUAGE_NAME"))
                    .anyMatch(lang -> lang.equals(language));

            if (!isSupported) {
                throw new APIException("Invalid Language: " + language,
                        ErrorCode.INVALID_LANGUAGE.getErrorCode());
            }
        } else {
            log.error("Error: No languages found in the Language table");
            throw new IllegalStateException("No languages available for validation.");
        }

    }

    public boolean isAnotherLanguage(String language){
        String query = ServiceQueryEngine.getQueryStringToExecute(GET_ALL_ANOTHER_LANGUAGE, this.getClass());
        List<Parameter> parameters = new ArrayList<>();
        List<Record> records = connectionUtil.executeSelect(query, parameters);
        if (records != null && !records.isEmpty()) {
            return records.stream()
                    .map(record -> record.getValueAsString("LANGUAGE_NAME"))
                    .anyMatch(lang -> lang.equals(language));
        } else {
            return false;
        }
    }




}
