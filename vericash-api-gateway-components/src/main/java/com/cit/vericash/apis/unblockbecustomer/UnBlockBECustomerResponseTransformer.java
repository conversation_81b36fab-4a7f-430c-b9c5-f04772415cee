package com.cit.vericash.apis.unblockbecustomer;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.unblockbecustomer.UnBlockBECustomerRequestTransformer")
public class UnBlockBECustomerResponseTransformer implements MuleResponseTransformer {

    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        return null;
    }
}
