package com.cit.vericash.apis.validatePaymentMethod;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.paymentmethod.SourcePaymentMethodDTO;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.validatePaymentMethod.validatePaymentMethodResponseTransformer")
public class validatePaymentMethodResponseTransformer implements MuleResponseTransformer {


    Map<String, Object> result = new HashMap<String, Object>();
    ObjectMapper mapper = new ObjectMapper();

    public Object transform(BusinessMessage businessMessage) throws Exception {

        SourcePaymentMethodDTO paymentMethodDTO = businessMessage.getSourcePaymentMethod();

        String parameters = mapper.writeValueAsString(paymentMethodDTO.getDynamicGroup().getInputParameters());
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        result.put("paymentMethodName", paymentMethodDTO.getPaymentMethodName());
        result.put("paymentMethodType", paymentMethodDTO.getPaymentMethodType());
        result.put("parameters", parameters);
        return result;
    }
}
