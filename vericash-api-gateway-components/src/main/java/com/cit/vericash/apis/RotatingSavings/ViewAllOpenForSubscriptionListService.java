package com.cit.vericash.apis.RotatingSavings;

import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.ViewAllOpenForSubscriptionListService")
public class ViewAllOpenForSubscriptionListService implements VericashAction {
    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    @Autowired
    OpenForSubscriptionListService openForSubscriptionListService;


    @Override
    public Object process(Request request) throws Exception {
        Object result = null;
        Integer customerId = request.getMessage().getPayload().getAttributeAsInteger("customerId");
        if (customerId != null) {
            result = openForSubscriptionListService.getOpenForSubscriptionList(true, customerId);
        } else {
            String errorMessage = propertyLoaderComponent.getPropertyAsString("error.rotating.savings.missing.id", "application", "application");
            throw new GeneralFailureException(errorMessage);
        }
        return result;
    }
}
