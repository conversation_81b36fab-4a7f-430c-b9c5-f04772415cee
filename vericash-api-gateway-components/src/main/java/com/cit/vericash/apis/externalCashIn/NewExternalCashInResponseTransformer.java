package com.cit.vericash.apis.externalCashIn;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.model.transaction.TransactionExecutionSummary;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class NewExternalCashInResponseTransformer implements MuleResponseTransformer {

    private final String trxAmountKey = "Transaction-Amount";
    private final String totalFeeAmountKey = "Total-Fee-Amount";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, Object> result = new HashMap<String, Object>();

        try {

            if (businessMessage.getSoftFields().get("TRANS_EXEC_SUMMARY") != null) {
                TransactionExecutionSummary transactionExecutionSummary = (TransactionExecutionSummary) businessMessage
                        .getSoftFields().get("TRANS_EXEC_SUMMARY");
                result.put(cacheableFieldParameters.getResponseParameters(trxAmountKey),
                        transactionExecutionSummary.getTransactionAmount());
                result.put(cacheableFieldParameters.getResponseParameters(totalFeeAmountKey),
                        (transactionExecutionSummary.getTotalFeeAmount() == null) ? 0
                                : transactionExecutionSummary.getTotalFeeAmount());
            }

        } catch (Exception e) {
            throw e;
        }

        return result;
    }

}
