package com.cit.vericash.apis.openBankAccount;

import com.cit.vericash.api.repository.customer.CustomerRepository;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;


@Component
@Qualifier("com.cit.vericash.apis.openBankAccount.validateOpenBankAccountData")
public class ValidateOpenBankAccountData implements VericashAction {
    private final CustomerRepository customerRepository;

    public ValidateOpenBankAccountData(CustomerRepository customerRepository) {
        this.customerRepository = customerRepository;
    }

    @Override
    public Object process(Request request) throws Exception {
        String birthdate = request.getMessage().getPayload().getAttributeAsString("birthDate");
        String msisdn = request.getMessage().getPayload().getAttributeAsString("bankAccountMsisdn");
        Long customerId = request.getMessage().getHeader().getAttributeAsLong("customerId");
        validateInput(msisdn, customerId, birthdate);
        return null;
    }

    private void validateInput(String msisdn, Long customerId, String birthdate) {

        if ((birthdate == null || birthdate.trim().isEmpty()) || (msisdn == null || msisdn.trim().isEmpty())) {
            throw new QuickActionException("birthdate or bvnNum Must be Found",
                    ErrorCode.MISSING_PARAMETER.getErrorCode());
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate birthDateTime = LocalDate.parse(birthdate, formatter);

        Period age = Period.between(birthDateTime, LocalDate.now());

        if (age.getYears() < 18)
        {
            throw new QuickActionException("You are allowed to open an account only after turning to 18 years old",
                    ErrorCode.USER_SMALLER_THAN_18.getErrorCode());
        }

        customerRepository.findByCustomerId(customerId)
                .filter(customer -> customer.getMsisdn().replace("+", "").equals(msisdn.replace("+", "")))
                .orElseThrow(() -> new QuickActionException("Customer Not Found", ErrorCode.CUSTOMER_IS_NOT_FOUND.getErrorCode()));
    }
}
