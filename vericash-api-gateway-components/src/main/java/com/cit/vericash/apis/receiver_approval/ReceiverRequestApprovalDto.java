package com.cit.vericash.apis.receiver_approval;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class ReceiverRequestApprovalDto {
    private long approvalRequestId;
    private LocalDateTime creationDate;
    private LocalDateTime expiryDate;
    private String message;
    private String title;
    private BigDecimal statusCode;
    private String statusName;
    private BigDecimal amount;
    private String senderFullName;
    private String mobileNumber;
    private String serviceName;
    private String correlationId;

}
