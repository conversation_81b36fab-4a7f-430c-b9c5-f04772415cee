<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="update-wallet-info-country-code" author="fatma">
    <comment>Update COUNTRY_CODE to '27' for WALLET_SHORT_CODE '2020' in PRO_MI_NO_C.WALLET_INFO</comment>
    
    <update  tableName="WALLET_INFO">
        <column name="COUNTRY_CODE" value="27"/>
        <where>WALLET_SHORT_CODE='2020'</where>
    </update>
</changeSet>


<changeSet id="insert-lookup-framework-config-auto-id" author="fatma">
 
    
    <insert  tableName="LOOKUP_FRAMEWORK_CONFIG">
        <column name="ID" valueComputed="(SELECT COALESCE(MAX(ID), 0) + 1 FROM LOOKUP_FRAMEWORK_CONFIG)"/>
        <column name="CACHING_KEY_PATTERN" value="0"/>
        <column name="CACHING_TYPE" valueNumeric="0"/>
        <column name="IS_CACHED_LOOKUP" valueNumeric="0"/>
        <column name="LOOKUP_NAME" value="be.sme.model.lookup.list"/>
        <column name="LOOKUP_TYPE" valueNumeric="0"/>
        <column name="RESPONSE_TRANSFORMER_MAPPING" valueComputed="NULL"/>
        <column name="CUSTOME_RESPONSE_TRANSFORMER" valueComputed="NULL"/>
        <column name="CUSTOM_TRANSFORMER_CONFIG_FILE" valueComputed="NULL"/>
        <column name="ENDPOINT_NAME" valueComputed="NULL"/>
        <column name="SERVICE_NAME" valueComputed="NULL"/>
    </insert>
</changeSet>



<changeSet id="insert-user-profile-fixed-id" author="your_name">
    <insert  tableName="USER_PROFILES">
        <column name="ID" valueComputed="(SELECT COALESCE(MAX(ID), 0) + 1 FROM USER_PROFILES)"/>
            <column name="ENABLED" valueNumeric="1"/>
        <column name="IS_SELF_REGISTERED" valueComputed="NULL"/>
        <column name="PORTALPROFILETYPE" valueComputed="NULL"/>
        <column name="PROFILE_NAME" value="Self Register by PIN"/>
        <column name="VIEW_MODE" valueNumeric="0"/>
        <column name="ORGANIZATION_ID" valueNumeric="1"/>
        <column name="PROFILE_TYPE_ID" valueNumeric="1"/>
        <column name="BUSINESS_ENTITY_ID" valueNumeric="2421"/>
        <column name="CUSTOMER_REGISTRATION_TYPE" valueNumeric="2"/>
        <column name="BUSINESSUSERCATEGORY" valueNumeric="4"/>
        <column name="BUSINESSUSERHIERARCHYTYPE" valueNumeric="3"/>
        <column name="ENABLE_TOKEN" valueNumeric="0"/>
    </insert>
</changeSet>


</databaseChangeLog>
