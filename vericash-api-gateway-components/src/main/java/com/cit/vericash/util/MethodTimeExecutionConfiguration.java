package com.cit.vericash.util;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.aop.support.Pointcuts;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;

@Aspect
@Configuration
public class MethodTimeExecutionConfiguration extends Pointcuts {

    private class MethodExecutionTime implements Comparable
    {
        private String className;
        private String method;
        private double elapsedTime;

        public MethodExecutionTime(String className, String method, double elapsedTime) {
            this.className = className;
            this.method = method;
            this.elapsedTime = elapsedTime;
        }

        public String getClassName() {
            return className;
        }

        public void setClassName(String className) {
            this.className = className;
        }

        public String getMethod() {
            return method;
        }

        public void setMethod(String method) {
            this.method = method;
        }

        public double getElapsedTimeInMilliSeconds() {
            return elapsedTime;
        }

        public void setElapsedTime(double elapsedTime) {
            this.elapsedTime = elapsedTime;
        }

        @Override
        public int compareTo(Object o) {
            return (int)(this.elapsedTime-((MethodExecutionTime)o).elapsedTime);
        }
    }
    @After("execution(* com.cit.vericash.api.controller.VericashBackendController.*(..))")
    public void after()
    {
        Collections.sort(methodExecutionTimesList);
        StringBuilder html=new StringBuilder("<html><font size=\"1\" face=\"Arial\" ><table border='1' cellspacing='0' cellpadding='0' style=\"width:100%;border:1px solid lightgray;line-height: 30px\"><tr style='background-color:lightblue'><td>&nbsp;<b>Class</b></td><td>&nbsp;<b>Method</b></td><td>&nbsp;<b>Elapsed Time (seconds)</b></td></tr>");
        for (int i = 0; i <methodExecutionTimesList.size() ; i++) {
            MethodExecutionTime methodExecutionTime=methodExecutionTimesList.get(i);
            String className=methodExecutionTime.getClassName();
            String methodName=methodExecutionTime.getMethod();
            double elapsedTimeInMilliSeconds=methodExecutionTime.getElapsedTimeInMilliSeconds();
            if(elapsedTimeInMilliSeconds>=100) {
                html.append("<tr><td>&nbsp;" + className + "</td><td>&nbsp;" + methodName + "</td><td>&nbsp;" + (elapsedTimeInMilliSeconds/1000d) + "</td></tr>");
            }
        }
        methodExecutionTimesList.clear();
        html.append("</table></html>");
        try (
                FileWriter myWriter = new FileWriter(System.getenv("EXECUTION_TIME_REPORT_PATH")+ File.separator+"API_Gateway_Execution_Time_"+new Date().getTime() +".html");

                ){
            myWriter.write(html.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }

        System.out.println(html.toString());
    }
    List<MethodExecutionTime> methodExecutionTimesList=new ArrayList<>();
    @Around("execution(* com.cit.vericash.*.*(..))")
    public Object aroundWebMethodE(ProceedingJoinPoint pjp) throws Throwable {
        String packageName = pjp.getSignature().getDeclaringTypeName();
        String methodName = pjp.getSignature().getName();
        long start = System.currentTimeMillis();
        Object output = pjp.proceed();
        long elapsedTime = System.currentTimeMillis() - start;
        if(!methodName.equals("initBinder")) {
            methodExecutionTimesList.add(new MethodExecutionTime(packageName,methodName,elapsedTime));
        }
        return output;
    }
}
