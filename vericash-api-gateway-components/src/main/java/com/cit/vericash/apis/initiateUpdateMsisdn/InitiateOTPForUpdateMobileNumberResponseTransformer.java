package com.cit.vericash.apis.initiateUpdateMsisdn;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.initiateUpdateMsisdn.InitiateOTPForUpdateMobileNumberResponseTransformer")
public class InitiateOTPForUpdateMobileNumberResponseTransformer implements MuleResponseTransformer {
    private final String successMsgKey = "success_msg";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    private String maskedMsisdn = "masked_Msisdn";
    private String otpLength = "otp_Length";

    public Object transform(BusinessMessage businessMessage) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();

        Map<String, String> result = new HashMap<String, String>();

        /**
         * MKEN-1234
         * return OTP length and masked MSISDN in response
         */
        String maskedMsisdnVal = cacheableFieldParameters.getResponseParameters(maskedMsisdn);
        String otpLengthVal = cacheableFieldParameters.getResponseParameters(otpLength);
        result.put(maskedMsisdnVal, businessMessage.getPrimarySenderInfo().getMaskedMsisdn());
        result.put(otpLengthVal, businessMessage.getPrimarySenderInfo().getOtpLength());

        try {

            result.put(cacheableFieldParameters.getResponseParameters(successMsgKey), "Success");

        } catch (Exception e) {
            throw e;
        }

        return result;
    }
}