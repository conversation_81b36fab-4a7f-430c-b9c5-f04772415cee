package com.cit.vericash.apis.reverseCashout;

import com.cit.mpaymentapp.common.customer.message.BalanceEnquiry;
import com.cit.mpaymentapp.common.message.PaymentMethodDetail;
import com.cit.mpaymentapp.common.message.ReverseTransactionInfo;
import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@Qualifier("com.cit.vericash.apis.reverseCashout.ReverseCashOutRequestTransformer")
public class ReverseCashOutRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    private final String originalAmountKey = "original_amount";
    private final String reversedAmountKey = "reverse_amount";
    private final String terminalIdKey = "terminal_id";
    private final String processingDateKey = "processing_date";
    private final String referenceNumberKey = "reference_number";
    private final String systemIdKey = "system_id";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

//        cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
        String currency = cacheableFieldParameters.getRequestParameters("currency");
        String orignalAmount = cacheableFieldParameters.getRequestParameters(originalAmountKey);
        String reversedAmount = cacheableFieldParameters.getRequestParameters(reversedAmountKey);
        String terminalId = cacheableFieldParameters.getRequestParameters(terminalIdKey);
        String processingDate = cacheableFieldParameters.getRequestParameters(processingDateKey);
        String referenceNumber = cacheableFieldParameters.getRequestParameters(referenceNumberKey);
        String systemId = cacheableFieldParameters.getRequestParameters(systemIdKey);
        PaymentMethodDetail paymentMethod = new PaymentMethodDetail();
        BalanceEnquiry balanceEnquiry = new BalanceEnquiry();
        TransactionInformation transactionInfo = new TransactionInformation();
        ReverseTransactionInfo reverseTransactionInfo = new ReverseTransactionInfo();

        balanceEnquiry.setBalanceCurrency(message.getPayload().getAttributeAsString(currency));
        paymentMethod.setBalanceEnquiry(balanceEnquiry);
        reverseTransactionInfo.setOriginalAmount(new BigDecimal(message.getPayload().getAttributeAsString(orignalAmount)));
        reverseTransactionInfo.setReversedAmount(new BigDecimal(message.getPayload().getAttributeAsString(reversedAmount)));
        reverseTransactionInfo.setProcessingDate(message.getPayload().getAttributeAsString(processingDate));
        reverseTransactionInfo.setTerminalId(Long.valueOf(message.getPayload().getAttributeAsString(terminalId)));
        reverseTransactionInfo.setSystemId(message.getPayload().getAttributeAsString(systemId));
        reverseTransactionInfo.setReferenceNumber(Long.valueOf(message.getPayload().getAttributeAsString(referenceNumber)));
        transactionInfo.setReverseTransactionInfo(reverseTransactionInfo);
        message.getPayload().put("paymentMethod", paymentMethod);
        message.getPayload().put("transactionInfo", transactionInfo);
    }
}
