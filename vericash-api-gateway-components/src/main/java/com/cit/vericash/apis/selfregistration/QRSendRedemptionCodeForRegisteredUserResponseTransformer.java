package com.cit.vericash.apis.selfregistration;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.selfregistration.QRSendRedemptionCodeForRegisteredUserResponseTransformer")
public class QRSendRedemptionCodeForRegisteredUserResponseTransformer implements MuleResponseTransformer {
    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        boolean isRegisteredUser = businessMessage.getParameters().getAttributeAsBoolean("isRegisteredUser");
        if(isRegisteredUser){
            result.put("isRegisteredUser",true);
            HashMap smsContent = (HashMap) businessMessage.getParameters().get("SendSMSWithRedemptionCode");

            String redemptionCode = (String)  smsContent.get("redemptionCode");
            String serialNumber = (String)  smsContent.get("serialNumber");
            String message = (String)  smsContent.get("message");

            if(redemptionCode!=null)
                    result.put("redemptionCode",redemptionCode);
                    result.put("message",message);
                    result.put("serialNumber",serialNumber);
            return result;
        }else{
            result.put("isRegisteredUser",false);
            return result;

        }

    }
}
