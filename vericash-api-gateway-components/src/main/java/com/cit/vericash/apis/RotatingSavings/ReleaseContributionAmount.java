package com.cit.vericash.apis.RotatingSavings;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ParametersMap;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.service.commons.codeMapping.PaymentMethodDto;
import com.cit.vericash.api.components.LoadPaymentMethodComponent;
import com.cit.vericash.api.components.SMEConfigLoaderComponent;
import com.cit.vericash.api.components.impl.LoadTransformWalletServiceComponent;
import com.cit.vericash.api.components.impl.ModelMapperInitializerImpl;
import com.cit.vericash.api.components.impl.PrepareTransformCommonsComponent;
import com.cit.vericash.apis.commons.api.ModelMapperInitializer;
import com.cit.vericash.apis.commons.api.MuleMessageSender;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.backend.commons.dynamicmodelmapper.*;
import com.cit.vericash.apis.commons.util.ModelMapper;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.security.authentication.AuthorizedValidationStep;
import com.cit.vericash.config.VericashBackendConfig;
import oracle.sql.TIMESTAMP;
import org.apache.commons.jxpath.JXPathContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class ReleaseContributionAmount {
    static Long businessServiceID = null;
    @Autowired
    ConnectionUtil connectionUtil;
    //	@Autowired
//	private pupulateSenderInfoUtil populateSenderInfoUtil;
    @Autowired
    LoadTransformWalletServiceComponent loadTransformWalletServiceComponent;
    @Autowired
    PrepareTransformCommonsComponent prepareTransformCommonsComponent;
    @Autowired
    SMEConfigLoaderComponent smeConfigLoader;
    @Autowired
    LoadPaymentMethodComponent loadPaymentMethodComponent;
    @Autowired
    AuthorizedValidationStep authorizedValidationStep;
    @Autowired
    VericashBackendConfig config;
    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;
    PaymentMethodDto destinationPaymentMethod = new PaymentMethodDto();
    PaymentMethodDto sourcePaymentMethod = new PaymentMethodDto();
    @Autowired
    private MuleMessageSender muleMessageSender;
    @Autowired
    private CachableVericashApi vericashApiCache;
    private ModelMapperInitializer modelMapperInitializer = new ModelMapperInitializerImpl();

    public void ReleaseDayService(Request request) throws Exception {
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields = setFields(fields);
        Integer chunkSize = propertyLoaderComponent.getPropertyAsInteger("ChunkSize", "application", "application");
        boolean readMoreRecords = true;
        int rowFrom = 0;
        int rowTo = chunkSize;
        VericashAPI vericashAPI = vericashApiCache.getVericashAPI(2293L);
        Message message = request.getMessage();
        List<Record> records = new ArrayList<Record>();

        while (readMoreRecords) {
            String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("ReleaseDayService", this.getClass(), String.valueOf(rowFrom), String.valueOf(rowTo));

            List<Record> tempRecords = connectionUtil.executeSelect(sqlQuery, parameters, fields);
            if (tempRecords == null || tempRecords.size() == 0) {
                readMoreRecords = false;
            }
            records.addAll(tempRecords);
            rowFrom += chunkSize;
            rowTo += chunkSize;
        }
        for (Record record : records) {
            Long walletId = record.getValueAsLong("WALLET_ID");
            TIMESTAMP turnTimeStamp = (TIMESTAMP) record.get("TURN_DAY");
            Date turnDayDate = new Date();
            turnDayDate.setTime(turnTimeStamp.timestampValue().getTime());
            LocalDate turnDayLocal = turnDayDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            Integer contributionHoldDays = getContributionHoldDays(walletId);
            boolean isReleaseDayToday = isReleaseDayToday(contributionHoldDays, turnDayLocal);
            if (isReleaseDayToday) {
                BusinessMessage businessMessage = getBusinessMessage(record, message);
                try {
                    muleMessageSender.sendMessageWithJson(businessMessage, vericashAPI);
                } catch (Exception e) {
                    throw e;
                }
            }

        }
    }

    private Integer getContributionHoldDays(Long walletId) {
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getContributionHoldDays", this.getClass(), String.valueOf(walletId));
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields = setFieldsContr(fields);
        List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters, fields);
        Integer contributionHoldDays = records.get(0).getValueAsInt("CONTRIBUTION_HOLD_DAYS");
        return contributionHoldDays;
    }

    private BusinessMessage getBusinessMessage(Record record, Message message) throws Exception {
        Payload payload = message.getPayload();
        Long id = record.getValueAsLong("ID");
        Long walletId = record.getValueAsLong("WALLET_ID");
        Long userId = message.getHeader().getAttributeAsLong("userId");
        PaymentMethodDto sourcePaymentMethod = getSourcePaymentMethod(userId, walletId);
        int currentTurn = record.getValueAsInt("CURRENT_TURN");
        int contributionAmount = record.getValueAsInt("TOTAL_AMOUNT");
        String walletShortCode = record.getValueAsString("WALLET_SHORT_CODE");
        Long paymentMethodCode = record.getValueAsLong("RECEIVING_ACCOUNT");
        BigDecimal paymentMethodTypeBig = record.getValueAsBigDecimal("PAYMENT_METHOD_TYPE");
        String paymentMethodType = paymentMethodTypeBig.toString();
        payload.setAttribute("amount", String.valueOf(contributionAmount));
        payload.setAttribute("currentTurn", currentTurn);
        payload.setAttribute("rotatingSavingsId", id);
        // receiving account 7azem
        // DestinationPaymentMethod destinationPaymentMethod = new DestinationPaymentMethod();
        destinationPaymentMethod.setPaymentMethodCode(paymentMethodCode);
        destinationPaymentMethod.setPaymentMethodType(paymentMethodType);
        payload.setAttribute("destinationPaymentMethod", destinationPaymentMethod);
        payload.setAttribute("sourcePaymentMethod", sourcePaymentMethod);
        message.getHeader().setAttribute("apiCode", "2293");
        message.setPayload(payload);
        BusinessMessage businessMessage = transform(message);
        // to be get from query
        if (businessServiceID == null) {
            businessServiceID = getBusinessServiceId();
        }
        businessMessage.getServiceInfo().setId("" + businessServiceID);
        businessMessage.getHeader().setWalletId(walletId);
        businessMessage.getHeader().setWalletShortCode(walletShortCode);


        ParametersMap parameters = new ParametersMap();
        parameters.putAll(payload);
        System.out.println(parameters);
        businessMessage.setParameters(parameters);


        return businessMessage;
    }

    public PaymentMethodDto getSourcePaymentMethod(Long userId, Long walletId) {
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getSourcePaymentMethod", this.getClass(), String.valueOf(userId), String.valueOf(walletId));
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields.add("GARID");
        fields.add("PAYMENT_METHOD_TYPE");
        List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters, fields);
        Long paymentMethodCode = records.get(0).getValueAsLong("GARID");
        Long paymentMethodType = records.get(0).getValueAsLong("PAYMENT_METHOD_TYPE");

        sourcePaymentMethod.setPaymentMethodType(paymentMethodType + "");
        sourcePaymentMethod.setPaymentMethodCode(paymentMethodCode);
        return sourcePaymentMethod;
    }

    private Long getBusinessServiceId() {
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getSourcePaymentMethod", this.getClass());
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields.add("ID");
        List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters, fields);
        Long id = records.get(0).getValueAsLong("ID");
        return id;

    }

    private boolean isReleaseDayToday(Integer contributionHoldDays, LocalDate turnDayLocal) {
        LocalDate releaseDayLocal = turnDayLocal.plusDays(contributionHoldDays);
        ZoneId defaultZoneId = ZoneId.systemDefault();
        Date releaseDayDate = Date.from(releaseDayLocal.atStartOfDay(defaultZoneId).toInstant());
        Date todaysDate = new Date();
        int datesCompare = releaseDayDate.compareTo(todaysDate);
        if (datesCompare == 0) {
            return true;
        }
        return false;
    }

    public List<String> setFields(List<String> fields) {
        fields.add("ID");
        fields.add("CURRENT_TURN");
        fields.add("TOTAL_AMOUNT");
        fields.add("RECEIVING_ACCOUNT");
        fields.add("PAYMENT_METHOD_TYPE");
        fields.add("TURN_DAY");
        fields.add("WALLET_SHORT_CODE");
        fields.add("WALLET_ID");
        return fields;
    }

    public List<String> setFieldsContr(List<String> fields) {
        fields.add("CONTRIBUTION_HOLD_DAYS");
        return fields;
    }

    public final BusinessMessage transform(Message message) throws Exception {
        BusinessMessage businessMessage = null;

        try {
            Long apiCode = message.getHeader().getAttributeAsLong("apiCode");
            VericashAPI vericashAPI = vericashApiCache.getVericashAPI(apiCode);
            ModelMapper modelMapper = modelMapperInitializer.getModelMapper(vericashAPI);
            List<Field> fieldList = modelMapper.getFields();
            businessMessage = new BusinessMessage();
            JXPathContext targetObject = JXPathContext.newContext(businessMessage);
            smeConfigLoader.prepareSMEBusinessMessage(message, targetObject);
            loadPaymentMethodComponent.loadPaymentMethodUsingOptions(message, targetObject);
            loadTransformWalletServiceComponent.loadTransformWalletService(vericashAPI, null, message, targetObject, businessMessage);
            prepareTransformCommonsComponent.prepareTransformationToBusinessMessage(message, targetObject);
            businessMessage.getServiceInfo().setCode("100043");
            enrichCustomerMessageWithCustomValues(message);

        } catch (Exception e) {
            throw e;
        }

        return businessMessage;
    }

    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

    }

    private void mapValuesToObjects(ModelMapper modelMapper, JXPathContext targetObject, JXPathContext sourceObject) {
        List<Field> mapperFields = modelMapper.getFields();
        if (mapperFields != null) {
            for (int i = 0; i < mapperFields.size(); i++) {
                Field field = mapperFields.get(i);
                String sourceField = field.getSource();
                String targetField = field.getTarget();

                Object sourceValue = sourceObject.getValue(sourceField);
                targetObject.setValue(targetField, sourceValue);
            }
        }

    }

}