package com.cit.vericash.apis.becustomerManagement;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.service.commons.codeMapping.Paging;
import com.cit.service.commons.codeMapping.PropertyLoaderInt;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.commons.dto.BECustomerDto;
import com.cit.vericash.api.commons.dto.SimpleGenericDTO;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.becustomerManagement.ViewBECustomersService")
public class ViewBECustomersService {
    private static final String SEARCH_API_CODE = "2330";
    private static final String VIEW_PENDING_CUSTOMER_HOME = "2356";
    private static int ACTIVITY_TRAILS_PENDING_APPROVAL = 0;
    private static int APPROVAL_STATUS_PENDING_APPROVAL = 0;
    private static String PAGE_SIZE = "pageSize";
    @Autowired
    ConnectionUtil connectionUtil;
    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;
    ArrayList<BECustomerDto> beCustomerData;
    List<SimpleGenericDTO> allowedActionsList;
    List<SimpleGenericDTO> allowedActionsFullList;
    @Autowired
    Paging paging;
    @Autowired
    PropertyLoaderInt propertyLoader;

    public ArrayList<BECustomerDto> getBeCustomers(Request request, boolean pendingApproval) throws Exception {
        String apiCode = request.getMessage().getHeader().getAttributeAsString("apiCode");

        Query query = getQuery(request, pendingApproval, apiCode);

        String queryStr = query.getQuery();
        String json = propertyLoaderComponent.getPropertyAsString("beCustomerAllowedActions", "application", "application");
        List<Record> records = connectionUtil.executeSelect(queryStr, query.getParameters(), query.getFields());
        Type type = new TypeToken<HashMap<String, List<SimpleGenericDTO>>>() {
        }.getType();
        HashMap<String, List<SimpleGenericDTO>> hashMap1 = new Gson().fromJson(json, type); // TODO rename
        for (int i = 0; i < records.size(); i++) {
            BECustomerDto beCustomerDto = new BECustomerDto();
            Record record = records.get(i);
            beCustomerDto.setBeCustomerId(record.getValueAsLong("ID"));
            String fullname = record.getValueAsString("FIRST_NAME") + " " + records.get(i).getValueAsString("LAST_NAME");
            beCustomerDto.setCustomerName(fullname);
//            beCustomerDto.setRole(record.getValueAsString("role"));
            beCustomerDto.setProfilePictureLink(record.getValueAsString("PROFILE_PICTURE_LINK"));
            Long beCustomerId = record.getValueAsLong("ID");
            String approvalStatus = record.getValueAsString("approvalstatus");
            String beCustomerStatusName = record.getValueAsString("becustomerStatusName");
            Long activityStatus = record.getValueAsLong("approvalstatusId");
            BigDecimal customerStatus = record.getValueAsBigDecimal("Becustomeractivitystatus");
            beCustomerDto.setStatus(approvalStatus);
            beCustomerDto.setStatusId(activityStatus);
            if (beCustomerId != null) {
                beCustomerDto.setStatus(beCustomerStatusName);
                beCustomerDto.setStatusId(customerStatus.longValue());
            }
            String role = record.getValueAsString("ROLE");
            Long taskId = record.getValueAsLong("TASK_ID");
            Long roleId = record.getValueAsLong("ROLE_ID");
            beCustomerDto.setRoleId(roleId);
            beCustomerDto.setTaskId(taskId);
            beCustomerDto.setRole(role);
            if (customerStatus == null && activityStatus != APPROVAL_STATUS_PENDING_APPROVAL) {
                if (hashMap1.containsKey("activityStatus_" + activityStatus)) {
                    beCustomerDto.setAllowedActions(hashMap1.get("activityStatus_" + activityStatus));
                }
            } else if (customerStatus != null) {
                beCustomerDto.setStatus(record.getValueAsString("becustomerStatusName"));
                if (hashMap1.containsKey("beCustomerStatus_" + customerStatus)) {
                    beCustomerDto.setAllowedActions(hashMap1.get("beCustomerStatus_" + customerStatus));
                }
            }
            beCustomerData.add(beCustomerDto);
        }

        return beCustomerData;
    }

    private Query getQuery(Request request, boolean pendingApproval, String apiCode) throws GeneralFailureException, IOException {
        Query query = new Query();
        String name = request.getMessage().getPayload().getAttributeAsString("beCustomerName");
        Long role = request.getMessage().getPayload().getAttributeAsLong("roleId");
        Long status = request.getMessage().getPayload().getAttributeAsLong("status");
        String searchQuery = request.getMessage().getPayload().getAttributeAsString("searchQuery");

        int corpid = request.getMessage().getHeader().getAttributeAsInteger("corporateId");
        Integer pageNumber = request.getMessage().getPayload().getAttributeAsInteger("pageNumber");
        Integer pagingObjFrom = null;
        Integer pagingObjTo = null;
        if (pageNumber != null) {
            String walletShortCode = request.getMessage().getHeader().getAttributeAsString("walletShortCode");
            int pageSize = Integer.parseInt(propertyLoader.loadProperty(walletShortCode + "_" + PAGE_SIZE));
            Paging pagingObj = paging.getPaging(pageNumber, pageSize);
            pagingObjFrom = pagingObj.getFrom();
            pagingObjTo = pagingObj.getTo();
        }

        StringBuilder queryStr = null;
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<String>();
        beCustomerData = new ArrayList<>();
        fields = setFields(fields);

        parameters.add(new Parameter(1, corpid));

        queryStr = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("getQuery",this.getClass()));

        if (pendingApproval == false) {
            queryStr.append(ServiceQueryEngine.getQueryStringToExecute("getQuery_approved_condition",this.getClass()));
            if (apiCode.equals(SEARCH_API_CODE)) {
                int index = 1;
                if (name != null) {
                    ++index;
                    queryStr.append(ServiceQueryEngine.getQueryStringToExecute("getQuery_name_condition", this.getClass()));
                    parameters.add(new Parameter(index, name));
                }
                if (role != null) {
                    ++index;
                    queryStr.append(ServiceQueryEngine.getQueryStringToExecute("getQuery_role_condition", this.getClass()));
                    parameters.add(new Parameter(index, role));
                }
                if (status != null) {
                    ++index;
                    queryStr.append(ServiceQueryEngine.getQueryStringToExecute("getQuery_status_condition", this.getClass()));
                    parameters.add(new Parameter(index, status));
                }
            }
            queryStr.append(ServiceQueryEngine.getQueryStringToExecute("getQuery_after_condition", this.getClass(), String.valueOf(pagingObjFrom), String.valueOf(pagingObjTo)));
        } else if (pendingApproval) {
            String customerId = request.getMessage().getHeader().getAttributeAsString("customerId");
            String corporateId = request.getMessage().getHeader().getAttributeAsString("corporateId");
            parameters.add(new Parameter(1, corporateId));
            parameters.add(new Parameter(2, ACTIVITY_TRAILS_PENDING_APPROVAL));
            parameters.add(new Parameter(3, customerId));
            parameters.add(new Parameter(4, APPROVAL_STATUS_PENDING_APPROVAL));

            queryStr = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("getQuery_pendingApproval_condition",this.getClass()));
            if (isHOME(apiCode))
                queryStr.append(ServiceQueryEngine.getQueryStringToExecute("isHome_condition",this.getClass(),String.valueOf(propertyLoaderComponent.getPropertyAsInteger("maxPendingUsersHomeScreen"))));
            else
                queryStr.append(ServiceQueryEngine.getQueryStringToExecute("else_condition",this.getClass(),String.valueOf(pagingObjFrom),String.valueOf(pagingObjTo)));

        } else
            queryStr.append(ServiceQueryEngine.getQueryStringToExecute("getQuery_after_condition",this.getClass(),String.valueOf(pagingObjFrom),String.valueOf(pagingObjTo)));

        query.setQuery(queryStr.toString());
        query.setFields(fields);
        query.setParameters(parameters);
        System.out.println("QUERY : ");
        System.out.println(query.getQuery());
        return query;
    }

    private boolean isHOME(String apiCode) {
        return VIEW_PENDING_CUSTOMER_HOME.equals(apiCode);
    }

    private List<String> setFields(List<String> fields) {
        fields.add("ID");
        fields.add("FIRST_NAME");
        fields.add("MIDDLE_NAME");
        fields.add("LAST_NAME");
        fields.add("approvalstatus");
        fields.add("Becustomeractivitystatus");
        fields.add("approvalstatusId");
        fields.add("becustomerStatusName");
        fields.add("ROLE");
        fields.add("TASK_ID");
        fields.add("ROLE_ID");
        return fields;
    }

    private class Query {
        String query;
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<String>();

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }

        public List<Parameter> getParameters() {
            return parameters;
        }

        public void setParameters(List<Parameter> parameters) {
            this.parameters = parameters;
        }

        public List<String> getFields() {
            return fields;
        }

        public void setFields(List<String> fields) {
            this.fields = fields;
        }
    }

}
