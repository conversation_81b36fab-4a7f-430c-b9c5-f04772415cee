package com.cit.vericash.apis.AdsService.FilterResponsabilityChain;

import com.cit.vericash.api.entity.Ad;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.mastercard.api.core.exception.ApiException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DeviceAndCountryFilterTest {

    private DeviceAndCountryFilter deviceAndCountryFilter;

    @Mock
    private AdsFilterHandler nextHandler;

    @Mock
    private Request request;

    @Mock
    private Payload payload;

    private LinkedHashMap<String, Object> requiredData = new LinkedHashMap<>();

    @BeforeEach
    void setUp() {
        requiredData.put("country", 1L);
        deviceAndCountryFilter = new DeviceAndCountryFilter(requiredData);


    }

    @Test
    void handleRequest_shouldFilterAdsByCountry() throws ApiException {
        // Arrange
        Ad countryAd = new Ad();
        countryAd.setAudienceCategoryId(0L); // COUNTRY_ID
        countryAd.setCountryIds(Collections.singletonList(1L));

        LinkedHashMap<String, Object> clientInfo = new LinkedHashMap<>();
        clientInfo.put("platformName", "android");

        when(request.getMessage()).thenReturn(mock(Message.class));
        when(request.getMessage().getPayload()).thenReturn(payload);
        when(payload.get("clientInfo")).thenReturn(clientInfo);

        Ad androidAd = new Ad();
        androidAd.setAudienceCategoryId(1L); // ANDROID_ID

        Stream<Ad> adStream = Stream.of(countryAd, androidAd);

        // Act
        Stream<Ad> result = deviceAndCountryFilter.handleRequest(request, adStream);

        // Assert
        assertEquals(2, result.count()); // Both should pass
    }

    @Test
    void handleRequest_shouldFilterAdsByPlatform() throws ApiException {

        LinkedHashMap<String, Object> clientInfo = new LinkedHashMap<>();
        clientInfo.put("platformName", "android");

        when(request.getMessage()).thenReturn(mock(Message.class));
        when(request.getMessage().getPayload()).thenReturn(payload);
        when(payload.get("clientInfo")).thenReturn(clientInfo);

        // Arrange
        Ad androidAd = new Ad();
        androidAd.setAudienceCategoryId(1L); // ANDROID_ID

        Ad iosAd = new Ad();
        iosAd.setAudienceCategoryId(2L); // IOS_ID

        Stream<Ad> adStream = Stream.of(androidAd, iosAd);

        // Act
        Stream<Ad> result = deviceAndCountryFilter.handleRequest(request, adStream);

        // Assert
        assertEquals(1, result.count()); // Only androidAd should pass
    }

    @Test
    void handleRequest_shouldThrowApiExceptionOnError() {
        LinkedHashMap<String, Object> clientInfo = new LinkedHashMap<>();
        clientInfo.put("platformName", "android");

        when(request.getMessage()).thenReturn(mock(Message.class));
        when(request.getMessage().getPayload()).thenReturn(payload);
        when(payload.get("clientInfo")).thenReturn(clientInfo);

        // Arrange
        when(payload.get("clientInfo")).thenThrow(new RuntimeException());

        // Act & Assert
        assertThrows(ApiException.class, () -> deviceAndCountryFilter.handleRequest(request, Stream.empty()));
    }

    @Test
    void getPlatformId_shouldReturnIosIdForIosPlatform() {
        // Act
        Integer result = deviceAndCountryFilter.getPlatformId("iOS");

        // Assert
        assertEquals(2, result); // IOS_ID
    }

    @Test
    void getPlatformId_shouldReturnAndroidIdForAndroidPlatform() {
        // Act
        Integer result = deviceAndCountryFilter.getPlatformId("Android");

        // Assert
        assertEquals(1, result); // ANDROID_ID
    }
}