package com.cit.vericash.apis.viewUserProfile;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.viewUserProfile.ViewAgentProfileResponseTransformer")
public class ViewAgentProfileResponseTransformer implements MuleResponseTransformer {

    public Object transform(BusinessMessage businessMessage) throws Exception {
        // TODO Auto-generated method stub
        return null;
    }
	
	
	/*public Object transform(BusinessMessage businessMessage) throws Exception {
	Map<String, String> result = new HashMap<String, String>();
		if(businessMessage.getBusinessUserInfo() != null) {
			BusinessUserInfo businessUserInfo = businessMessage.getBusinessUserInfo();
			
		String businessEntityName = businessUserInfo.getBusinessEntityName();
			String businessEntityParent = businessUserInfo.getParentBusinessEntity();
			String corporateName = businessUserInfo.getCorporateName();
			String businessEntityType = businessUserInfo.getBusinessEntityType();
			String city = businessUserInfo.getCity();
			String country = businessUserInfo.getCountry();
			String state = businessUserInfo.getState();
			String registrationNumber = businessUserInfo.getRegistrationNumber();
			String walletIdentifier = businessUserInfo.getWalletIdentifier();
			String msisdn = businessUserInfo.getMsisdn();
			
			result.put("Business Entity Name",businessEntityName);
			result.put("Country",country);
			result.put("City",city);
			result.put("type",businessEntityType);
			result.put("Corporate Name",corporateName);
			result.put("Registration Number",registrationNumber);
			result.put("Parent Business Entity",businessEntityParent);
			result.put("Wallet Identifier",walletIdentifier);
			result.put("MSISDN",msisdn);
			result.put("State",state);
		}

		return result;
	}*/


}
