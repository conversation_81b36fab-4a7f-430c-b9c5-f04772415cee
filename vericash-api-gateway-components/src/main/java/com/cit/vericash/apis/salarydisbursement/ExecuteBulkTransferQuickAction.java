package com.cit.vericash.apis.salarydisbursement;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.ExecuteBulkTransferQuickAction")
public class ExecuteBulkTransferQuickAction implements VericashAction {

    @Autowired
    ExecuteBulkTransferCommonService executeBulkTransferCommonService;

    @Autowired
    ConnectionUtil connectionUtil;

    @Override
    public Object process(Request request) throws Exception {
        Integer groupTransferSetupId = request.getMessage().getPayload().getAttributeAsInteger("groupSetupId");

        List<Record> recipientsRecords = getRecipientsForNormalFlow(groupTransferSetupId);

        executeBulkTransferCommonService.executeTransfer(request, recipientsRecords);
        return null;
    }

    public List<Record> getRecipientsForNormalFlow(Integer groupTransferSetupId) {
        //get all recipients for group transfer setup1
        String transferRecipientsQuery = ServiceQueryEngine.getQueryStringToExecute("getRecipientsForNormalFlow",this.getClass(),String.valueOf(groupTransferSetupId));
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields = setRecipientsFields(fields);
        List<Record> recipientRecords = connectionUtil.executeSelect(transferRecipientsQuery, parameters, fields);

        return recipientRecords;
    }

    public List<String> setRecipientsFields(List<String> fields) {
        fields.add("TRANSFER_ID");
        fields.add("AMOUNT");
        fields.add("BENEFICIARY_ID");
        fields.add("BENEFICIARY_NAME");
        fields.add("BRANCH_CODE");
        fields.add("BRANCH_NAME");
        fields.add("CARD_NUMBER");
        fields.add("COUNTRY_NAME");
        fields.add("INSTITUTION_CODE");
        fields.add("INSTITUTION_NAME");
        fields.add("TRANSFER_TYPE");
        fields.add("TRANSFER_TYPE_CATEGORY");
        fields.add("WALLET_SHORT_CODE");
        fields.add("GROUP_TRANSFER_SETUP_ID");
        fields.add("STATUS");
        fields.add("CLIENT_ID");
        fields.add("RECEIVER_TYPE");
        fields.add("WALLET_PROVIDER");
        fields.add("CURRENCY");
        fields.add("PAYMENT_ALIAS");
        fields.add("MOBILE_NUMBER");
        fields.add("IS_DELETED");
        fields.add("VALIDATION_ERROR");
        fields.add("GAR_ID");
        fields.add("PAYMENT_METHOD_TYPE");

        fields.add("ACCOUNT_ID");
        fields.add("CUSTOMER_ID");
        fields.add("CUSTOMER_FEE_PROFILE_ID");
        fields.add("GROUP_NAME");
        return fields;
    }


}