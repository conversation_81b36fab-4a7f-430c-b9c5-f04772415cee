package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ParametersMap;
import com.cit.mpaymentapp.common.message.StatusType;
import com.cit.mpaymentapp.model.Enums;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.service.commons.codeMapping.PaymentMethodDto;
import com.cit.service.commons.codeMapping.ServiceCodeMappingComponent;
import com.cit.vericash.api.components.LoadPaymentMethodComponent;
import com.cit.vericash.api.components.PaymentMethodServicePreprationComponent;
import com.cit.vericash.api.components.SMEConfigLoaderComponent;
import com.cit.vericash.api.components.impl.LoadTransformWalletServiceComponent;
import com.cit.vericash.api.components.impl.PrepareTransformCommonsComponent;
import com.cit.vericash.api.entity.GroupTransferHistory;
import com.cit.vericash.api.entity.GroupTransferSetup;
import com.cit.vericash.api.repository.GroupTransferHistoryRepository;
import com.cit.vericash.api.repository.GroupTransferSetupRepository;
import com.cit.vericash.apis.commons.api.MuleMessageSender;
import com.cit.vericash.apis.commons.exceptions.MuleException;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.security.authentication.AuthorizedValidationStep;
import com.cit.vericash.config.VericashBackendConfig;
import org.apache.commons.jxpath.JXPathContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.ExecuteBulkTransferCommonService")
public class ExecuteBulkTransferCommonService {

    final static String parentCode = "100050";
    @Autowired
    ConnectionUtil connectionUtil;
    @Autowired
    LoadTransformWalletServiceComponent loadTransformWalletServiceComponent;
    @Autowired
    PrepareTransformCommonsComponent prepareTransformCommonsComponent;
    @Autowired
    SMEConfigLoaderComponent smeConfigLoader;
    @Autowired
    LoadPaymentMethodComponent loadPaymentMethodComponent;
    @Autowired
    AuthorizedValidationStep authorizedValidationStep;
    @Autowired
    VericashBackendConfig config;
    @Autowired
    ServiceCodeMappingComponent serviceCodeMappingComponent;
    @Autowired
    PaymentMethodServicePreprationComponent paymentMethodServicePreprationComponent;
    @Autowired
    GroupTransferHistoryRepository groupTransferHistoryRepository;
    @Autowired
    GroupTransferSetupRepository groupTransferSetupRepository;
    @Autowired
    CalculateBulkFeesCommonService calculateBulkFeesCommonService;
    @Autowired
    private MuleMessageSender muleMessageSender;
    @Autowired
    private CachableVericashApi vericashApiCache;

    public void executeTransfer(Request request, List<Record> recipientRecords) throws Exception {
        getRecipientsAndCalculateFee(request, recipientRecords);
    }

    public BusinessMessage getRecipientsAndCalculateFee(Request request, List<Record> recipientRecords) throws Exception {
        String apiCode = request.getMessage().getHeader().getAttributeAsString("apiCode");
        GroupTransferSetup groupTransferSetup = new GroupTransferSetup();

        //load group transfer setup id from request
        Integer groupTransferSetupId = request.getMessage().getPayload().getAttributeAsInteger("groupSetupId");
        groupTransferSetup = groupTransferSetupRepository.findById(Long.valueOf(groupTransferSetupId)).orElse(null);


        //execute transfer & save recipient transfer execution step
        VericashAPI vericashAPISecondStep = vericashApiCache.getVericashAPI(2299l);

        Message message = request.getMessage();
        Message paymentMethodMappingMessage = new Message();
        Payload payload = new Payload();
        PaymentMethodDto destinationPaymentMethod;
        Long totalNumberOfFailures = 0l;
        Long totalNumberOfSuccess = 0l;
        boolean errorExists;
        PaymentMethodDto sourcePaymentMethod = new PaymentMethodDto();

        if (apiCode.equals("2296")) {
            //load source payment method from request
            LinkedHashMap<String, LinkedHashMap<String, Integer>> sourcePaymentMethodMap = (LinkedHashMap<String, LinkedHashMap<String, Integer>>) message.getPayload().getAttribute("sourcePaymentMethod");

            //map source payment method
            String sourcePaymentMethodType = String.valueOf(sourcePaymentMethodMap.get("paymentMethodType"));
            Long sourcePaymentMethodCode = Long.valueOf(String.valueOf(sourcePaymentMethodMap.get("paymentMethodCode")));
            sourcePaymentMethod.setPaymentMethodType(sourcePaymentMethodType);
            sourcePaymentMethod.setPaymentMethodCode(sourcePaymentMethodCode);
        } else {
            if (groupTransferSetup != null) {
                sourcePaymentMethod.setPaymentMethodType(groupTransferSetup.getPaymentMethodType());
                sourcePaymentMethod.setPaymentMethodCode(groupTransferSetup.getPaymentMethodCode());
            }
        }
        BusinessMessage businessMessage = new BusinessMessage();
        request.getMessage().getPayload().put("groupTransferSetup", groupTransferSetup);

        businessMessage = (BusinessMessage) calculateBulkFeesCommonService.calculateFee(request, recipientRecords);

        BigDecimal totalRecipientsAmount = BigDecimal.valueOf(0);
        BigDecimal accountId = BigDecimal.valueOf(0);
        if (businessMessage.getParameters().getAttributeAsBigDecimal("totalRecipientsAmount") != null) {
            totalRecipientsAmount = businessMessage.getParameters().getAttributeAsBigDecimal("totalRecipientsAmount");
        }
        if (businessMessage.getParameters().getAttributeAsBigDecimal("accountId") != null) {
            accountId = businessMessage.getParameters().getAttributeAsBigDecimal("accountId");
        }


        //set total fees and total vat retrieved from mule
        BigDecimal totalFees = businessMessage.getParameters().getAttributeAsBigDecimal("totalFees");
        BigDecimal totalVat = businessMessage.getParameters().getAttributeAsBigDecimal("totalVat");

        //get status type
        StatusType statusType = businessMessage.getStatus();

        //check if account has sufficient balance or any error from mule
        if (statusType.getErrorFlag() || statusType.getStatusCode() != null || statusType.getStatusMsg() != null || statusType.getStatusSource() != null) {
            if (statusType.getErrorFlag() && statusType.getStatusCode() != null) {
                throw new MuleException(statusType.getStatusCode(), statusType.getStatusMsg());
            }
        }

        if (businessMessage.getParameters().getAttributeAsBigDecimal("totalRecipientsAmount") != null) {
            totalRecipientsAmount = businessMessage.getParameters().getAttributeAsBigDecimal("totalRecipientsAmount");
        }
        if (businessMessage.getParameters().getAttributeAsBigDecimal("accountId") != null) {
            accountId = businessMessage.getParameters().getAttributeAsBigDecimal("accountId");
        }

        String groupName = "";

        if (recipientRecords == null || recipientRecords.isEmpty()) {
            throw new MuleException("VAL0009", "No recipients found for group transfer setup id " + groupTransferSetupId);
        }

        if (recipientRecords.get(0).getOrDefault("GROUP_NAME", "") != null) {
            groupName = recipientRecords.get(0).getOrDefault("GROUP_NAME", "").toString();
        }
        String userId = request.getMessage().getHeader().getAttributeAsString("userId");


        //Update Group Transfer Setup parent


        groupTransferSetup.setPaymentMethodCode(sourcePaymentMethod.getPaymentMethodCode());
        groupTransferSetup.setPaymentMethodType(sourcePaymentMethod.getPaymentMethodType());

        groupTransferSetupRepository.save(groupTransferSetup);

        //Save Group Transfer history parent
        GroupTransferHistory groupTransferHistoryEntity = new GroupTransferHistory();
        groupTransferHistoryEntity.setGroupSetupId(Long.valueOf(groupTransferSetupId));
        groupTransferHistoryEntity.setCustomerId(Long.valueOf(userId));
        groupTransferHistoryEntity.setTotalNumberOfFailure(0l);
        groupTransferHistoryEntity.setTotalNumberOfSuccess(0l);
        groupTransferHistoryEntity.setTotalNumberOfInProgress((long) recipientRecords.size());
        groupTransferHistoryEntity.setNumOfRecipients((long) recipientRecords.size());
        groupTransferHistoryEntity.setStatus("In Progress");
        groupTransferHistoryEntity.setName(groupName);
        groupTransferHistoryEntity.setServiceLogId(null);
        groupTransferHistoryEntity.setTransferSenderAccount(String.valueOf(accountId));
        if (totalFees != null) {
            groupTransferHistoryEntity.setTotalFee(totalFees.longValue());
        }
        if (totalVat != null) {
            groupTransferHistoryEntity.setTotalVat(totalVat.longValue());
        }
        groupTransferHistoryEntity.setTotalPayoutAmount(totalRecipientsAmount.longValue());
        groupTransferHistoryEntity.setTransferDate(new Date());

        groupTransferHistoryEntity = groupTransferHistoryRepository.save(groupTransferHistoryEntity);

        String childServiceCode;

        for (Map<String, Object> record : recipientRecords) {
            destinationPaymentMethod = new PaymentMethodDto();
            destinationPaymentMethod.setPaymentMethodType(record.get("PAYMENT_METHOD_TYPE").toString());
            destinationPaymentMethod.setPaymentMethodCode(Long.valueOf(record.get("GAR_ID").toString()));
            payload.setAttribute("sourcePaymentMethod", sourcePaymentMethod);
            payload.setAttribute("destinationPaymentMethod", destinationPaymentMethod);

            Enums.OwnerTypeEnum ownerTypeEnum = businessMessage.getPrimarySenderInfo().getOwnerType();

            payload.setAttribute("senderOwnerType", ownerTypeEnum.getTypeEnum(String.valueOf(ownerTypeEnum.ordinal())));
            paymentMethodMappingMessage.setPayload(payload);
            childServiceCode = serviceCodeMappingComponent.excute(parentCode, paymentMethodMappingMessage);
            businessMessage = getBusinessMessage(setHashMap(record), message, "2299", accountId, groupTransferHistoryEntity.getTransferHistoryId(), groupName, (long) recipientRecords.size(), totalRecipientsAmount);
            businessMessage.getServiceInfo().setCode(childServiceCode);


            //outbound to mule execute transaction step
            businessMessage = (BusinessMessage) muleMessageSender.sendMessageWithJson(businessMessage, vericashAPISecondStep);

            errorExists = businessMessage.getStatus().getErrorFlag();

            if (errorExists) {
                totalNumberOfFailures += 1;
            } else {
                totalNumberOfSuccess += 1;
            }
        }

        //Update Group Transfer history parent
        groupTransferHistoryEntity.setTotalNumberOfFailure(totalNumberOfFailures);
        groupTransferHistoryEntity.setTotalNumberOfSuccess(totalNumberOfSuccess);

        if (totalNumberOfFailures > 0 && totalNumberOfSuccess == 0) {
            groupTransferHistoryEntity.setStatus("failed");
        } else if (totalNumberOfFailures > 0 && totalNumberOfSuccess > 0) {
            groupTransferHistoryEntity.setStatus("partially_succeeded");
        } else if (totalNumberOfFailures == 0 && totalNumberOfSuccess > 0) {
            groupTransferHistoryEntity.setStatus("succeeded");
        }
        groupTransferHistoryRepository.save(groupTransferHistoryEntity);

        return businessMessage;
    }

    //load business message needed data for mule steps
    private BusinessMessage getBusinessMessage(Record record, Message message, String apiCode, BigDecimal accountId, Long groupTransferHistoryId, String groupName, Long recipientListSize, BigDecimal totalRecipientsAmount) throws Exception {
        Payload payload = message.getPayload();
        Long userId = message.getHeader().getAttributeAsLong("userId");

        //Set Group Transfer Recipient Attribute
        if (record != null) {
            Long id = record.getValueAsLong("TRANSFER_ID");
            payload.setAttribute("groupTransferRecipientId", id);
        }

        if (record != null) {
            BigDecimal amount = record.getValueAsBigDecimal("AMOUNT");
            payload.setAttribute("amount", amount);
        }
        if (record != null) {
            BigDecimal customerFeeProfileId = record.getValueAsBigDecimal("CUSTOMER_FEE_PROFILE_ID");
            payload.setAttribute("customerFeeProfileId", customerFeeProfileId);
        }

        payload.setAttribute("accountId", accountId);
        payload.setAttribute("groupTransferHistoryId", groupTransferHistoryId);
        payload.setAttribute("groupName", groupName);
        payload.setAttribute("numOfReceivers", recipientListSize);
        payload.setAttribute("totalRecipientsAmount", totalRecipientsAmount);

        //Set Payment Method Attributes
        if (record != null) {
            Long paymentMethodCode = record.getValueAsLong("GAR_ID");
            BigDecimal paymentMethodTypeBig = record.getValueAsBigDecimal("PAYMENT_METHOD_TYPE");

            String paymentMethodType = paymentMethodTypeBig.toString();
            PaymentMethodDto destinationPaymentMethod = new PaymentMethodDto();
            destinationPaymentMethod.setPaymentMethodCode(paymentMethodCode);
            destinationPaymentMethod.setPaymentMethodType(paymentMethodType);
            payload.setAttribute("destinationPaymentMethod", destinationPaymentMethod);
        }

        //Set Api Code Attribute
        message.getHeader().setAttribute("apiCode", apiCode);

        message.setPayload(payload);

        //Set Business Message Attributes
        BusinessMessage businessMessage = transformBusinessMessage(message);
        VericashAPI vericashAPI = vericashApiCache.getVericashAPI(Long.valueOf(apiCode));

        Long businessId = getBusinessId(vericashAPI);
        businessMessage.getServiceInfo().setId("" + businessId);

        //Set Wallet Attributes
        String walletShortCode = message.getHeader().getAttributeAsString("walletShortCode");
        Long walletId = message.getHeader().getAttributeAsLong("walletId");
        businessMessage.getHeader().setWalletId(walletId);
        businessMessage.getHeader().setWalletShortCode(walletShortCode);
        businessMessage.getHeader().setCustomerId(userId);


        //Set Pay Load to Business Message Parameters
        ParametersMap parameters = new ParametersMap();
        payload.remove("PMTransferType");
        payload.remove("groupTransferSetup");
        parameters.putAll(payload);
        System.out.println(parameters);

        businessMessage.setParameters(parameters);
        return businessMessage;
    }

    public Long getBusinessId(VericashAPI vericashAPI) {
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getBusinessId", this.getClass(), vericashAPI.getName());
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields.add("ID");
        List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters, fields);
        return records.get(0).getValueAsLong("ID");
    }

    public List<String> setAccountIdFields(List<String> fields) {
        fields.add("ACCOUNT_ID");
        return fields;
    }

    public List<String> setReciepientsFields(List<String> fields) {
        fields.add("TRANSFER_ID");
        fields.add("AMOUNT");
        fields.add("BENEFICIARY_ID");
        fields.add("BENEFICIARY_NAME");
        fields.add("BRANCH_CODE");
        fields.add("BRANCH_NAME");
        fields.add("CARD_NUMBER");
        fields.add("COUNTRY_NAME");
        fields.add("INSTITUTION_CODE");
        fields.add("INSTITUTION_NAME");
        fields.add("TRANSFER_TYPE");
        fields.add("TRANSFER_TYPE_CATEGORY");
        fields.add("WALLET_SHORT_CODE");
        fields.add("GROUP_TRANSFER_SETUP_ID");
        fields.add("STATUS");
        fields.add("CLIENT_ID");
        fields.add("RECEIVER_TYPE");
        fields.add("WALLET_PROVIDER");
        fields.add("CURRENCY");
        fields.add("PAYMENT_ALIAS");
        fields.add("MOBILE_NUMBER");
        fields.add("IS_DELETED");
        fields.add("VALIDATION_ERROR");
        fields.add("GAR_ID");
        fields.add("PAYMENT_METHOD_TYPE");

        fields.add("ACCOUNT_ID");
        fields.add("CUSTOMER_ID");
        fields.add("CUSTOMER_FEE_PROFILE_ID");
        fields.add("GROUP_NAME");
        return fields;
    }

    public final BusinessMessage transformBusinessMessage(Message message) throws Exception {
        BusinessMessage businessMessage = null;

        try {
            Long apiCode = message.getHeader().getAttributeAsLong("apiCode");
            VericashAPI vericashAPI = vericashApiCache.getVericashAPI(apiCode);

            businessMessage = new BusinessMessage();
            JXPathContext targetObject = JXPathContext.newContext(businessMessage);

            smeConfigLoader.prepareSMEBusinessMessage(message, targetObject);
            loadPaymentMethodComponent.loadPaymentMethodUsingOptions(message, targetObject);
            paymentMethodServicePreprationComponent.LoadPaymentMethodsTypes(message, vericashAPI);
            loadTransformWalletServiceComponent.loadTransformWalletService(vericashAPI, null, message, targetObject, businessMessage);
            prepareTransformCommonsComponent.prepareTransformationToBusinessMessage(message, targetObject);
        } catch (Exception e) {
            throw e;
        }
        return businessMessage;
    }

    public Record setHashMap(Map<String, Object> map) {

        Record record = new Record();

        HashMap<String, Object> hMap = new HashMap<String, Object>();
        if (map != null && map instanceof HashMap<?, ?>) {
            hMap = (HashMap<String, Object>) map;
        } else if (map != null) {
            hMap.putAll(map);
        }
        for (Map.Entry<String, Object> entry : hMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            record.setField(key, value);
        }
        return record;
    }

}