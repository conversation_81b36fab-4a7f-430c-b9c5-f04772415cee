package com.cit.vericash.backend.service.config;

import liquibase.integration.spring.SpringLiquibase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
//import org.springframework.web.context.annotation.ApplicationScope;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@org.springframework.context.annotation.Configuration
@ComponentScan
@PropertySource(value = {"file:${VERICASH_APIS_CONFIG}/${PROJECT}-config/services/shared-config/datasource.properties"}, ignoreResourceNotFound = false)
//@ApplicationScope
public class Configuration {
    private static DriverManagerDataSource driverManagerDataSource = null;

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource dataSource() {
        System.out.println("start datasource initialization...");
        if (driverManagerDataSource == null)
            driverManagerDataSource = new DriverManagerDataSource();
        return driverManagerDataSource;
    }

    @Autowired
    DataSource dataSource;

  /* @Bean
    public SpringLiquibase liquibase() {
        SpringLiquibase lb = new SpringLiquibase();
        lb.setDataSource(dataSource);
        Map<String, String> params = new HashMap<>();
        params.put("verbose", "true");
        lb.setChangeLogParameters(params);
        lb.setShouldRun(true);

        return lb;
    }*/
}
