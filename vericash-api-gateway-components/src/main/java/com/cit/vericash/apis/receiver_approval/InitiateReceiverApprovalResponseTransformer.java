package com.cit.vericash.apis.receiver_approval;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.receiver_approval.InitiateReceiverApprovalResponseTransformer")
public class InitiateReceiverApprovalResponseTransformer implements MuleResponseTransformer {

    public static final String APPROVAL_REQUEST_ID = "approvalRequestId";
    public static final String RECEIVER_REQUEST_APPROVAL_ID = "receiverRequestApprovalId";
    private static final Map<String, Object> result = new HashMap<>();
    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Object requestApprovalId = businessMessage.getDynamicPayload().getPayload().getAttribute(RECEIVER_REQUEST_APPROVAL_ID);
        if (requestApprovalId != null) {
            result.put(APPROVAL_REQUEST_ID, requestApprovalId);
        }
        return result;
    }
}
