package com.cit.vericash.apis.gettopupproducts;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.gettopupproducts.GetTopUpProductsResponseTransformer")
public class GetTopUpProductsResponseTransformer implements MuleResponseTransformer {
    public Object transform(BusinessMessage businessMessage) throws Exception {
        JSONObject json = new JSONObject();

        try {
            if (businessMessage.getSoftFields().containsKey("bills")) {
                json.put("Bills", businessMessage.getSoftFields().get("bills"));
            }
        } catch (Exception e) {
            throw e;
        }

        return json;
    }
}