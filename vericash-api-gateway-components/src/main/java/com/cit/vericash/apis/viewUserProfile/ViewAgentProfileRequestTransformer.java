package com.cit.vericash.apis.viewUserProfile;

import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.viewUserProfile.ViewAgentProfileRequestTransformer")
public class ViewAgentProfileRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

    }
}
