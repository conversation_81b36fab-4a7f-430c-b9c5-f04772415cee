<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">


    <changeSet failOnError="false" author="Zainab" id="addSecurityCommon1">
        <insert tableName="API_CHANNEL">
            <column name="ID" value="5"/>
            <column name="CHANNEL_NAME" value="sanlam"/>
            <column name="WALLET_SHORT_CODE" value="2020"/>
            <column name="ENABLED_CHANNEL" value="1"/>
        </insert>
        <insert tableName="API_SECURITY_IMPL">
            <column name="ID" value="18"/>
            <column name="CLASS_IMPL" value="com.cit.vericash.apis.security.authentication.mac.session.EncryptResponseHandler"/>
        </insert>
        <insert tableName="API_SECURITY_IMPL">
            <column name="ID" value="19"/>
            <column name="CLASS_IMPL" value="com.cit.vericash.apis.security.authentication.mac.session.RegisterMacHandler"/>
        </insert>
    </changeSet>
    <changeSet failOnError="false" author="Zainab" id="addSecurityForLookup1">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 2324, 1, 'request', 4, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 2324, 1, 'response', 18, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2324 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2324 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2324 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2324 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2324 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2324 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),13);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Zainab" id="addSecurityForDefaultCountryLookup1">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 2368, 1, 'request', 4, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 2368, 1, 'response', 18, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2368 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2368 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2368 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2368 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2368 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2368 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),13);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Zainab" id="addSecurityForCheckRegistrationStatus1">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 2369, 1, 'request', 4, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 2369, 1, 'response', 18, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2369 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2369 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2369 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2369 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2369 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2369 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),13);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Zainab" id="addSecurityForSANSendOTP1">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 2367, 1, 'request', 4, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 2367, 1, 'response', 18, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2367 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2367 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2367 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2367 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2367 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2367 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),13);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Zainab" id="addSecurityForSANValidateOTP1">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 2372, 1, 'request', 4, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 2372, 1, 'response', 18, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2372 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2372 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2372 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2372 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2372 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=2372 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),13);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Zainab" id="addSecurityForRegistration1">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 142501, 1, 'request', 4, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 142501, 1, 'response', 18, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142501 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142501 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142501 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142501 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142501 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142501 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),13);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Zainab" id="addSecurityForForgetCustomerPin1">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 129021, 1, 'request', 4, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 129021, 1, 'response', 18, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=129021 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=129021 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=129021 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=129021 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=129021 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=129021 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),13);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Zainab" id="addSecurityForLogin1">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 142601, 1, 'request', 4, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 142601, 1, 'response', 10, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 142601, 2, 'response', 18, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142601 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142601 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142601 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142601 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=10),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142601 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=10),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142601 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=10),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142601 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142601 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142601 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),13);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Zainab" id="addSecurityForRegisterUpdate1">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 142501, 1, 'response', 10, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142501 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=10),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142501 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=10),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=142501 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=10),13);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Zainab" id="addSecurityForDefultAfterLogin22">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 1, 'request', 5, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 1, 'response', 18, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE IS NULL and CHANNEL_IDD=5 and SECURITY_IMPL_ID=5),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE IS NULL and CHANNEL_IDD=5 and SECURITY_IMPL_ID=5),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE IS NULL and CHANNEL_IDD=5 and SECURITY_IMPL_ID=5),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE IS NULL and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE IS NULL and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE IS NULL and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),13);
        </sql>
    </changeSet>


    <changeSet failOnError="false" author="Zainab" id="addSecurityForOTPGeneration1">
        <sql>
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 150061, 1, 'request', 4, 1, 5);
            INSERT INTO API_CHANNEL_SECURITY_CONFIG (ID, API_CODE, "ORDER", SCOPE, SECURITY_IMPL_ID, ENABLED, CHANNEL_IDD) VALUES ((SELECT MAX(ID)+1 FROM API_CHANNEL_SECURITY_CONFIG), 150061, 1, 'response', 18, 1, 5);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=150061 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=150061 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=150061 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=4),13);
        </sql>
        <sql>
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=150061 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),11);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=150061 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),12);
            Insert into API_SECURITY_CONFIG_PARAMS (SECUIRTY_CONFIG_ID,SECURITY_PARAMS_ID) values ((SELECT ID FROM API_CHANNEL_SECURITY_CONFIG WHERE API_CODE=150061 and CHANNEL_IDD=5 and SECURITY_IMPL_ID=18),13);
        </sql>
    </changeSet>


    <changeSet failOnError="false" author="Zainab" id="addNewLookupForEnableSecurity1">

        <sql>
            Insert into PORTAL_LOOKUPS_CONFIG (ID,LOOKUP_NAME,LOOKUP_KEY,LOOKUP_QUERY,CACHEABLE) values ( (SELECT MAX(ID)+1 FROM PORTAL_LOOKUPS_CONFIG),'get security enable','get.security.enable','SELECT ENABLED_CHANNEL FROM API_CHANNEL WHERE UPPER (CHANNEL_NAME)= '${filters.getAttributeAsString('channelCode')}' AND UPPER (WALLET_SHORT_CODE)= '${filters.getAttributeAsString('walletShortCode')}',0);
            Insert into LOOKUP_FRAMEWORK_CONFIG (ID,CACHING_KEY_PATTERN,CACHING_TYPE,IS_CACHED_LOOKUP,LOOKUP_NAME,LOOKUP_TYPE,RESPONSE_TRANSFORMER_MAPPING,CUSTOME_RESPONSE_TRANSFORMER,CUSTOM_TRANSFORMER_CONFIG_FILE,ENDPOINT_NAME,SERVICE_NAME) values ( (SELECT MAX(ID)+1 FROM LOOKUP_FRAMEWORK_CONFIG),'0',0,0,'get.security.enable',0,null,null,null,null,null);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Zainab" id="addSchemaForNewChannel1">

        <sql>
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878788001','EnquireTransactionInfo.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('127011','B2B.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1320071','AgentLogin.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1444511','getCustomerDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787901','getTopUpOperators.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('135051','getTopUpProducts.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1320031','moneyDisbursement.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1320041','moneyIssuance.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1422141','purchaseByCustomer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787941','purchaseByAgent.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878788021','RedeemCustomerCashout.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11552','View-Agent-Profole.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878788011','RedeemCashout.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787861','cashout.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1425702','Validate-agent-api.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('192121','Lookup-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('113001','ActivateCustomer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787961','Display-Home-Icons.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878788901','activate-customer-by-agent-request-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787921','Get-Billers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11111','ReverseCashoutRedemption.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1455561','SetPinForTransactions.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11551','GetTransactionStatus.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('142501','SelfRegistration.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1400441','updateTransactionPin.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('142725261','External p2p.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('144441','RegisterCustomer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('192141','agentcashout.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('192131','customercashout.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1422081','CalculateFees.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1500601','BalanceInquiry.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11071','CashIn.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11081','OTCCashIn.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1920011','BillPaymentByCustomer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('192111','BillPaymentByAgent.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('14441','P2MTransfer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('141541','InternalP2P.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('13331','Complete_Cashout.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('12221','Initiate_Cash_Out _by _Customer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11061','Agent_Balance_Inquiry_Schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11091','CancelCashOut.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11101','CancelCashOut.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878788931','RedeemCashout.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787991','agent-mini-statement-request-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787931','forget-agent-password-request-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18899991','resend-activation-code-request-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787881','update-agent-password-request-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787871','update-customer-password-request-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('182351','generate-otp-request-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('117001','GetTransactionHistory.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1425703','Validate-user-api.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('192151','PricingModelSubscribtion.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881021','delete-payment-method-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881041','unfreeze-payment-method-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18889911','CreateUpdateGroupTransfer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890301','creditExpressValidation.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881091','NewFreezePaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890651','BECustomerFilterService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('198906941','GetCashPin.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881231','InquirePaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890751','BECustomersRejectTask.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890761','BECustomersShowTaskDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890811','BECustomersActionDeny.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('188881','NewCashIn.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('182341','Validate-Otp.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('194941','product_lookup.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878788991','InitiateOTPForUpdateMobile.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11131','InitiateForgetUserNameByConsumer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11161','AddPaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881061','InitiateOTPForSelfRegisteration.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11171','AddPaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881071','getSupportedPaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('14050671','trends.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('188819931','networth.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('***********','NewUNFreezePaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140001','getAllMovies.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140011','getAllShowtimesForMovie.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140031','purchaseMovieTicketBank.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140051','purchaseMovieTicketPrepaid.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140021','getAllEvents.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140041','purchaseEventBank.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140061','purchaseEventPrepaid.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('********','BECustomerValidateActivation.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('*********','NewViewPaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150501','GetCashPinAgent.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1555551','LookupFrameWork.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('********','BECustomersGetMyTasks.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('********','BECustomersUnBlock.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890771','SMEDeleteCustomerRole.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890801','SMEGrantCustomerRole.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('163661','TransferUsingAlias.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('199991','NewOTCCashIn.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787971','Get-CashOut.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787981','Get-CashOut.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('153281','generate-otp-request-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881011','update-customer-user-name-request-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881031','FreezePaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('142720051','AddBillToMyBills.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('142720061','DeleteBillFromMyBills.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('142720071','ValidateBillExisted.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('142720091','ValidateBillName.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140071','foodLoadPlasess.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('194961','AuthenticationPerService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140081','foodLoadRestaurants.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140091','foodLoadRestaurantsDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140101','foodPlaceOrderPreview.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140151','foodPlaceOrderBank.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('140161','foodPlaceOrderPrepaid.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11191','CustomerLogout.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1502301','GetAllowedBeneficiariesList.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('111151','validatePaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1777771','P2PSendMoney.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('********','BECustomersSMEActivate.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('********','BECustomerCheckExsistance.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890601','BECustomerLoadConfigration.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890691','BECustomersviewBECustomersandsearch.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890621','BECustomerAddPrimarySecondary.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('177771','OwnTransfer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881411','GetTransactionHistory.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('198906951','GetTransactionHistory.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('19090901','country_adel.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787951','Update-Customer-Info.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878788981','UpdateCustomerMobileNumber.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11151','Notify_VC_Card_Token.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881051','UpdateCardAlias.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('131021','ViewPaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('142720081','UpdateBillOfMyBills.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('142720031','GetMyBills.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('16666661','GhanaQrMasterPass.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('194951','Authentication.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881081','NewViewPaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881101','NewUNFreezePaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11181','UpdatePaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('19890561','getSupportedPaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881201','NewDeletePaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('198906931','getDestinationPaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881211','ResendSMSCashOutAgent.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881221','ResendSMSCashOutCust.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('166661','P2P.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18787881281','NewDeletePaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890661','BECustomerSetPinandPassword.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890701','SMEUpdateCustomerRole.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890781','BECustomersBlock.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('162661','TransferUsingOption.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2001','OcityGetFare.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2002','OcityGetFare.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2034','update-customer-email-request-schema.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2000','appMenus.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2061','ValidateMobileNumber.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2063','CreateSalaryDisbursement.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2249','ViewSalaryReceivers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2243','LinkCustomerProfilePicture.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2247','ProcessSalaryReceivers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2244','PersistSalaryDisbursementFile.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2256','DeleteRegisteredDeviceAPI.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2252','DeleteCustomerNotification.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2251','MarkCustomerNotificationasRead.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2257','GetRegisteredDevices.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2067','CreateBulkPayment.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2076','UpdateBulkPaymentReceiver.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2077','DeleteBulkPaymentReceiver.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2075','LoadBulkPaymentConfigurationsapi.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2241','DeleteSalaryDisbursement.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890401','LoadSalaryConfig.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2258','UpdateCustomerDeviceName.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2071','DeleteBulkPayment.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2074','BulkPaymentHistoryDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2073','BulkPaymentHistory.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2079','ViewSalaryReceivers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2260','AddBulkPaymentReceiver.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2238','SearchSalaryDisbursement.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2056','OcityPayFare.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2239','UpdateSalaryDisbursement.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2062','AddReceiver.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2240','UpdateReceiver.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2242','DeleteReceiver.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2248','SalaryDisbursementHistoryDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2246','SalaryDisbursementHistory.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2253','AddNewDevice.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2254','DisableRegisteredDeviceAPI.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2255','ActivateRegisteredDeviceAPI.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2250','ViewCustomerNotification.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2259','DeleteCustomerProfilePicture.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2068','SearchBulkPayment.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2070','UpdateBulkPayment.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2078','ValidateBulkPaymentReceivers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2080','ImportBulkPaymentReceivers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2264','DeleteRecurringTransactions.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2261','ViewRecurringTransactions.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2266','ViewMonthRecurringTransactions.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2268','CreateSchedulerService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('18890431','SalaryValidateReceivers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2270','RotatingSavingsHome.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2271','ViewAllMyOrganizedRotatingSavings.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2274','ViewRotatingSavingSummaryDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2275','ViewAllMyOrganizedRotatingSavingsHistory.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11152','QueryService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2276','CancelRotatingSaving.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2277','SubscribeRotatingSaving.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2279','MarkAsReadyRotatingSavings.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2281','SettleRotatingSavings.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2282','ScheduleEventsRotatingSaving.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2283','UnsubscribeRotatingSavings.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2285','MySubscriptioninlist.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2287','ConfirmUpdate.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2290','RejectRotatingSavingsMember.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2292','AssignRotatingSavingsMemberTurn.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2294','ReleaseDay.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2295','ReviewandConfirmChange.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('17777771','OwnTransfer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('250002','TransferMoneyByAlias.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('350003','TransferMoneyByDefault.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('198906961','SalaryTransaction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('198906971','BulkTransaction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('198906991','OTCCashInTransfer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('16001','AddVirtualCardpaymentmethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2267','ViewRecurringOccurrences.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2265','CancelCertainTransferInRecurringRransaction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2269','ViewBulkRecipientDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2272','ViewAllOpenForSubscriptionRotatingSavings.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2278','ViewFreeTurns.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2284','UpdateRotatingSavingService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2280','ReturnRotatingSavingstoNew.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2286','Rotatingsavingslookup.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2289','ApproveRotatingSavingsMember.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2291','ChangeRotatingSavingsMemberTurn.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2293','ReleaseAmounttowinner.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2288','PayContributionAmount.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150001','TransferMoney.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2072','AddBulkPaymentReceiver.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2297','AutomaticDeductionPayContributionAmount.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150011','AddBeneficiary.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150021','UpdateBeneficiary.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150031','DeleteBeneficiary.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150041','ViewBeneficiary.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150051','ExternalCashInTransfer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2302','ValidateOTP.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2304','ViewStatusDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2305','ViewMyPaymentHistory.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2308','ReviewAndUpdateView.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1422001','CalculateFeesForAddVirtualCard.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2311','GetLatestTransactionandActivitesAPI.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('889086','ViewDeletedVirtualCards.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2312','CalculateBulkFee.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2354','RepaymentReminderNotify.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2360','MarketingNotificationService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2318','ViewPaymentMethod.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11121','ForgetCustomerPassword.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('11141','ForgetCustomerUserName.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2346','ExpireTask.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2348','ValidateBusinessAccountAction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2349','ValidatePersonalAccountAction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150171','ValidateToken.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150151','InquiryCustomerDetailsWithIdentificationKey.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150161','registercustomerwithoutbvn.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150181','CreateNonUBABankAccount.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('135071','Get-Billers-By-CategoryId.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('135081','Get-Categories.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2296','Executebulktransferquickaction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('***********','PayBillPayment.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('********','ViewPaymentMethodDetaileswithbalance.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2321','Executesalarytransferquickaction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2357','LoanCalculator.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2358','ViewLoans.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2345','ValidateMobileNumberAddBEcustomer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2327','BECustomerActivation.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2356','ViewPendingApprovalHome.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2337','UnBlockBECustomerService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2338','Changerole.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2335','grantsuperuser.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2334','denysuperuser.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('100074','IntegrationFrameworkPOCMohanad.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2364','ApplyForLoan.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2326','BeCustomerActivationcodevalidation.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2273','SetupNewRotatingSaving.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2306','ViewListOfTurns.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2309','ReviewAndConfirmView.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2307','ManageMembers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('3001','ViewPayContributionSummary.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2313','ManageMemberTurnView.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('889085','ViewVirtualCard.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('142601','CustomerLogin.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2355','AutomaticRepaymentInstallmentNotify.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2315','RetryFailedBulkTransfer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2299','ExecuteBulkTransfer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2353','LendingNotificationService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2319','GETSchedulerType.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2320','GETSchedulerFrequency.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('123211','TestLookup.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2323','LookupBeforeLoginDynamic.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2324','LookupAfterLoginDynamic.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2331','ApproveTask.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2340','ViewPendingApprovalTasks.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2341','ViewApprovedTasks.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2342','ViewTasksHistory.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2343','ViewTasksDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2344','CancelTask.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2332','RejectTask.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2333','BECustomerManagerService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2347','SMEAppMenus.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('889091','supporteddistPaymentMethodperscreen.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('889090','supportedsourcePaymentMethodperscreen.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('889087','ViewPaymentMethodServiceperscreenforcustomerandsme.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('135061','Get-Billers-Bills.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150191','GetTransactionReceipt.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('1878787911','PayBillPayment.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150201','SelfRegisterAgent.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2339','ViewRoleDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('3002','CalculateBulkFeesquickaction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('9123213','ViewRoleDe123tails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2316','RetryAllFailedBulkTransfer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150081','GenerateOTPDynamic.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2359','ViewLoansDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2325','AddBECustomer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150071','GenerateOTPWithNoValidation.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2350','BECustomerActivationLandingPage.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2328','ViewAllBECustomersAction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2329','ViewPendingApprovalBECustomersAction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2330','SearchBECustomersAction.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2336','BlockBECustomerService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2351','ValidateBeRoleServiceAccessibility.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2365','BalanceInquiryCWG.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2363','CalculateFeesForApplyForLoan.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2366','Getcategories/products.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150091','ValidateOTPDynamic.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2361','ViewProductDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('889095','ExpiryDateLookups.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('889096','CommissionRedemption.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('150061','GenericOTPGeneration.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('129011','UpdateCustomer’sPIN.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2369','CheckRegistrationStatus.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2368','DefaultCountryLookup.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2370','InviteUser.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('129021','ForgetCustomerPin.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2371','ViewAcceptedInvitation.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2372','SANValidateOTP.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2385','AssignE-Voucher.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2373','RegistrationUsingQRCode.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2375','TransferE-Voucher.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2376','CheckReferralCodeExistence.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2382','ValidateCampaign.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2380','RedeemEvoucher.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2377','ViewActiveEvouchers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2381','ViewEvouchersHistory.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2383','SANActivateCustomer.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2384','ValidateE-Voucher.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2367','SANSendOTP.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2386','UpdateE-VoucherStatus.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2411','E-voucherGiftCardbyRedemptionCode.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2388','ReceivedVoucherReward.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('3003','AboutContent.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2379','PurchaseE-Voucher.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('3005','UpdateUserDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2389','ReceivedCashReward.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2392','QRSendRedemptionCodeForRegisteredUser.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2396','CheckKYCJobStatus.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2398','HomeScreenService.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2393','BillPaymentPurchaseAirtime.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2394','ValidateCustomerAml.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2390','UpgradeProfileRequest.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2395','BIllPaymentPurchaseBundles.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2399','GetAllOffers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2397','GetEvoucherDetails.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2402','ReferandEarn.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2401','GetQuickServices.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2403','GetAllServices.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2400','GetAllOffers.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2404','BIllPaymentPurchaseDSTV.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2405','BIllPaymentDSTVaccountinquiry.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('23100','BillPaymentPrepaidElectricity.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2406','BillPaymentPurchaseShowMax.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2407','MaxScanAPI.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2409','E-voucherGiftCard.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2408','AssignandRedeemGifts.json',5);
            Insert into API_CHANNEL_JSON_SCHEMA (API_CODE,FILE_NAME,CHANNEL_ID) values ('2410','GetCampaignIdStoreLocationPartnerReg.json',5);

        </sql>
    </changeSet>
</databaseChangeLog>