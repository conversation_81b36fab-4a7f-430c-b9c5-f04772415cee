package com.cit.vericash.apis.ocityAuthenticate;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.ocityAuthenticate.OcityAuthenticateResponseTransformer")
public class OcityAuthenticateResponseTransformer implements MuleResponseTransformer {
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    private String businessEntityNameKey = "business_Entity_Name";
    private String accountKey = "Account";

    public Object transform(BusinessMessage businessMessage) throws Exception {
        if (businessMessage.getSoftFields().containsKey("token")) {
            return businessMessage.getSoftFields().get("token");
        }
        return null;
    }
}
