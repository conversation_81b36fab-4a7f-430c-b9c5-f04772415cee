package com.cit.vericash.apis.updateagentpassword;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.updateagentpassword.UpdateAgentPasswordRequestTransformer")
public class UpdateAgentPasswordRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

    }
}
