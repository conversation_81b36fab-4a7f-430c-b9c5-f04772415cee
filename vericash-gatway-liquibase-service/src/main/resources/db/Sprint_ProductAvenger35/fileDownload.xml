<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="insert-services-paymentmethod-dynamic-id-848" author="<PERSON>">
        <preConditions onFail="CONTINUE">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM PRO_MI_NO_C.SERVICES_PAYMENTMETHOD WHERE ID = (SELECT MAX(ID) + 1 FROM PRO_MI_NO_C.SERVICES_PAYMENTMETHOD);
            </sqlCheck>
        </preConditions>
        <insert tableName="SERVICES_PAYMENTMETHOD" schemaName="PRO_MI_NO_C">
            <column name="ID" valueComputed="(SELECT MAX(ID) + 1 FROM PRO_MI_NO_C.SERVICES_PAYMENTMETHOD)"/>
            <column name="API_CODE" value="190961"/>
            <column name="PAYMENMETHODTYPE" value="848"/>
            <column name="LOOKUP_ENUM" value="2"/>
            <column name="GAR_STATUS" value="'1'"/>
            <column name="USER_TYPE" value="1"/>
            <column name="VAILDATE_PM" value="0"/>
        </insert>
    </changeSet>

    <changeSet id="insert-services-paymentmethod-842" author="Mohamed Orabi">
        <preConditions onFail="CONTINUE">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM PRO_MI_NO_C.SERVICES_PAYMENTMETHOD WHERE ID = (SELECT MAX(ID) + 1 FROM PRO_MI_NO_C.SERVICES_PAYMENTMETHOD);
            </sqlCheck>
        </preConditions>
        <insert tableName="SERVICES_PAYMENTMETHOD" schemaName="PRO_MI_NO_C">
            <column name="ID" valueComputed="(SELECT MAX(ID) + 1 FROM PRO_MI_NO_C.SERVICES_PAYMENTMETHOD)"/>
            <column name="API_CODE" value="190961"/>
            <column name="PAYMENMETHODTYPE" value="842"/>
            <column name="LOOKUP_ENUM" value="2"/>
            <column name="GAR_STATUS" value="'1'"/>
            <column name="USER_TYPE" value="1"/>
            <column name="VAILDATE_PM" value="0"/>
        </insert>
    </changeSet>

    <changeSet id="insert-services-paymentmethod-871" author="Mohamed Orabi">
        <preConditions onFail="CONTINUE">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM PRO_MI_NO_C.SERVICES_PAYMENTMETHOD WHERE ID = (SELECT MAX(ID) + 1 FROM PRO_MI_NO_C.SERVICES_PAYMENTMETHOD);
            </sqlCheck>
        </preConditions>
        <insert tableName="SERVICES_PAYMENTMETHOD" schemaName="PRO_MI_NO_C">
            <column name="ID" valueComputed="(SELECT MAX(ID) + 1 FROM PRO_MI_NO_C.SERVICES_PAYMENTMETHOD)"/>
            <column name="API_CODE" value="190961"/>
            <column name="PAYMENMETHODTYPE" value="871"/>
            <column name="LOOKUP_ENUM" value="2"/>
            <column name="GAR_STATUS" value="'1'"/>
            <column name="USER_TYPE" value="1"/>
            <column name="VAILDATE_PM" value="0"/>
        </insert>
    </changeSet>

</databaseChangeLog>
