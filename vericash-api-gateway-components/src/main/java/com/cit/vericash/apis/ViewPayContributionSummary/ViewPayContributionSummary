package com.cit.vericash.apis.ViewPayContributionSummary;


import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ParametersMap;
import com.cit.mpaymentapp.model.rotatingSavings.RotatingSavings;
import com.cit.service.commons.codeMapping.PaymentMethodDto;
import com.cit.service.commons.codeMapping.ServiceCodeMappingComponent;
import com.cit.vericash.api.commons.calculateFees.CalculateFeesService;
import com.cit.vericash.api.components.LoadPaymentMethodComponent;
import com.cit.vericash.api.components.PaymentMethodServicePreprationComponent;
import com.cit.vericash.api.components.SMEConfigLoaderComponent;
import com.cit.vericash.api.components.impl.LoadTransformWalletServiceComponent;
import com.cit.vericash.api.components.impl.PrepareTransformCommonsComponent;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.commons.util.WalletInfoLoader;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.security.authentication.AuthorizedValidationStep;
import com.cit.vericash.config.VericashBackendConfig;
import org.apache.commons.jxpath.JXPathContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Component
@Qualifier("com.cit.vericash.apis.ViewPayContributionSummary.ViewPayContributionSummary")
public class ViewPayContributionSummary implements VericashAction {


    private static final int CURRENT_TURN = 0;
    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    private CachableVericashApi vericashApiCache;

    @Autowired
    LoadTransformWalletServiceComponent loadTransformWalletServiceComponent;

    @Autowired
    PrepareTransformCommonsComponent prepareTransformCommonsComponent;

    @Autowired
    SMEConfigLoaderComponent smeConfigLoader;

    @Autowired
    LoadPaymentMethodComponent loadPaymentMethodComponent;

    @Autowired
    AuthorizedValidationStep authorizedValidationStep;

    @Autowired
    VericashBackendConfig config;

    @Autowired
    ServiceCodeMappingComponent serviceCodeMappingComponent;

    @Autowired
    PaymentMethodServicePreprationComponent paymentMethodServicePreprationComponent;

    @Autowired
    CalculateFeesService calculateFeeService;

    @Autowired
    WalletInfoLoader walletInfoLoader;
    private static String SERVICE_CODE = "878788150";
    private static String PAY_CONTRIBUTION_AMOUNT = "100038";
    @Override
    public Object process(Request request) throws Exception {
        Message message = request.getMessage();
        Long apiCode = 3004l;
        BusinessMessage businessMessage = getBusinessMessage(message,apiCode.toString());
        String child = serviceCodeMappingComponent.excute(PAY_CONTRIBUTION_AMOUNT,message);
        businessMessage.getParameters().put("servicesFees",child);
        BigDecimal feesRuleOutputModel = calculateFeeService.execute(businessMessage);
        //load source payment method from request
        return prepareResult(businessMessage,feesRuleOutputModel);
    }

    private Object prepareResult (BusinessMessage businessMessage  , BigDecimal fees){
        String walletShortCode = businessMessage.getHeader().getWalletShortCode();
        String currency = walletInfoLoader.loadWalletInfo(walletShortCode).getCurrency();
        Long amount = businessMessage.getParameters().getAttributeAsLong("contributionAmount");
        Map  <String,Object> result = new HashMap<>();
        Long rotatingSavingsId = businessMessage.getParameters().getAttributeAsLong("rotatingSavingsId");
        int requiredTurn = businessMessage.getParameters().getAttributeAsInteger("requiredTurn");
        int turn = businessMessage.getParameters().getAttributeAsInteger("turn");
        int turnToPay = getTurn(requiredTurn,turn,rotatingSavingsId);
        result.put("Contribution Amount:",amount + " "+ currency);
        result.put("Payment Turn:",turnToPay);
        result.put("Fees",fees);
        BigDecimal totalAmount = fees.add(BigDecimal.valueOf(amount));
        result.put("Total Amount",totalAmount);
        return result;
    }

    //load business message needed data for mule steps
    private BusinessMessage getBusinessMessage(Message message, String apiCode) throws Exception {
        Payload payload = message.getPayload();
        Long userId = message.getHeader().getAttributeAsLong("userId");
        Long walletId = message.getHeader().getAttributeAsLong("walletId");
        Long rotatingSavingsId = message.getPayload().getAttributeAsLong("rotatingSavingsId");

        setSourcePaymentMethod(message, payload);
        PaymentMethodDto destinationPaymentMethod = getDestinationPaymentMethod(payload,rotatingSavingsId,userId,walletId);
        setDestinationPaymentMethod(payload,destinationPaymentMethod);
        message.getHeader().setAttribute("apiCode", apiCode);
        message.setPayload(payload);

        //Set Business Message Attributes
        BusinessMessage businessMessage = transformBusinessMessage(message);
        Long businessId = getBusinessId();
        businessMessage.getServiceInfo().setId("" + businessId);

        //Set Wallet Attributes
        String walletShortCode = message.getHeader().getAttributeAsString("walletShortCode");
        businessMessage.getHeader().setWalletId(walletId);
        businessMessage.getHeader().setWalletShortCode(walletShortCode);
        businessMessage.getHeader().setUserId(userId);


        //Set Pay Load to Business Message Parameters
        ParametersMap parameters = new ParametersMap();
        payload.remove("PMTransferType");
        parameters.putAll(payload);
        System.out.println(parameters);

        businessMessage.setParameters(parameters);
        return businessMessage;
    }

    private int getTurn(int requiredTurn, int turn , Long rotatingSavingsId  ) {
        if (requiredTurn != CURRENT_TURN) {
            return turn;
        } else {
            turn = getCurrentTurn(rotatingSavingsId);
            return turn;
        }
    }

    private int getCurrentTurn(Long rotatingSavingsId) {
        String query = "SELECT CURRENT_TURN FROM ROTATING_SAVINGS rs WHERE rs.ID = "+ rotatingSavingsId;
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields.add("CURRENT_TURN");
        List<Record> records= connectionUtil.executeSelect(query, parameters, fields);
        int currentTurn = records.get(0).getValueAsInt("CURRENT_TURN");
        return currentTurn;
    }

    private void setDestinationPaymentMethod(Payload payload , PaymentMethodDto destinationPaymentMethod) {
        payload.setAttribute("destinationPaymentMethod", destinationPaymentMethod);
    }

    private PaymentMethodDto getDestinationPaymentMethod(Payload payload ,Long rotatingSavingsId,Long userId,Long walletId){
        String query = "SELECT CUSTOMER_GAR.GARID, " +
                "CUSTOMER_GAR.PAYMENT_METHOD_TYPE," +
                "CUSTOMER.CUSTOMER_FEE_PROFILE_ID," +
                "ROTATING_SAVINGS.CONTRIBUTION_AMOUNT FROM CUSTOMER_GAR" +
                " INNER JOIN PAYMENT_METHOD ON CUSTOMER_GAR.PAYMENT_METHOD_TYPE = PAYMENT_METHOD.ID" +
                " INNER JOIN ROTATING_SAVINGS ON ROTATING_SAVINGS.ORGANIZER = CUSTOMER_GAR.CUSTOMER_ID " +
                " INNER JOIN CUSTOMER ON CUSTOMER.CUSTOMER_ID = CUSTOMER_GAR.CUSTOMER_ID"+
                " WHERE PAYMENT_METHOD.TYPE = 7" +
                " AND PAYMENT_METHOD.BUSINESS_ENTITY_ID = " + walletId +
                " AND ROTATING_SAVINGS.ID = "+rotatingSavingsId+
                " AND CUSTOMER.CUSTOMER_ID = "+userId;

        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields.add("GARID");
        fields.add("PAYMENT_METHOD_TYPE");
        fields.add("CUSTOMER_FEE_PROFILE_ID");
        fields.add("CONTRIBUTION_AMOUNT");
        List<Record> records= connectionUtil.executeSelect(query, parameters, fields);
        Long paymentMethodCode = records.get(0).getValueAsLong("GARID");
        Long paymentMethodType = records.get(0).getValueAsLong("PAYMENT_METHOD_TYPE");
        Long customerFeeProfileId = records.get(0).getValueAsLong("CUSTOMER_FEE_PROFILE_ID");
        Long contributionAmount = records.get(0).getValueAsLong("CONTRIBUTION_AMOUNT");
        PaymentMethodDto destinationPaymentMethod = new PaymentMethodDto();
        destinationPaymentMethod.setPaymentMethodType(paymentMethodType+"");
        destinationPaymentMethod.setPaymentMethodCode(paymentMethodCode);
        setContriubtionAmountAndFeeProfile(payload,contributionAmount,customerFeeProfileId);
        return destinationPaymentMethod;
    }

    private void setContriubtionAmountAndFeeProfile(Payload payload , Long contributionAmount, Long customerFeeProfileId) {
        payload.setAttribute("contributionAmount", contributionAmount);
        payload.setAttribute("customerFeeProfileId", customerFeeProfileId);
    }


    private void setSourcePaymentMethod(Message message, Payload payload) {
        LinkedHashMap<String, LinkedHashMap<String, Integer>> sourcePaymentMethodMap = (LinkedHashMap<String, LinkedHashMap<String, Integer>>) message.getPayload().getAttribute("sourcePaymentMethod");

        String sourcePaymentMethodType = String.valueOf(sourcePaymentMethodMap.get("paymentMethodType"));
        Long sourcePaymentMethodCode = Long.valueOf(String.valueOf(sourcePaymentMethodMap.get("paymentMethodCode")));
        PaymentMethodDto sourcePaymentMethod = new PaymentMethodDto();
        sourcePaymentMethod.setPaymentMethodType(sourcePaymentMethodType);
        sourcePaymentMethod.setPaymentMethodCode(sourcePaymentMethodCode);
        payload.setAttribute("sourcePaymentMethod", sourcePaymentMethod);
    }

    public final BusinessMessage transformBusinessMessage(Message message) throws Exception {
        BusinessMessage businessMessage = null;

        try {
            Long apiCode = message.getHeader().getAttributeAsLong("apiCode");
            VericashAPI vericashAPI = vericashApiCache.getVericashAPI(apiCode);

            businessMessage = new BusinessMessage();
            JXPathContext targetObject = JXPathContext.newContext(businessMessage);

            smeConfigLoader.prepareSMEBusinessMessage(message, targetObject);
            loadPaymentMethodComponent.loadPaymentMethodUsingOptions(message, targetObject);
            paymentMethodServicePreprationComponent.LoadPaymentMethodsTypes(message, vericashAPI);
            loadTransformWalletServiceComponent.loadTransformWalletService(vericashAPI, null, message, targetObject, businessMessage);
            prepareTransformCommonsComponent.prepareTransformationToBusinessMessage(message, targetObject);
        } catch (Exception e) {
            throw e;
        }
        return businessMessage;
    }

    private Long getBusinessId() {
        String query = "SELECT ID FROM BUSINESS_SERVICE_CONFIG WHERE NAME= 'Calculate Fees Service'";
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields.add("ID");
        List<Record> records = connectionUtil.executeSelect(query, parameters, fields);
        Long id = records.get(0).getValueAsLong("ID");
        return id;
    }
}
