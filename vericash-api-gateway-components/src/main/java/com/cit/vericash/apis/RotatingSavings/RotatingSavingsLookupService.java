package com.cit.vericash.apis.RotatingSavings;

import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.quick.actions.QueryServiceComponent;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.RotatingSavingsLookupService")
public class RotatingSavingsLookupService implements VericashAction {

    @Autowired
    QueryServiceComponent queryServiceComponent;

    @Override
    public Object process(Request request) throws Exception {
        //Retrieve apiCode from header and add it to the payload
        String apiCode = request.getMessage().getHeader().getAttributeAsString("apiCode");
        Payload payload = new Payload();
        payload.setAttribute("apiCode", apiCode);
        request.getMessage().setPayload(payload);
        //Call query service
        List<Record> recordsList = (List<Record>) queryServiceComponent.process(request);
        Map<Integer, Object> recordsMap = new HashMap<Integer, Object>();
        Integer walletId;
        String uiName = "";
        JSONObject json = new JSONObject();
        for (int i = 0; i < recordsList.size(); i++) {
            walletId = recordsList.get(i).getValueAsBigDecimal("WALLET_ID").intValue();
            uiName = recordsList.get(i).getValueAsString("UI_NAME");
            recordsMap.put(walletId, uiName);
        }
        json.putAll(recordsMap);
        return json;
    }
}