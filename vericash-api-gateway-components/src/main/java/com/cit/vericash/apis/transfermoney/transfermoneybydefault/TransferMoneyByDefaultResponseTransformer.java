package com.cit.vericash.apis.transfermoney.transfermoneybydefault;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ParametersMap;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.receipt.PaymentMethodAccountIdentifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
public class TransferMoneyByDefaultResponseTransformer implements MuleResponseTransformer {
    @Autowired
    PaymentMethodAccountIdentifier paymentMethodAccountIdentifier;

    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, Object> result = new HashMap<String, Object>();
        String receiverIdentifier = "";

        if (Objects.isNull(businessMessage.getDestinationPaymentMethod().getDynamicGroup())) {
            receiverIdentifier = businessMessage.getPrimaryReceiverInfo().getMsisdn();
        } else {
            receiverIdentifier = paymentMethodAccountIdentifier.getPaymentMethodAccountNumber
                    (businessMessage.getDestinationPaymentMethod().getPaymentMethodType(),
                            businessMessage.getDestinationPaymentMethod().getDynamicGroup().getInputParametersMap(),
                            null, businessMessage.getDestinationPaymentMethod().getPaymentMethodCode()
                    );
        }
        ParametersMap newParametersMap = businessMessage.getParameters();
        newParametersMap.put("Receiver_Identifier", receiverIdentifier);
        businessMessage.setParameters(newParametersMap);
        return result;
    }
}
