package com.cit.vericash.apis.cancelLoanApplication;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
@Component
@Qualifier("com.cit.vericash.apis.cancelLoanApplication.CancelApplicationRequestTransformer")
public class CancelApplicationRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    public static final String sourcePaymentMethod="sourcePaymentMethod";
    public static final String destinationPaymentMethod="destinationPaymentMethod";

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
//
////		try {
//////			cacheableFieldParameters = CacheableFieldParametersImpl.getInstance();
////		//	String ownerTypeKey = cacheableFieldParameters.getRequestParameters(OWNERTYPE);
////			String genderKey = cacheableFieldParameters.getRequestParameters(GENDER);
////			String gender = message.getPayload().getAttributeAsString(genderKey);
////		//	String ownerTypeString = message.getPayload().getAttributeAsString(ownerTypeKey);
////			Gender genderEnum = EnumTransformer.convertGender(gender);
////			message.getPayload().put(genderKey, genderEnum);
////
////
////		} catch (Exception e) {
////			throw e;
////		}
    }
}
