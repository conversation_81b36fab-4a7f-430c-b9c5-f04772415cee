<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet failOnError="true" author="michael" id="create-table-frequent-config">
        <sql>
            CREATE TABLE FREQUENT_CONFIG (
            ID INT PRIMARY KEY AUTO_INCREMENT,       -- UNIQUE IDENTIFIER FOR EACH RECORD
            API_CODE VARCHAR(50) NOT NULL,           -- CODE ASSOCIATED WITH THE API
            OPERATOR_ID INT NOT NULL,                -- IDENTIFIER FOR THE OPERATOR
            AMOUNT DECIMAL(10, 2) DEFAULT 0.00,      -- AMOUNT, DEFAULTING TO 0.00
            STATUS TINYINT DEFAULT 1,                -- STATUS OF THE ENTRY, DEFAULT IS 1 (ACTIVE)
            IS_DELETED BOOLEAN DEFAULT FALSE,        -- INDICATES IF THE ENTRY IS DELETED
            IS_AMOUNT_ENABLED BOOLEAN DEFAULT TRUE   -- INDICATES IF THE AMOUNT IS ENABLED
            )
        </sql>
    </changeSet>
</databaseChangeLog>