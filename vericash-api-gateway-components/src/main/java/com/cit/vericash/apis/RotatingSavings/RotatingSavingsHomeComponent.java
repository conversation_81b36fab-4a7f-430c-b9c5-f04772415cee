package com.cit.vericash.apis.RotatingSavings;

import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.commons.dto.MyOrganizedRotatingSavingsDTO;
import com.cit.vericash.api.commons.dto.MySubscriptionInRotatingSavingsDTO;
import com.cit.vericash.api.commons.dto.OpenForSubscriptionRotatingSavingsDTO;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.RotatingSavingsHomeComponent")
public class RotatingSavingsHomeComponent implements VericashAction {
    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    @Autowired
    OpenForSubscriptionListService openForSubscriptionListService;

    @Autowired
    MySubscriptionInListService mySubscriptionInListService;

    @Autowired
    MyOrganizedListService myOrganizedListService;

    ArrayList<OpenForSubscriptionRotatingSavingsDTO> openForSubscription;
    ArrayList<MyOrganizedRotatingSavingsDTO> myOrganized;
    ArrayList<MySubscriptionInRotatingSavingsDTO> mySubscriptionsIn;
    HashMap<String, ArrayList> result = new HashMap<>();
    Integer status;

    @Override
    public Object process(Request request) throws Exception {
        openForSubscription = new ArrayList<>();
        myOrganized = new ArrayList<>();
        mySubscriptionsIn = new ArrayList<>();
        Integer customerId = request.getMessage().getPayload().getAttributeAsInteger("customerId");
        if (customerId != null) {
            openForSubscription = openForSubscriptionListService.getOpenForSubscriptionList(false, customerId);
            myOrganized = myOrganizedListService.getMyOrganizedList(false, customerId);
            mySubscriptionsIn = mySubscriptionInListService.getMySubscriptionInList(customerId);
            result.put("openForSubscription", openForSubscription);
            result.put("myOrganized", myOrganized);
            result.put("mySubscriptionsIn", mySubscriptionsIn);
        } else {
            String errorMessage = propertyLoaderComponent.getPropertyAsString("error.rotating.savings.missing.id", "application", "application");
            throw new GeneralFailureException(errorMessage);
        }
        return result;
    }
}
