getReceiverRequestApproval=SELECT RRA.ID AS REQUEST_ID,\
    RRA.CREATION_DATE AS REQUEST_DATE,\
    RRA.AMOUNT AS AMOUNT,\
    RRA.EXPIRY_DATE AS EXPIRY_DATE,\
    RRA.API_CODE AS API_CODE,\
    rras.ID AS STATUS_CODE,\
    rras.STATUS_NAME AS STATUS_NAME,\
    c.MSISDN AS MSISDN,\
    RRA.CORRELATION_ID AS CORRELATION_ID, \
    CONCAT(COALESCE(c.FIRST_NAME, ''), CONCAT(' ', COALESCE(c.LAST_NAME, ''))) AS FULL_NAME \
    FROM RECEIVER_REQUEST_APPROVAL rra \
    LEFT OUTER JOIN RECEIVER_REQUEST_APPROVAL_STATUS rras ON rras.ID = rra.STATUS \
    LEFT OUTER JOIN CUSTOMER c ON c.CUSTOMER_ID = RRA.SENDER_CUSTOMER_ID \
    WHERE rra.id = ? \
    AND RRA.RECEIVER_CUSTOMER_ID = ? \
    AND RRA.STATUS = ?

updateStatus=UPDATE RECEIVER_REQUEST_APPROVAL rra SET RRA.STATUS = ?, rra.LAST_UPDATE_DATE = CURRENT_DATE WHERE RRA.id = ?

respondToRequest=UPDATE RECEIVER_REQUEST_APPROVAL rra SET rra.STATUS = ?, rra.LAST_UPDATE_DATE = CURRENT_DATE WHERE rra.ID = ?