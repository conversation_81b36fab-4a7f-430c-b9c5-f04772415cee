package com.cit.vericash.util;


import com.cit.vericash.printpdf.PDFData;
import lombok.RequiredArgsConstructor;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.io.RandomAccessReadBufferedFile;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.io.*;
import java.nio.file.Path;
import java.util.List;

@Service
@RequiredArgsConstructor
public class PdfGenerator {
    private final ConfigFileResolver configFileResolver;
    public static final String FONTS = "fonts";
    public static final String POPPINS = "Poppins";
    public static final String POPPINS_REGULAR_TTF = "Poppins-Regular.ttf";
    public static final Color GREEN_COLOR = new Color(71, 202, 100, 1);
    public static final Color GRAY_COLOR = new Color(99, 93, 93, 1);
    public static final Color LIGHT_GRAY_COLOR = new Color(217, 217, 217, 1);
    public static final String Green = "Green";
    public static final String Red = "Red";
    public PDDocument appendTextToPDF(List<PDFData> data, String PDFPath){
        PDDocument document;
        try
        {
            document = Loader.loadPDF(new RandomAccessReadBufferedFile(PDFPath));
            PDPage page = document.getPage(0);

            PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true, true);
            PDType0Font customFont = PDType0Font.load(document, Path.of(configFileResolver.getVericashRootFolder(), FONTS, POPPINS, POPPINS_REGULAR_TTF).toFile());
            drawTable(page, contentStream, data, customFont);
            contentStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return document;
    }

    /**
     * @param page page to write to
     * @param contentStream stream to write
     * @param data          a map containing the table data
     * @throws IOException throws io exception
     */
    private void drawTable(PDPage page, PDPageContentStream contentStream,
                           List<PDFData> data, PDFont valueFont) throws IOException {
        final int rows = data.size();
        final float rowHeight = 30f;
        final float tableWidth = page.getMediaBox().getWidth() - 50;
        final float cellMargin = 3f;
        final float padding = 17f;
        final float y = 510f;
        final float margin = 25f;
        final int fontSize = 16;

        contentStream.setStrokingColor(LIGHT_GRAY_COLOR);
        contentStream.setFont(valueFont, fontSize);

        float nexty = y - rowHeight - padding;
        for (int i = 0; i < rows; i++) {
            contentStream.moveTo(margin + 2, nexty - cellMargin);
            contentStream.lineTo(margin + tableWidth - 4, nexty - cellMargin);
            contentStream.stroke();
            nexty -= rowHeight;
        }

        float textx = margin + cellMargin;
        float texty = y - 25 - padding;
        for (PDFData entry : data) {

            String key = entry.getKey();
            contentStream.beginText();
            contentStream.setNonStrokingColor(GRAY_COLOR);
            contentStream.newLineAtOffset(textx, texty);
            contentStream.showText(key);
            contentStream.endText();

            String value = entry.getValue();

            if (entry.getColor().equals(Green)){
                contentStream.setNonStrokingColor(GREEN_COLOR);
            }else if (entry.getColor().equals(Red)){
                contentStream.setNonStrokingColor(Color.RED);
            }else{
                contentStream.setNonStrokingColor(Color.BLACK);
            }
            float valueTextx = margin + tableWidth - cellMargin - (valueFont.getStringWidth(value) / 1000 * fontSize);
            contentStream.beginText();
            contentStream.newLineAtOffset(valueTextx, texty);
            contentStream.showText(value);
            contentStream.endText();

            texty -= rowHeight;
        }
    }
}
