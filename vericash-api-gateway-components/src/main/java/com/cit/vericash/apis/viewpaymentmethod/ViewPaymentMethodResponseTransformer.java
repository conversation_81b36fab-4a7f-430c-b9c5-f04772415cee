package com.cit.vericash.apis.viewpaymentmethod;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.TreeMap;

@Component
@Qualifier("com.cit.vericash.apis.viewpaymentmethod.ViewPaymentMethodResponseTransformer")
public class ViewPaymentMethodResponseTransformer implements MuleResponseTransformer {


    public Object transform(BusinessMessage businessMessage) throws Exception {

        //Map<String, Object> result = new HashMap<String, Object>();
        Map<String, Object> result = new TreeMap<String, Object>();

		/*List<PaymentMethodDetail> paymentDetails = new LinkedList<PaymentMethodDetail>();
		paymentDetails=(List<PaymentMethodDetail>) businessMessage.getSoftFields().get("paymentMethods");
		Iterator iterator=paymentDetails.iterator();
		int i=0;
		while(iterator.hasNext()){
			businessMessage.getSoftFields().put("paymentMethods"+i+"", iterator.next());
			result.put("paymentMethods "+i+" Bank SchemeCode : ",((PaymentMethodDetail)businessMessage.getSoftFields().get("paymentMethods"+i+"")).getBank().getSchemeCode());
			result.put("paymentMethods "+i+" Card SchemeCode : ",((PaymentMethodDetail)businessMessage.getSoftFields().get("paymentMethods"+i+"")).getCard().getSchemeCode());

			i++;
		}*/
        if (businessMessage.getSoftFields().get("paymentMethods") != null) {
            result.put("paymentMethods", businessMessage.getSoftFields().get("paymentMethods"));
        }

        return result;
    }
}


