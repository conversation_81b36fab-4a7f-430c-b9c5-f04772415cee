package com.cit.vericash.apis.RotatingSavings;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.commons.dto.MyOrganizedRotatingSavingsDTO;
import com.cit.vericash.api.commons.dto.MySubscriptionInRotatingSavingsDTO;
import com.cit.vericash.api.commons.dto.SimpleGenericDTO;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.MyOrganizedListService")
public class MyOrganizedListService {
    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    @Autowired
    MyOrganizedAllowedOptionsService myOrganizedAllowedOptionsService;

    ArrayList<MyOrganizedRotatingSavingsDTO> myOrganizedRotatingSavingsDTOArrayList;
    Integer numberOfMembers;
    Integer alreadyMember;
    Integer status;
    Date subscriptionStartDate;
    Date subscriptionEndDate;
    List<Parameter> parameters = new ArrayList<>();
    private Integer customerId;

    public ArrayList<MyOrganizedRotatingSavingsDTO> getMyOrganizedList(boolean viewAll, Integer customerId) throws Exception {
        myOrganizedRotatingSavingsDTOArrayList = new ArrayList<MyOrganizedRotatingSavingsDTO>();
        String rotatingSavingsQuery = null;
        List<String> fields = new ArrayList<String>();
        setFields(fields);
        Integer rotatingSavingsNum = propertyLoaderComponent.getPropertyAsInteger("last.n.rotating.savings", "application", "application");
        this.customerId = customerId;
        if (customerId == null) {
            String errorMessage = propertyLoaderComponent.getPropertyAsString("error.rotating.savings.missing.id", "application", "application");
            throw new GeneralFailureException(errorMessage);
        }
        if (customerId != null && !viewAll) {
            rotatingSavingsQuery = ServiceQueryEngine.getQueryStringToExecute("getMyOrganizedList", this.getClass(), String.valueOf(customerId), String.valueOf(rotatingSavingsNum));
        } else if (customerId != null && viewAll) {
            rotatingSavingsQuery = ServiceQueryEngine.getQueryStringToExecute("rotatingSavingsQuery_elseif", this.getClass(), String.valueOf(customerId));
        }
        List<Record> rotatingSavings = connectionUtil.executeSelect(rotatingSavingsQuery, parameters, fields);
        Record rotatingSavingsRecord = null;
        for (Record rotatingSaving : rotatingSavings) {
            rotatingSavingsRecord = rotatingSaving;
            MyOrganizedRotatingSavingsDTO myOrganizedRotatingSavingsDTO = buildRotatingSavings(rotatingSavingsRecord);
            if (myOrganizedRotatingSavingsDTO != null) {
                myOrganizedRotatingSavingsDTOArrayList.add(myOrganizedRotatingSavingsDTO);
            }
        }
        return myOrganizedRotatingSavingsDTOArrayList;
    }

    public MyOrganizedRotatingSavingsDTO buildRotatingSavings(Record record) throws Exception {
        MyOrganizedRotatingSavingsDTO myOrganizedRotatingSavingsDTO = new MyOrganizedRotatingSavingsDTO();
        Integer rotatingSavingsId = record.getValueAsInt("ID");
        SimpleGenericDTO simpleGenericDTO = new SimpleGenericDTO();
        StringBuilder alreadyMemberQuery = null;
        String numberOfMembersQuery = null;

        List<Record> numberOfMembersResult;
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("buildRotatingSavings", this.getClass(), String.valueOf(this.customerId), String.valueOf(rotatingSavingsId));
        List<String> alreadyMemberFields = new ArrayList<String>();
        alreadyMemberFields = setMembersNumFields(alreadyMemberFields);
        List<Record> alreadyMemberRecords;
        alreadyMemberRecords = connectionUtil.executeSelect(sqlQuery, parameters, alreadyMemberFields);

        List<String> numberOfMembersFields = new ArrayList<String>();
        numberOfMembersFields = setMembersNumFields(numberOfMembersFields);

        numberOfMembersQuery = ServiceQueryEngine.getQueryStringToExecute("numberOfMembersQuery", this.getClass(), String.valueOf(rotatingSavingsId));

        numberOfMembersResult = connectionUtil.executeSelect(numberOfMembersQuery, parameters, numberOfMembersFields);

        if (record.getValueAsBigDecimal("ID") != null)
            myOrganizedRotatingSavingsDTO.setId(record.getValueAsBigDecimal("ID").intValue());

        if (record.getValueAsString("CURRENCY") != null && record.getValueAsInt("TOTAL_AMOUNT") != null)
            myOrganizedRotatingSavingsDTO.setTotalAmount(record.getValueAsString("CURRENCY") + " " + record.getValueAsInt("TOTAL_AMOUNT"));

        if (record.getValueAsDate("SUBSCRIPTION_START_DATE") != null) {
            myOrganizedRotatingSavingsDTO.setFrom(record.getValueAsDate("SUBSCRIPTION_START_DATE"));
            subscriptionStartDate = record.getValueAsDate("SUBSCRIPTION_START_DATE");
        }
        if (record.getValueAsDate("SUBSCRIPTION_END_DATE") != null) {
            subscriptionEndDate = record.getValueAsDate("SUBSCRIPTION_END_DATE");
            myOrganizedRotatingSavingsDTO.setSubscriptionEndDate(subscriptionEndDate);
        }

        if (record.getValueAsDate("END_DATE") != null) {
            subscriptionEndDate = record.getValueAsDate("END_DATE");
            myOrganizedRotatingSavingsDTO.setTo(subscriptionEndDate);
        }

        if (record.getValueAsBigDecimal("CONTRIBUTION_AMOUNT") != null && record.getValueAsString("CURRENCY") != null && record.getValueAsString("RSF_NAME") != null) {
            String contributionAmount = record.getValueAsString("CURRENCY") + " " + record.getValueAsBigDecimal("CONTRIBUTION_AMOUNT") + "/" + " " + record.getValueAsString("RSF_NAME").substring(0, record.getValueAsString("RSF_NAME").toCharArray().length - 2);
            myOrganizedRotatingSavingsDTO.setContributionAmount(contributionAmount);
        }
        if (record.getValueAsInt("NUMBER_MEMBERS") != null)
            myOrganizedRotatingSavingsDTO.setMembers(numberOfMembers + " / " + record.getValueAsInt("NUMBER_MEMBERS"));
        if (record.getValueAsBigDecimal("STATUS") != null) {
            status = record.getValueAsBigDecimal("STATUS").intValue();
            simpleGenericDTO.setName(MySubscriptionInRotatingSavingsDTO.RotatingSavingStatus.values()[status].name());
            simpleGenericDTO.setId(status);
            myOrganizedRotatingSavingsDTO.setRotatingSavingStatus(simpleGenericDTO);
        }
        List<SimpleGenericDTO> allowedOptionsList = myOrganizedAllowedOptionsService.getAllowedOptionsList(status, subscriptionStartDate, subscriptionEndDate);
        myOrganizedRotatingSavingsDTO.setAllowedOptions(allowedOptionsList);

        Record numberOfMembersRecord = null;

        for (int i = 0; i < numberOfMembersResult.size(); i++) {
            numberOfMembersRecord = numberOfMembersResult.get(i);
            if (numberOfMembersRecord.getValueAsBigDecimal("NUM") != null) {
                numberOfMembers = numberOfMembersRecord.getValueAsBigDecimal("NUM").intValue();
            }
        }

        Record alreadyMemberRecord = null;
        for (int i = 0; i < alreadyMemberRecords.size(); i++) {
            alreadyMemberRecord = alreadyMemberRecords.get(i);
            if (alreadyMemberRecord.getValueAsInt("NUM") != null) {
                alreadyMember = alreadyMemberRecord.getValueAsInt("NUM");
            }
        }
        if (alreadyMember != null) {
            if (alreadyMember > 0)
                myOrganizedRotatingSavingsDTO.setAlreadyMember(true);
        }

        return myOrganizedRotatingSavingsDTO;
    }


    private List<String> setMembersNumFields(List<String> fields) {
        fields.add("NUM");
        return fields;
    }

    private List<String> setFields(List<String> fields) {
        fields.add("CURRENCY");
        fields.add("ID");
        fields.add("TOTAL_AMOUNT");
        fields.add("SUBSCRIPTION_START_DATE");
        fields.add("SUBSCRIPTION_END_DATE");
        fields.add("CONTRIBUTION_AMOUNT");
        fields.add("CONTRIBUTION_FREQUENCY");
        fields.add("STATUS");
        fields.add("NUMBER_MEMBERS");
        fields.add("RSF_NAME");
        fields.add("END_DATE");
        return fields;
    }

}
