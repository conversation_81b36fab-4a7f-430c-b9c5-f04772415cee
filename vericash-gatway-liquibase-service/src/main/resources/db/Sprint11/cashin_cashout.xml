<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">


    <changeSet failOnError="false" author="Abubakr" id="AddCashInWalletToWalletService">
        <sql>
            INSERT INTO SERVICE_CONFIG_MAP(SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)VALUES(33331, 0, 'Cash In WalletToWallet', 0, NULL, NULL, NULL, '33331', 1, 0, 0, 0, 0, NULL, NULL, NULL, 3333, 0);
            INSERT INTO BUSINESS_SERVICE_CONFIG(ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION)VALUES(**************, 0, 0, 1, 0, 0, 'Cash In WalletToWallet', 33331, 3, 2421, NULL, 1, NULL, NULL);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="AddCashInWalletToGTPService">
        <sql>
            INSERT INTO SERVICE_CONFIG_MAP(SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)VALUES(33332, 0, 'Cash In WalletToGTP', 0, NULL, NULL, NULL, '33332', 1, 0, 0, 0, 0, NULL, NULL, NULL, 3333, 0);
            INSERT INTO SANLAM_TEST.BUSINESS_SERVICE_CONFIG(ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION)VALUES(***************, 0, 0, 1, 0, 0, 'Cash In WalletToGTP', 33332, 3, 2421, NULL, 1, NULL, NULL);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="CashINInputParams">
        <sql>
            INSERT INTO INPUT_PARAM_SERVICE_DEF(ID, WALLET_SHORT_CODE, SERVICE_TYPE_ID)VALUES(100, '2020', 3333);
            INSERT INTO INPUT_PARAM_GROUP_WALLET(ID, WALLET_SHORT_CODE, GROUP_DEF_ID)VALUES(43359, '2020', 43358);
            INSERT INTO INPUT_PARAM_SERVICE_GROUP(ID, GROUP_WALLET_ID, SERVICE_DEF_ID)VALUES(100, 43359, 100);
            INSERT INTO INPUT_PARAM_GROUP_VALIDATION(ID, CUSTOM_VALIDATION_BEAN, GROUP_VALIDATION_TYPE, INPUT_PARAM_SERVICE_GROUP_ID)VALUES(100, NULL, 0, 100);
        </sql>
    </changeSet>


    <changeSet failOnError="false" author="Abubakr" id="AddCashOutWalletToWalletService">
        <sql>
            INSERT INTO SERVICE_CONFIG_MAP
            (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES(33341, 0, 'Cash Out WalletToWallet', 0, NULL, NULL, NULL, '33341', 1, 0, 0, 0, 0, NULL, NULL, NULL, 3334, 0);

            INSERT INTO BUSINESS_SERVICE_CONFIG
            (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION)
            VALUES(***************, 0, 0, 1, 0, 0, 'Cash Out WalletToWallet', 33341, 3, 2421, NULL, 1, NULL, NULL);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="AddCashOutWalletToGTPService">
        <sql>
            INSERT INTO SERVICE_CONFIG_MAP
            (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES(33342, 0, 'Cash Out WalletToGTP', 0, NULL, NULL, NULL, '33342', 1, 0, 0, 0, 0, NULL, NULL, NULL, 3334, 0);

            INSERT INTO BUSINESS_SERVICE_CONFIG
            (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION)
            VALUES(***************, 0, 0, 1, 0, 0, 'Cash Out WalletToGTP', 33342, 3, 2421, NULL, 1, NULL, NULL);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="CashOutInputParams">
        <sql>
            INSERT INTO INPUT_PARAM_SERVICE_DEF
                (ID, WALLET_SHORT_CODE, SERVICE_TYPE_ID)
            VALUES (101, '2020', 3334);

            INSERT INTO INPUT_PARAM_GROUP_WALLET
                (ID, WALLET_SHORT_CODE, GROUP_DEF_ID)
            VALUES (43359, '2020', 43358);


            INSERT INTO INPUT_PARAM_SERVICE_GROUP
                (ID, GROUP_WALLET_ID, SERVICE_DEF_ID)
            VALUES (101, 43359, 101);

            INSERT INTO INPUT_PARAM_GROUP_VALIDATION
            (ID, CUSTOM_VALIDATION_BEAN, GROUP_VALIDATION_TYPE, INPUT_PARAM_SERVICE_GROUP_ID)
            VALUES (101, NULL, 0, 101);

        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="CashOutNewTransactionWalletToWallet">
        <sql>
            INSERT INTO TRANSACTION_TYPE (TRANSACTION_TYPE_ID, IS_GENERIC_SERVICE, TRANSACTION_TYPE_NAME, ORGANIZATION_ID, TRANSACTION_TYPE_CODE, CODE, ORGANIZATION) VALUES(131, 0, 'cash_out_wallet_to_wallet', NULL, 'cash_out_wallet_to_wallet', NULL, NULL);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="CashOutNewTransactionWalletToWalletSummary">
        <sql>
            INSERT INTO TRANS_DEF_SUMMARY (TRANS_DEF_SUMMARY_ID, DEFAULT_DEFINITION, IS_MULTI_WALLET, TRANSACTION_NAME, ORGANIZATION_ID, BUSINESS_ENTITY_ID, TRANSACTION_TYPE_ID) VALUES(*********, 0, 0, 'wallet to wallet_cash_out', NULL, 2421, 131);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="CashOutNewTransactionsWalletToWalletSteps">
        <sql>
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES(101291, 0, NULL, NULL, NULL, 0, 0, 0, 0, 0, 2, NULL, NULL, 0, NULL, 0, 0, NULL, 0, 1, NULL, 1, NULL, NULL, NULL, 2, *********, NULL, 1);
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES(101301, 0, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, NULL, NULL, 0, NULL, 0, 0, NULL, 0, 2, NULL, 1, NULL, NULL, NULL, 1, *********, NULL, 1);
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES(101311, 0, NULL, NULL, NULL, 0, 0, 0, 0, 0, 2, NULL, NULL, 2, NULL, 0, 0, NULL, 0, 3, NULL, 1, NULL, NULL, NULL, 2, *********, NULL, 1);
            INSERT INTO TRANS_DEF_STEP (TRANS_DEF_STEP_ID, ACCOUNT_ID_ENABLED, ACCOUNT_LEVEL, ACCOUNT_OWNER_TYPE, ACCOUNT_TYPE_ID, ACCOUNT_OWNER_CORPORATE, ACCOUNT_TYPE_ENABLED, ALIAS_ENABLED, MSISDN_ENABLED, SHORTCODE_ENABLED, ACCOUNT_OWNER, ACCOUNTTYPE, AMOUNT_PERCENTAGE, AMOUNT_TYPE, FIXED_FEES_AMOUNT, INC_COMMISSION_CALC, INC_FEES_CALC, MSISDNTYPE, NEED_APPROVAL, STEPS_ORDER, OWNER_TYPE, REVERSABLE, SHORTCODETYPE, TRANSACTION_INITIATOR, USER_ID_TYPE, STEP_ACTION_TYPE, TRANS_DEF_SUMMARY_ID, FEE_PROFILE_OWNER, PAYMENT_METHOD_CODE_ENABLED) VALUES(101321, 0, NULL, NULL, 2, 1, 1, 0, 0, 0, 1, NULL, NULL, 5, NULL, 0, 0, NULL, 0, 4, NULL, 1, NULL, NULL, NULL, 1, *********, NULL, 1);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="AddErrorApiCodeForNoneExistedUser">
        <sql>
            INSERT INTO API_ERROR (ERROR_CODE, ERROR_DESCRIPTION) VALUES('VAL0178', 'Mobile number doesn’t exist on the system');
        </sql>
    </changeSet>
</databaseChangeLog>