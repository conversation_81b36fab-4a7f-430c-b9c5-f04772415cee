package com.cit.vericash.apis.RedeemCashoutVoucherAPI_Customer;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableCustomerInfo;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.RedeemCashoutVoucherAPI_Customer.RedeemCustomerVoucherCashoutRequestTransformer")
public class RedeemCustomerVoucherCashoutRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    @Autowired
    private CacheableCustomerInfo customerInfoCache;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;


//	public RedeemCustomerVoucherCashoutRequestTransformer() {
//		customerInfoCache = new CacheableCustomerInfoImpl();
//	}

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

    }


}
