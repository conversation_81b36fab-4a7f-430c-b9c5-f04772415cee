package com.cit.vericash.apis.receiver_approval;

import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.dto.request.Request;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.receiver_approval.RespondReceiverApprovalRequestTransformer")
@RequiredArgsConstructor
public class RespondToReceiverApprovalRequestTransformer implements VericashAction {
    private final ReceiverApprovalService respondReceiverApprovalService;
    @Override
    public Object process(Request request) throws Exception {
        return respondReceiverApprovalService.updateReceiverRequestApproval(request);
    }
}
