package com.cit.vericash.apis.addnewsalaryreceiver;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.addnewsalaryreceive.AddNewSalaryReceiverResponseTransformer")
public class AddNewSalaryReceiverResponseTransformer implements MuleResponseTransformer {
    Map<String, Object> result = new HashMap<String, Object>();

    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        if (businessMessage.getGroupTransfer() != null) {
            result.put("setupGroupTransfer", businessMessage.getGroupTransfer().getSetupGroupTransfer());
        }

        return result.get("setupGroupTransfer");

    }
}
