getMaxFavoriteCurrencies = SELECT LOOKUP_VALUE FROM GENERAL_LOOKUPS WHERE LOOKUP_KEY = ?
listFavoriteCurrencies = SELECT CURRENCY_ID FROM FCY_CUSTOMER_FAVORITES WHERE CUSTOMER_ID = ?
deleteFavoriteCurrencies= DELETE FROM FCY_CUSTOMER_FAVORITES WHERE CUSTOMER_ID = ?
startInsertFavoriteCurrencies= INSERT ALL 
insertFavoriteCurrencies=INTO FCY_CUSTOMER_FAVORITES (CUSTOMER_ID,CURRENCY_ID) VALUES (
endInsertFavoriteCurrencies=SELECT 1 FROM DUAL
