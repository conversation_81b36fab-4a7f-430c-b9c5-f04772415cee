<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet failOnError="false" author="sherif" id="moneyRequestChildServiceConfigMap">
        <sql>
            
			
	INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID,IS_GENERIC_SERVICE,SERVICE_TYPE_NAME,SERVICE_MODE,CATEGORY_ID,ORGANIZATION_ID,SERVICE_TYPE,SERVICE_CODE,IS_INDEMNITY,SEPARATE_QUEUE,
		SEPARATE_FRAMEWORK_QUEUE,HAS_WORKFLOW,SERVICE_CATEGORY,SERVICE_CATEGORY_ID,HOME_SCREEN_CONFIG,ENABLED_PROFILES,PARENT_SERVICE_CODE,ROLE_BASED) 
		VALUES(205,0,'sendCollectMoneyRequest by Wallet',0,NULL,NULL,NULL,'205',1,0,0,0,0,NULL,NULL,0,99347,0);
	INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID,IS_GENERIC_SERVICE,SERVICE_TYPE_NAME,SERVICE_MODE,CATEGORY_ID,ORGANIZATION_ID,SERVICE_TYPE,SERVICE_CODE,IS_INDEMNITY,SEPARATE_QUEUE,
		SEPARATE_FRAMEWORK_QUEUE,HAS_WORKFLOW,SERVICE_CATEGORY,SERVICE_CATEGORY_ID,HOME_SCREEN_CONFIG,ENABLED_PROFILES,PARENT_SERVICE_CODE,ROLE_BASED) 
		VALUES(206,0,'sendCollectMoneyRequest by CIB',0,NULL,NULL,NULL,'206',1,0,0,0,0,NULL,NULL,0,99347,0);
	INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID,IS_GENERIC_SERVICE,SERVICE_TYPE_NAME,SERVICE_MODE,CATEGORY_ID,ORGANIZATION_ID,SERVICE_TYPE,SERVICE_CODE,IS_INDEMNITY,SEPARATE_QUEUE,
		SEPARATE_FRAMEWORK_QUEUE,HAS_WORKFLOW,SERVICE_CATEGORY,SERVICE_CATEGORY_ID,HOME_SCREEN_CONFIG,ENABLED_PROFILES,PARENT_SERVICE_CODE,ROLE_BASED) 
		VALUES(207,0,'sendCollectMoneyRequest by PREPAID',0,NULL,NULL,NULL,'207',1,0,0,0,0,NULL,NULL,0,99347,0);
	INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID,IS_GENERIC_SERVICE,SERVICE_TYPE_NAME,SERVICE_MODE,CATEGORY_ID,ORGANIZATION_ID,SERVICE_TYPE,SERVICE_CODE,IS_INDEMNITY,SEPARATE_QUEUE,
		SEPARATE_FRAMEWORK_QUEUE,HAS_WORKFLOW,SERVICE_CATEGORY,SERVICE_CATEGORY_ID,HOME_SCREEN_CONFIG,ENABLED_PROFILES,PARENT_SERVICE_CODE,ROLE_BASED) 
		VALUES(208,0,'sendCollectMoneyRequest by Virtual Card ',0,NULL,NULL,NULL,'208',1,0,0,0,0,NULL,NULL,0,99347,0);
	INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID,IS_GENERIC_SERVICE,SERVICE_TYPE_NAME,SERVICE_MODE,CATEGORY_ID,ORGANIZATION_ID,SERVICE_TYPE,SERVICE_CODE,IS_INDEMNITY,SEPARATE_QUEUE,
		SEPARATE_FRAMEWORK_QUEUE,HAS_WORKFLOW,SERVICE_CATEGORY,SERVICE_CATEGORY_ID,HOME_SCREEN_CONFIG,ENABLED_PROFILES,PARENT_SERVICE_CODE,ROLE_BASED)  
		VALUES(209,0,'sendCollectMoneyRequest by DEBIT',0,NULL,NULL,NULL,'209',1,0,0,0,0,NULL,NULL,0,99347,0);

        </sql>
    </changeSet>
	
	
	<changeSet failOnError="false" author="sherif" id="moneyRequestChildBUSINESS_SERVICE_CONFIG">
        <sql>
            
			
	INSERT INTO BUSINESS_SERVICE_CONFIG (ID,BANKING_AGENT,BUSINESSSERVICECATEGORY,ISDEFAULTSERVICE,IS_EXPOSABLE,IS_MULTI_WALLET,NAME,BUSINESS_SERVICE_TYPE,
		SEGMENTATIONTYPE_ID,ORGANIZATION_ID,VERSION,IS_ENABLED,CATEGORY,NARRATION) VALUES (***************,0,0,1,0,0,'sendCollectMoneyRequest by Wallet',205,0,2421,NULL,1,NULL,NULL);
	INSERT INTO BUSINESS_SERVICE_CONFIG (ID,BANKING_AGENT,BUSINESSSERVICECATEGORY,ISDEFAULTSERVICE,IS_EXPOSABLE,IS_MULTI_WALLET,NAME,BUSINESS_SERVICE_TYPE,
		SEGMENTATIONTYPE_ID,ORGANIZATION_ID,VERSION,IS_ENABLED,CATEGORY,NARRATION) VALUES (***************,0,0,1,0,0,'sendCollectMoneyRequest by CIB',206,0,2421,NULL,1,NULL,NULL);
	INSERT INTO BUSINESS_SERVICE_CONFIG (ID,BANKING_AGENT,BUSINESSSERVICECATEGORY,ISDEFAULTSERVICE,IS_EXPOSABLE,IS_MULTI_WALLET,NAME,BUSINESS_SERVICE_TYPE,
		SEGMENTATIONTYPE_ID,ORGANIZATION_ID,VERSION,IS_ENABLED,CATEGORY,NARRATION) VALUES (***************,0,0,1,0,0,'sendCollectMoneyRequest by PREPAID',207,0,2421,NULL,1,NULL,NULL);
	INSERT INTO BUSINESS_SERVICE_CONFIG (ID,BANKING_AGENT,BUSINESSSERVICECATEGORY,ISDEFAULTSERVICE,IS_EXPOSABLE,IS_MULTI_WALLET,NAME,BUSINESS_SERVICE_TYPE,
		SEGMENTATIONTYPE_ID,ORGANIZATION_ID,VERSION,IS_ENABLED,CATEGORY,NARRATION) VALUES (***************,0,0,1,0,0,'sendCollectMoneyRequest by Virtual Card ',208,0,2421,NULL,1,NULL,NULL);
	INSERT INTO BUSINESS_SERVICE_CONFIG (ID,BANKING_AGENT,BUSINESSSERVICECATEGORY,ISDEFAULTSERVICE,IS_EXPOSABLE,IS_MULTI_WALLET,NAME,BUSINESS_SERVICE_TYPE,
		SEGMENTATIONTYPE_ID,ORGANIZATION_ID,VERSION,IS_ENABLED,CATEGORY,NARRATION) VALUES (***************,0,0,1,0,0,'sendCollectMoneyRequest by DEBIT',209,0,2421,NULL,NULL,NULL,NULL);

        </sql>
    </changeSet>
</databaseChangeLog>