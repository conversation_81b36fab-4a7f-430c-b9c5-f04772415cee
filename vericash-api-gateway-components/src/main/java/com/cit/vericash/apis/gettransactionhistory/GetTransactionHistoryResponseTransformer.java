package com.cit.vericash.apis.gettransactionhistory;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ExternalTransactionInformation;
import com.cit.mpaymentapp.common.message.TransactionReceiptInformation;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.model.MiniStatement;
import com.cit.vericash.config.VericashBackendConfig;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class GetTransactionHistoryResponseTransformer implements MuleResponseTransformer {
    private final String miniStatementKey = "mini_statement";
    Gson gson = new Gson();
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    @Autowired
    private VericashBackendConfig vericashBackendConfig;

    public Object transform(BusinessMessage businessMessage) throws Exception {


        // Need to change later ---- for OS

        Long paymentMethodCode = null;
        Map<String, String> result = new HashMap<String, String>();

        List<MiniStatement> resultOneList = new ArrayList<>();
        try {
            List<MiniStatement> trInformationList = new ArrayList<MiniStatement>();

            List<List<ExternalTransactionInformation>> externalTransactionInformationsListByGarIds = businessMessage.getExternalTransactionInformationsListByGarIds();
            List<ExternalTransactionInformation> externalTransactionInformationsList = businessMessage.getExternalTransactionInformations();

            List<ExternalTransactionInformation> externalTransactionInformations;

            if (externalTransactionInformationsList != null && externalTransactionInformationsList.size() > 0) {
                transform(externalTransactionInformationsList, trInformationList, paymentMethodCode, result);
                resultOneList.addAll(trInformationList);
                return resultOneList;
            } else if (externalTransactionInformationsListByGarIds != null && externalTransactionInformationsListByGarIds.size() > 0) {
                ListIterator<List<ExternalTransactionInformation>> listItr = externalTransactionInformationsListByGarIds.listIterator();

                while (listItr.hasNext()) {
                    trInformationList = new ArrayList<MiniStatement>();
                    externalTransactionInformations = listItr.next();
                    if (!externalTransactionInformations.isEmpty() && externalTransactionInformations != null) {
                        paymentMethodCode = transform(externalTransactionInformations, trInformationList, paymentMethodCode, result);
                    }
                }
            } else {

            }


        } catch (Exception e) {
            throw e;
        }
        return result;
    }

    private Long transform(List<ExternalTransactionInformation> externalTransactionInformations, List<MiniStatement> trInformationList, Long paymentMethodCode, Map<String, String> result) {

        boolean isBillPaymentService = false;

        for (ExternalTransactionInformation externalTransactionInformation : externalTransactionInformations) {
            TransactionReceiptInformation transactionInfo = (TransactionReceiptInformation) externalTransactionInformation;

            MiniStatement miniStatement = new MiniStatement();

            isBillPaymentService=checkIfABillPaymentService(transactionInfo);

            miniStatement.setTransactionStatus(transactionInfo.getTransactionStatus());
            miniStatement.setTransactionId(transactionInfo.getTranId());
            miniStatement.setTransactionDate(transactionInfo.getTranDate());
            miniStatement.setAmount(transactionInfo.getTransactionAmount());
            miniStatement.setFrom(transactionInfo.getSenderAccountNumber());

            if(!isBillPaymentService){
                miniStatement.setReceiver(transactionInfo.getReceiverAccountNumber());
            }
            miniStatement.setTransactionType(transactionInfo.getTranSubtype());
            miniStatement.setTransferType(transactionInfo.getTransferOption());
            miniStatement.setOwnerType(transactionInfo.getSenderName());
            miniStatement.setSourcePaymentMethodTypeID(transactionInfo.getSourcePaymentMethodTypeID());
            miniStatement.setDestinationPaymentMethodTypeID(transactionInfo.getDestinationPaymentMethodTypeID());
            miniStatement.setSourceDynamicParameters(transactionInfo.getSourceDynamicParameters());
            miniStatement.setDestinationDynamicParameters(transactionInfo.getDestinationDynamicParameters());
            miniStatement.setCurrency(transactionInfo.getCurrency());
            miniStatement.setTotalAmount(transactionInfo.getTotalTransactionAmount());
            miniStatement.setBiller(transactionInfo.getBiller());
            miniStatement.seteVoucherName(transactionInfo.geteVoucherName());
            miniStatement.setPartnerName(transactionInfo.getPartnerName());
            miniStatement.setReference(transactionInfo.getReference());
            miniStatement.setType(transactionInfo.getTransactionType());
            miniStatement.setFailureReason(transactionInfo.getFailureReason());
            miniStatement.setCode(transactionInfo.getCode());
            miniStatement.setSourcePaymentMethod(transactionInfo.getSourcePaymentMethod());
            miniStatement.setDestinationPaymentMethod(transactionInfo.getDestinationPaymentMethods());
            miniStatement.setBillPaymentOperatorId(transactionInfo.getBillPaymentOperatorId());
            miniStatement.setSourceDynamicParam(transactionInfo.getSourceDynamicParam());
            miniStatement.setDestinationDynamicParam(transactionInfo.getDestinationDynamicParam());
            miniStatement.setIsRepeatEnabled(transactionInfo.getIsRepeatEnabled());
            miniStatement.setIsFeeReadable(transactionInfo.getIsFeeReadable());
            if (transactionInfo.getFeeAmount() != null) {
                miniStatement.setFee(transactionInfo.getFeeAmount().toString());
            }

            trInformationList.add(miniStatement);
            paymentMethodCode = transactionInfo.getGarId();
            result.put("PaymentmethodCode_" + paymentMethodCode, gson.toJson(trInformationList));

        }
        return paymentMethodCode;
    }

    public boolean checkIfABillPaymentService(TransactionReceiptInformation transactionInfo) {
        String billPaymentPurchaseDSTV = "2404";
        String billPaymentPurchaseBundles = "2395";
        String billPaymentPurchaseAirtime = "2393";
        String billPaymentPurchaseShowMax = "2406";
        String billPaymentPrepaidElectricity = "23100";

        if (transactionInfo == null || transactionInfo.getCode() == null) {
            return false;
        }

        return transactionInfo.getCode().equals(billPaymentPurchaseShowMax) || transactionInfo.getCode().equals(billPaymentPurchaseAirtime)
                || transactionInfo.getCode().equals(billPaymentPurchaseDSTV) || transactionInfo.getCode().equals(billPaymentPurchaseBundles)
                || transactionInfo.getCode().equals(billPaymentPrepaidElectricity);
    }
}
//   if(!SEPARATED_LIST){
//
//           result_two.add(miniStatement);
//           paymentMethodCode=transactionInfo.getGarId();
//
//           