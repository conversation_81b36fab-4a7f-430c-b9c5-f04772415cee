package com.cit.vericash.apis.transfermoney.transfermoneybydefault;

import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableWalletInfo;
import com.cit.vericash.apis.commons.util.MsisdnFormatterImpl;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.backend.commons.dynamicinputparameters.mapping.DynamicPayloadHashMapMapping;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;

@Component
public class TransferMoneyByDefaultRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Autowired
    MsisdnFormatterImpl msisdnFormatter;
    @Autowired
    DynamicPayloadHashMapMapping dynamicPayloadHashMapMapping;
    @Autowired
    CacheableWalletInfo walletInfoCache;

    public static final String sourcePaymentMethod="sourcePaymentMethod";
    public static final String destinationPaymentMethod="destinationPaymentMethod";

    private static final String RECEIVER_OWNER_TYPE = "recieverOwnerType";
    private static final String RECEIVER_IDENTIFY_TYPE = "recieverIdentifyType";
    private static final String DESTINATION_PAYMENT_METHOD_PREFERENCE = "destinationPaymentMethodPreference";
    private static final String RECEIVER_IDENTIFY_VALUE = "recieverIdentifyValue";
    private static final String PAYMENT_METHOD_OPTIONS = "paymentMethodOptions";

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {


        try{
            Map<String, String> destinationPaymentMethodPreference = (Map<String, String>) message.getPayload().get(DESTINATION_PAYMENT_METHOD_PREFERENCE);
            if( message.getPayload().getAttributeAsString(RECEIVER_IDENTIFY_VALUE) != null && destinationPaymentMethodPreference.get(PAYMENT_METHOD_OPTIONS).equals("0")){
                message.getPayload().put(RECEIVER_OWNER_TYPE, "1");
                message.getPayload().put(RECEIVER_IDENTIFY_TYPE, "1");
            }
            String mobileNumber = ((Payload) dynamicPayloadHashMapMapping.transform(message.getDynamicPayload())).getAttributeAsString("msisdn");
            String walletShortCode = message.getHeader().getAttributeAsString("walletShortCode");
            WalletInfo walletInfo = walletInfoCache.getWalletInfo(walletShortCode);
            String region = walletInfo.getCountryIso2();

            String newMsisdn = msisdnFormatter.formatMSISDN(mobileNumber, region, walletShortCode);
            Map msisdnInputParam = (LinkedHashMap) (message.getDynamicPayload().getPayload().getAttribute("104"));

            if (msisdnInputParam != null && msisdnInputParam.size() > 0)
                msisdnInputParam.put("parameterValue", newMsisdn);

            message.getDynamicPayload().getPayload().put("104", msisdnInputParam);
        }
        catch (Exception ex){
            throw ex;
        }

//         try {
//
//        ObjectMapper mapperSource = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//         ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
//
//        Object sourcePaymentMethodObject=message.getPayload().getAttribute(sourcePaymentMethod);
//        Object destinationPaymentMethodObject=message.getPayload().getAttribute(destinationPaymentMethod);
//
//        SourcePaymentMethod sourcePaymentMethod = message.getPayload().getMapper().readValue(ow.writeValueAsString(sourcePaymentMethodObject), SourcePaymentMethod.class);
//        DestinationPaymentMethod destinationPaymentMethod = message.getPayload().getMapper().readValue(ow.writeValueAsString(destinationPaymentMethodObject), DestinationPaymentMethod.class);
//        message.getPayload().put("destinationPaymentMethod", destinationPaymentMethod);
//        message.getPayload().put("sourcePaymentMethod",sourcePaymentMethod);
//            targetObject.setValue("/destinationPaymentMethod", destinationPaymentMethod);
//
//         } catch (Exception e) {
//         throw e;
//         }
    }
}