package com.cit.vericash.api.advice;

import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.mpaymentapp.model.Enums;
import com.cit.vericash.api.components.JsonValidationStrategy;
import com.cit.vericash.api.components.impl.DynamicServiceValidationImpl;
import com.cit.vericash.api.entity.ApiChannel;
import com.cit.vericash.api.repository.ApiChannelRepository;
import com.cit.vericash.api.transformerfactory.RequestTransformer;
import com.cit.vericash.api.transformerfactory.TransformerFactory;
import com.cit.vericash.apis.commons.exceptions.ApiValidationException;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.commons.util.CacheableWalletInfo;
import com.cit.vericash.apis.commons.util.EndToEndComponent;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.dto.response.Response;
import com.cit.vericash.apis.security.component.SecurityValidatorComponent;
import com.cit.vericash.backend.commons.dynamicmodelmapper.RequestType;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.util.ApplicationNameValidator;
import com.cit.vericash.util.ResponseExceptionBuilder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;
import reactor.core.publisher.Mono;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AroundAdviceRequestTest {

    @InjectMocks
    private AroundAdviceRequest aroundAdviceRequest;

    @Mock
    private SecurityValidatorComponent securityValidatorComponent;
    @Mock
    private CachableVericashApi vericashApiCache;
    @Mock
    private DynamicServiceValidationImpl dynamicServiceValidation;
    @Mock
    private JsonValidationStrategy jsonValidationStrategy;
    @Mock
    private ApplicationNameValidator applicationNameValidator;
    @Mock
    private ApiChannelRepository apiChannelRepository;
    @Mock
    private EndToEndComponent endToEndComponent;
    @Mock
    private ProceedingJoinPoint joinPoint;
    @Mock
    private ResponseExceptionBuilder responseExceptionBuilder;
    @Mock
    private CacheableWalletInfo walletInfoCache;
    @Mock
    TransformerFactory transformerFactory;

    private Request request;
    private Message message;
    private WalletInfo walletInfo;
    private Optional<ApiChannel> channel;

    @BeforeEach
    void setUp() {
        request = new Request();
        message = new Message();
        message.setHeader(new Header());
        message.setPayload(new Payload());
        request.setMessage(message);
        walletInfo = new WalletInfo();
        channel = Optional.of(new ApiChannel());
    }
    @Test
    void executeSecurityAndValidation_ValidRequest_Success() throws Exception {
        // Setup
        VericashAPI mockVericashAPI = new VericashAPI();
        mockVericashAPI.setValidationType(Enums.ValidationType.SCHEMA);
        mockVericashAPI.setRequestType(RequestType.DYNAMIC.getRequestType());

        walletInfo = new WalletInfo();

        // Configure header attributes with proper types
        request.getMessage().getHeader().setAttribute("apiCode", 99560L); // Long value
        request.getMessage().getHeader().setAttribute("timestamp", 1647350817727L);
        request.getMessage().getHeader().setAttribute("walletShortCode", "2020");
        request.getMessage().getHeader().setAttribute("channelCode", "test_1");

        // Configure mocks
        when(vericashApiCache.getVericashAPI(anyLong())).thenReturn(mockVericashAPI);
        when(securityValidatorComponent.execute(any(Request.class))).thenReturn(request);
        when(walletInfoCache.getWalletInfo(anyString())).thenReturn(walletInfo);
        when(apiChannelRepository.findByChannelName(anyString())).thenReturn(channel);
        when(vericashApiCache.getVericashAPI(anyLong())).thenReturn(mockVericashAPI);
        RequestTransformer mockTransformer = mock(RequestTransformer.class);
        when(mockTransformer.transform(any(Message.class))).thenReturn(message);

        when(transformerFactory.getTransformerFactory(RequestType.DYNAMIC))
                .thenReturn(mockTransformer);
        // Execute
        Request result = aroundAdviceRequest.executeSecurityAndValidation(request);


        // Verify
        assertNotNull(result);
        verify(dynamicServiceValidation, never()).validateDynamicService(any());
        verify(jsonValidationStrategy).validateJsonStrategy(request);
        verify(applicationNameValidator).validateApplicationName(request);
    }

    @Test
    void setCustomAttributesForDefaultTransfer_ValidPayload_SetsAttributes() {
        // Setup
        Map<String, Object> payload = message.getPayload();
        payload.put("destinationPaymentMethodPreference", Map.of("paymentMethodOptions", "0"));
        payload.put("recieverIdentifyValue", "ID123");

        // Execute
        aroundAdviceRequest.setCustomAttributesForDefaultTransfer(message);

        // Verify
        assertEquals("1", message.getPayload().get("recieverOwnerType"));
        assertEquals("1", message.getPayload().get("recieverIdentifyType"));
    }
    @Test
    void generalVariablesValidation_MissingRequiredHeaders_ThrowsException() {
        // Clear all headers

        ApiValidationException exception = assertThrows(ApiValidationException.class,
                () -> aroundAdviceRequest.generalVariablesValidation(request)
        );
        assertEquals(ApiValidationException.class, exception.getClass());

    }
    @Test
    void generalVariablesValidation_InvalidWalletShortCode_ThrowsException() throws Exception {
        request.getMessage().getHeader().setAttribute("timestamp", 1647350817727L);
        request.getMessage().getHeader().setAttribute("walletShortCode", null);

        ApiValidationException exception = assertThrows(ApiValidationException.class,
                () -> aroundAdviceRequest.generalVariablesValidation(request)
        );
        assertEquals(ApiValidationException.class, exception.getClass());
    }

}