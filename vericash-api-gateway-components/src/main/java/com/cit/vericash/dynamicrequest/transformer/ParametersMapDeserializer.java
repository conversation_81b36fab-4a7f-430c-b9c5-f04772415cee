package com.cit.vericash.dynamicrequest.transformer;

import com.cit.mpaymentapp.common.message.ParametersMap;
import com.cit.vericash.backend.commons.util.PropertyLoader;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Map;

@Component
public class ParametersMapDeserializer extends JsonDeserializer<ParametersMap> {

    private static final String FILE = "customDeserializationClasses";
    private static final String FOLDER = "gateway-service";
    private static final String PROPERTY = "classes";

    @Autowired
    private PropertyLoader propertyLoaderComponent;

    private Map<String, Class<?>> classesMap;

    @PostConstruct
    private void initClassesMap() throws IOException {
        String classes = propertyLoaderComponent.getPropertyAsString(PROPERTY, FOLDER, FILE);
        classesMap = new ObjectMapper().readValue(classes, new TypeReference<Map<String, Class<?>>>() {});
    }

    @Override
    public ParametersMap deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        ObjectMapper mapper = (ObjectMapper) p.getCodec();
        JsonNode node = mapper.readTree(p);
        ParametersMap parametersMap = new ParametersMap();

        node.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode valueNode = entry.getValue();

            if (classesMap.containsKey(key)) {
                try {
                    parametersMap.put(key, mapper.treeToValue(valueNode, classesMap.get(key)));
                } catch (Exception e) {
                    throw new RuntimeException("Failed to deserialize key: " + key, e);
                }
            } else {
                parametersMap.put(key, mapper.convertValue(valueNode, Object.class));
            }
        });

        return parametersMap;
    }
}
