getTermsAndConditions=SELECT GL.LOOKUP_VALUE as TermsAndConditionlookupValue FROM GENERAL_LOOKUPS GL \
  where GL.LOOKUP_KEY =?
getCustomerIdTypes=SELECT H1.TYPEID,H2.USER_IDENTIFICATION_TYPE_NAME FROM CUSTOMER_ID_TYPES H1 \
  LEFT JOIN USER_IDENTIFICATION_TYPE H2 ON H1.TYPEID = H2.USER_IDENTIFICATION_TYPE_ID \
  where H1.SHORTCODE = ? 
getCustomerIdTypes_condition=and H1.ISGENERALTYPE = ?
getCustomerIdTypes_condition2=and H1.ISCUSTOMERTYPE != ?
getCountryOfWaalet=SELECT BEH.Country  as Countryid , CO.Country,CO.ISO, CO.ISO_Code, \
  CO.Country_Code  FROM  Business_Entity_Hierarchy  BEH \
  LEFT JOIN Country CO  ON  BEH.Country =  CO.Country_ID  \
  where  BEH.SHORT_CODE =? and BEH.HIERARCHY_LEVEL = ?
getNationalities=SELECT Id , Name  FROM  Nationality 
getCountriesDto=SELECT CO.Country_ID , CO.Country,CO.ISO,CO.ISO_Code, CO.Country_Code,STA.ID as ID_State \
  ,STA.NAME as NAME_State,REG.NAME  as NAME_Region ,REG.ID  as ID_Region ,REG.CODE FROM  Country  CO \
  LEFT JOIN state STA ON  CO.Country_ID = STA.ID \
  left join  region REG on STA.ID=REG.ID
getCustomerCategories=SELECT  CUT.Name ,CUT.Id  FROM CustomerType CUT \
  LEFT JOIN Business_Entity_Hierarchy  BEH  ON  CUT.BUSINESS_ENTITY_ID= BEH.BUSINESS_ENTITY_ID \
  where SHORT_CODE  = ? 
getServices=SELECT  VAPI.code,VAPI.NAME ,BUS.BUSINESSSERVICECATEGORY from API_VERICASH_APIS VAPI \
  LEFT JOIN  SERVICE_CONFIG_MAP SCP  ON VAPI.SERVICE_CODE=SCP.SERVICE_TYPE_ID \
  LEFT JOIN  BUSINESS_SERVICE_CONFIG BUS ON  SCP.SERVICE_TYPE_ID=BUS.BUSINESS_SERVICE_TYPE \
  LEFT JOIN  BUSINESS_ENTITY_HIERARCHY BEH ON BUS.ORGANIZATION_ID=BEH.BUSINESS_ENTITY_ID \
  where BEH.SHORT_CODE = ?  and BUS.BUSINESSSERVICECATEGORY = ? 
getServicesContainsFee=SELECT  VAPI.code,VAPI.NAME ,BUS.BUSINESSSERVICECATEGORY from API_VERICASH_APIS VAPI \
  LEFT JOIN  SERVICE_CONFIG_MAP SCP  ON VAPI.SERVICE_CODE=SCP.SERVICE_TYPE_ID \
  LEFT JOIN  BUSINESS_SERVICE_CONFIG BUS ON  SCP.SERVICE_TYPE_ID=BUS.BUSINESS_SERVICE_TYPE \
  LEFT JOIN  BUSINESS_ENTITY_HIERARCHY BEH ON BUS.ORGANIZATION_ID=BEH.BUSINESS_ENTITY_ID \
  LEFT JOIN BUSINESS_SERVICE_STEP BS ON  BUS.ID=BS.BUSINESS_SERVICE_CONFIG_ID where BEH.SHORT_CODE = ? and  BS.TYPE = ? 



