<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="2" author="khaled">
        <sql>
            <![CDATA[
        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From VC To Prepaied Card GTP by Customer',
            179,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Own Transfer mkento wallet to mkento wallet',
            178,
            0,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From VC To Debit Card by Customer',
            177,
            3,
            2421,
            1
        );
    ]]>
        </sql>

        <sql>
            <![CDATA[
        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From VC To CIB by Customer',
            176,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From Prepaid To Debit Card by Customer',
            175,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From Prepaid To CIB by Customer',
            174,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From Wallet To Debit Card by Customer',
            173,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From Wallet To CIB by Customer',
            172,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From Debit Card To Debit Card by Customer',
            171,
            3,
            2421,
            1
        );
    ]]>
        </sql>

        <sql>
            <![CDATA[
        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From Debit Card To Prepaid by Customer',
            170,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From Debit Card To Wallet by Customer',
            169,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From Debit Card To VC by Customer',
            168,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From Debit Card To CIB by Customer',
            167,
            3,
            2421,
            1
        );
    ]]>
        </sql>

        <sql>
            <![CDATA[
        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From CIB To Debit Card by Customer',
            166,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From CIB To VC by Customer',
            165,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From CIB To Prepaid by Customer',
            164,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From CIB To Wallet by Customer',
            163,
            3,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Internal Transfer - From CIB To CIB by Customer',
            162,
            3,
            2421,
            1
        );
    ]]>
        </sql>

        <sql>
            <![CDATA[
        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Own Transfer BU prepaid card to prepaid card',
            161,
            0,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Own Transfer BU prepaid card to mkento wallet',
            160,
            0,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Own Transfer BU mkento wallet to prepaid card',
            159,
            0,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Own Transfer BU mkento wallet to mkento wallet',
            158,
            0,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Own Transfer BU mkento wallet to CIB',
            157,
            0,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Own Transfer BU mkento wallet to Wallet',
            156,
            0,
            2421,
            1
        );

        INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
            ID,
            BANKING_AGENT,
            BUSINESSSERVICECATEGORY,
            ISDEFAULTSERVICE,
            IS_EXPOSABLE,
            IS_MULTI_WALLET,
            NAME,
            BUSINESS_SERVICE_TYPE,
            SEGMENTATIONTYPE_ID,
            ORGANIZATION_ID,
            IS_ENABLED
        ) VALUES (
            (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
            0,
            0,
            1,
            0,
            0,
            'Own Transfer BU Prepaid card to Prepaid card',
            155,
            0,
            2421,
            1
        );
    ]]>
        </sql>

    </changeSet>
</databaseChangeLog>
