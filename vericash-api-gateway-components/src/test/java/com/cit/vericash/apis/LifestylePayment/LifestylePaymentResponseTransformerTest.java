package com.cit.vericash.apis.LifestylePayment;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LifestylePaymentResponseTransformerTest {

    @Mock
    private CachableVericashApi vericashApiCache;

    @InjectMocks
    private LifestylePaymentResponseTransformer transformer;

    private BusinessMessage businessMessage;
    private TransactionInformation transactionInformation;
    private Map<String, Object> softFields;
    private Map<String, Object> transExecSummary;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // Prepare TransactionInformation mock
        transactionInformation = new TransactionInformation();
        transactionInformation.setTransactionAmount(BigDecimal.valueOf(100.0));
        transactionInformation.setTransactionFeeAmount(BigDecimal.valueOf(5));

        // Prepare TRANS_EXEC_SUMMARY map
        transExecSummary = new HashMap<>();
        transExecSummary.put("transactionExecutionSummaryID", "TX12345");

        // Prepare soft fields
        softFields = new HashMap<>();
        softFields.put("TRANS_EXEC_SUMMARY", transExecSummary);

        // Prepare BusinessMessage mock
        businessMessage = new BusinessMessage();
        businessMessage.setTransactionInfo(transactionInformation);
        businessMessage.setSoftFields(softFields);
    }

    @Test
    public void testTransform_ShouldReturnCorrectMap() throws Exception {
        Map<String, String> result = (Map<String, String>) transformer.transform(businessMessage);

        assertEquals("100.0", result.get("amount"));
        assertEquals("TX12345", result.get("refNo"));
    }
    @Test(expected = NullPointerException.class)
    public void testTransform_NullSoftFields_ShouldThrowException() throws Exception {
        businessMessage.setSoftFields(null);
        transformer.transform(businessMessage);
    }

    @Test(expected = NullPointerException.class)
    public void testTransform_MissingTransExecSummary_ShouldThrowException() throws Exception {
        businessMessage.getSoftFields().remove("TRANS_EXEC_SUMMARY");
        transformer.transform(businessMessage);
    }

    @Test(expected = NullPointerException.class)
    public void testTransform_NullTransExecSummary_ShouldThrowException() throws Exception {
        businessMessage.getSoftFields().put("TRANS_EXEC_SUMMARY", null);
        transformer.transform(businessMessage);
    }

    @Test(expected = NullPointerException.class)
    public void testTransform_TransactionInfoIsNull_ShouldThrowException() throws Exception {
        businessMessage.setTransactionInfo(null);
        transformer.transform(businessMessage);
    }

    @Test
    public void testTransform_NullAmounts_ShouldReturnStringNulls() throws Exception {
        TransactionInformation info = new TransactionInformation();
        info.setTransactionAmount(null);
        info.setTransactionFeeAmount(null);
        businessMessage.setTransactionInfo(info);
        Map<String, Object> summary = new HashMap<>();
        summary.put("transactionExecutionSummaryID", "ID001");
        Map<String, Object> softFields = new HashMap<>();
        softFields.put("TRANS_EXEC_SUMMARY", summary);
        businessMessage.setSoftFields(softFields);

        Map<String, String> result = (Map<String, String>) transformer.transform(businessMessage);
        assertEquals("null", result.get("amount"));
        assertEquals("null", result.get("fee"));
        assertEquals("ID001", result.get("refNo"));
    }

}
