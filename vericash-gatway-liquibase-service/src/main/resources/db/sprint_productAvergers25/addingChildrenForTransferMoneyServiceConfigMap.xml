<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
<changeSet author="Sheri<PERSON> <PERSON>" id="Sherif_SERVICE_CONFIG_MAP">

		<insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878899998"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer - Wallet to Source Debit"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878899998"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="ENABLED_PROFILES" valueNumeric="1"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878899999"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - Wallet to CIB"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878899999"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="ENABLED_PROFILES" valueNumeric="1"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900000"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money- Wallet to Source Virtual Card"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900000"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="ENABLED_PROFILES" valueNumeric="1"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900001"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Debit to Wallet"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900001"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="ENABLED_PROFILES" valueNumeric="1"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900002"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Debit to Debit card"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900002"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="ENABLED_PROFILES" valueNumeric="1"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900003"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Debit to CIB"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900003"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="ENABLED_PROFILES" valueNumeric="1"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900004"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Debit Source Prepaid"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900004"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="ENABLED_PROFILES" valueNumeric="1"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900005"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Debit to Source Virtual"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900005"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="ENABLED_PROFILES" valueNumeric="1"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
		
		<insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900006"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From CIB To Wallet"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900006"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900007"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From CIB to Debit Card"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900007"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900008"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From CIB To CIB"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900008"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900009"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From CIB To Prepaid"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900009"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900010"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From CIB To Virtual Card"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900010"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
		
		<insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900011"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Source prepaid to Debit"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900011"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900012"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Source prepaid to CIB"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900012"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900013"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Source prepaid to Source virtual card"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900013"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900014"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Source Virtual To Wallet"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900014"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900016"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Source Virtual To CIB"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900016"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
		
		<insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900015"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Source Virtual To Debit"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900015"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900017"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Source Virtual To Source Prepaid"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900017"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP" schemaName="PRO_MI_NO_C">
            <column name="SERVICE_TYPE_ID" valueNumeric="878900018"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Transfer Money - From Source Virtual To Source Virtual"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_CODE" value="878900018"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="5000"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
		
		</changeSet>

</databaseChangeLog>
