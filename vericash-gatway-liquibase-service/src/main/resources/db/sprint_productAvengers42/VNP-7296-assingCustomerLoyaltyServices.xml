<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="change-service-type-id-to-long" author="Zyad El-Alfy">
        <modifyDataType tableName="APP_MENUS" columnName="SERVICE_TYPE_ID" newDataType="NUMBER(19,0)"/>
    </changeSet>

    <changeSet id="insert-Loyalty-Service-app-menu" author="Zyad El-Alfy">
        <insert tableName="APP_MENUS">
            <column name="ID" valueNumeric="5"/>
            <column name="ENABLED" valueNumeric="1"/>
            <column name="MENU_ITEM_NAME" value="Loyalty Services"/>
            <column name="MENU_ITEM_ORDER" valueNumeric="3"/>
            <column name="ITEM_TYPE" valueNumeric="1"/>
            <column name="MENU_ITEM_NAME_FR" value="Services de fidélité"/>
            <column name="MENU_ITEM_NAME_PT" value="Serviços de fidelidade"/>
        </insert>
        <insert tableName="APP_MENUS">
            <column name="ID" valueNumeric="7"/>
            <column name="ENABLED" valueNumeric="1"/>
            <column name="PARENT" valueNumeric="5"/>
            <column name="MENU_ITEM_NAME" value="Send Points"/>
            <column name="MENU_ITEM_ORDER" valueNumeric="1"/>
            <column name="SERVICE_TYPE_ID" valueNumeric="9101"/>
            <column name="ITEM_TYPE" valueNumeric="1"/>
            <column name="MENU_ITEM_NAME_FR" value="Envoyer des points"/>
            <column name="MENU_ITEM_NAME_PT" value="Enviar pontos"/>
        </insert>
        <insert tableName="APP_MENUS">
            <column name="ID" valueNumeric="8"/>
            <column name="ENABLED" valueNumeric="1"/>
            <column name="PARENT" valueNumeric="5"/>
            <column name="MENU_ITEM_NAME" value="Redeem To Cash"/>
            <column name="MENU_ITEM_ORDER" valueNumeric="2"/>
            <column name="SERVICE_TYPE_ID" valueNumeric="8081"/>
            <column name="ITEM_TYPE" valueNumeric="1"/>
            <column name="MENU_ITEM_NAME_FR" value="Convertir en argent"/>
            <column name="MENU_ITEM_NAME_PT" value="Resgatar por dinheiro"/>
        </insert>
        <insert tableName="APP_MENUS">
            <column name="ID" valueNumeric="9"/>
            <column name="ENABLED" valueNumeric="1"/>
            <column name="PARENT" valueNumeric="5"/>
            <column name="MENU_ITEM_NAME" value="Redeem To Voucher"/>
            <column name="MENU_ITEM_ORDER" valueNumeric="3"/>
            <column name="SERVICE_TYPE_ID" valueNumeric="8082"/>
            <column name="ITEM_TYPE" valueNumeric="1"/>
            <column name="MENU_ITEM_NAME_FR" value="Convertir en bon d''achat"/>
            <column name="MENU_ITEM_NAME_PT" value="Resgatar por vale"/>
        </insert>
        <insert tableName="APP_MENUS">
            <column name="ID" valueNumeric="10"/>
            <column name="ENABLED" valueNumeric="1"/>
            <column name="PARENT" valueNumeric="5"/>
            <column name="MENU_ITEM_NAME" value="View Received Points History "/>
            <column name="MENU_ITEM_ORDER" valueNumeric="4"/>
            <column name="SERVICE_TYPE_ID" valueNumeric="287879998436"/>
            <column name="ITEM_TYPE" valueNumeric="1"/>
            <column name="MENU_ITEM_NAME_FR" value="Voir l''historique des points reçus"/>
            <column name="MENU_ITEM_NAME_PT" value="Ver histórico de pontos recebidos"/>
        </insert>
        <insert tableName="APP_MENUS">
            <column name="ID" valueNumeric="11"/>
            <column name="ENABLED" valueNumeric="1"/>
            <column name="PARENT" valueNumeric="5"/>
            <column name="MENU_ITEM_NAME" value="View Redeemed Points History "/>
            <column name="MENU_ITEM_ORDER" valueNumeric="5"/>
            <column name="SERVICE_TYPE_ID" valueNumeric="287879998437"/>
            <column name="ITEM_TYPE" valueNumeric="1"/>
            <column name="MENU_ITEM_NAME_FR" value="Voir l''historique des points convertis"/>
            <column name="MENU_ITEM_NAME_PT" value="Ver histórico de pontos resgatados"/>
        </insert>
        <insert tableName="APP_MENUS">
            <column name="ID" valueNumeric="12"/>
            <column name="ENABLED" valueNumeric="1"/>
            <column name="PARENT" valueNumeric="5"/>
            <column name="MENU_ITEM_NAME" value="View Transfered Points History "/>
            <column name="MENU_ITEM_ORDER" valueNumeric="6"/>
            <column name="SERVICE_TYPE_ID" valueNumeric="287879998438"/>
            <column name="ITEM_TYPE" valueNumeric="1"/>
            <column name="MENU_ITEM_NAME_FR" value="Voir l''historique des points transférés"/>
            <column name="MENU_ITEM_NAME_PT" value="Ver histórico de pontos transferidos"/>
        </insert>
    </changeSet>

    <changeSet id="insert-Loyalty-Service-app-menus-wallet" author="Zyad El-Alfy">
        <insert tableName="APP_MENUS_WALLET">
            <column name="APP_MENUS_ID" valueNumeric="5"/>
            <column name="WALLET_SHORT_CODE" value="2020"/>
            <column name="APP_TYPE" valueNumeric="2"/>
        </insert>
        <insert tableName="APP_MENUS_WALLET">
            <column name="APP_MENUS_ID" valueNumeric="7"/>
            <column name="WALLET_SHORT_CODE" value="2020"/>
            <column name="APP_TYPE" valueNumeric="2"/>
        </insert>
        <insert tableName="APP_MENUS_WALLET">
            <column name="APP_MENUS_ID" valueNumeric="8"/>
            <column name="WALLET_SHORT_CODE" value="2020"/>
            <column name="APP_TYPE" valueNumeric="2"/>
        </insert>
        <insert tableName="APP_MENUS_WALLET">
            <column name="APP_MENUS_ID" valueNumeric="9"/>
            <column name="WALLET_SHORT_CODE" value="2020"/>
            <column name="APP_TYPE" valueNumeric="2"/>
        </insert>
        <insert tableName="APP_MENUS_WALLET">
            <column name="APP_MENUS_ID" valueNumeric="10"/>
            <column name="WALLET_SHORT_CODE" value="2020"/>
            <column name="APP_TYPE" valueNumeric="2"/>
        </insert>
        <insert tableName="APP_MENUS_WALLET">
            <column name="APP_MENUS_ID" valueNumeric="11"/>
            <column name="WALLET_SHORT_CODE" value="2020"/>
            <column name="APP_TYPE" valueNumeric="2"/>
        </insert>
        <insert tableName="APP_MENUS_WALLET">
            <column name="APP_MENUS_ID" valueNumeric="12"/>
            <column name="WALLET_SHORT_CODE" value="2020"/>
            <column name="APP_TYPE" valueNumeric="2"/>
        </insert>
    </changeSet>

</databaseChangeLog>




