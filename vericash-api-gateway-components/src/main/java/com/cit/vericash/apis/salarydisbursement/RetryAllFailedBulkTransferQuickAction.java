package com.cit.vericash.apis.salarydisbursement;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.RetryAllFailedBulkTransferQuickAction")
public class RetryAllFailedBulkTransferQuickAction implements VericashAction {

    @Autowired
    ExecuteBulkTransferCommonService executeBulkTransferCommonService;

    @Autowired
    CalculateBulkFeesCommonService calculateBulkFeesCommonService;

    @Autowired
    ConnectionUtil connectionUtil;

    @Override
    public Object process(Request request) throws Exception {
        Integer groupTransferSetupId = request.getMessage().getPayload().getAttributeAsInteger("groupSetupId");

        List<Record> recipientsRecords = getRecipientsForRetryAllFailedTransfers(groupTransferSetupId);

        executeBulkTransferCommonService.executeTransfer(request, recipientsRecords);
        return null;
    }

    public List<Record> getRecipientsForRetryAllFailedTransfers(Integer groupTransferSetupId) {
        //get all recipients for group transfer setup
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getRecipientsForRetryAllFailedTransfers" , this.getClass() ,String.valueOf(groupTransferSetupId));
        /*String transferRecipientsQuery = "SELECT\n" +
                "\tgts.GROUP_NAME,\n" +
                "\tc.CUSTOMER_FEE_PROFILE_ID ,\n" +
                "\tgtr.TRANSFER_ID,\n" +
                "\tgtr.AMOUNT,\n" +
                "\tgtr.BENEFICIARY_ID,\n" +
                "\tgtr.BENEFICIARY_NAME,\n" +
                "\tgtr.BRANCH_CODE,\n" +
                "\tgtr.BRANCH_NAME,\n" +
                "\tgtr.CARD_NUMBER,\n" +
                "\tgtr.COUNTRY_NAME,\n" +
                "\tgtr.INSTITUTION_CODE,\n" +
                "\tgtr.INSTITUTION_NAME,\n" +
                "\tgtr.TRANSFER_TYPE,\n" +
                "\tgtr.TRANSFER_TYPE_CATEGORY,\n" +
                "\tgtr.WALLET_SHORT_CODE,\n" +
                "\tgtr.GROUP_TRANSFER_SETUP_ID,\n" +
                "\tgtr.STATUS,\n" +
                "\tgtr.CLIENT_ID,\n" +
                "\tgtr.RECEIVER_TYPE,\n" +
                "\tgtr.WALLET_PROVIDER,\n" +
                "\tgtr.CURRENCY,\n" +
                "\tgtr.PAYMENT_ALIAS,\n" +
                "\tgtr.MOBILE_NUMBER,\n" +
                "\tgtr.IS_DELETED,\n" +
                "\tgtr.VALIDATION_ERROR,\n" +
                "\tgtr.GAR_ID,\n" +
                "\tgtr.PAYMENT_METHOD_TYPE ,\n" +
                "\tcg.ACCOUNT_ID,\n" +
                "\tgtr.CUSTOMER_ID\n" +
                "FROM\n" +
                "\tGROUP_TRANSFER_RECIPIENT gtr\n" +
                "INNER JOIN CUSTOMER_GAR cg ON\n" +
                "\tgtr.GAR_ID = cg.GARID\n" +
                "INNER JOIN CUSTOMER c ON\n" +
                "\tc.CUSTOMER_ID = gtr.CUSTOMER_ID\n" +
                "INNER JOIN GROUP_TRANSFER_SETUP gts ON\n" +
                "\tgts.GROUP_TRANSFER_SETUP_ID = gtr.GROUP_TRANSFER_SETUP_ID\n" +
                "INNER JOIN GROUP_TRANSFER_RECIPIENT_EXEC gtre ON\n" +
                "\tgtre.RECIPIENT_ID  = gtr.TRANSFER_ID \n" +
                "WHERE\n" +
                "\tgtr.GROUP_TRANSFER_SETUP_ID = " + groupTransferSetupId + "AND gtre.TRANSFER_RESULT=0";*/
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields = setRecipientsFields(fields);
        List<Record> recipientRecords = connectionUtil.executeSelect(sqlQuery, parameters, fields);

        return recipientRecords;
    }

    public List<String> setRecipientsFields(List<String> fields) {
        fields.add("TRANSFER_ID");
        fields.add("AMOUNT");
        fields.add("BENEFICIARY_ID");
        fields.add("BENEFICIARY_NAME");
        fields.add("BRANCH_CODE");
        fields.add("BRANCH_NAME");
        fields.add("CARD_NUMBER");
        fields.add("COUNTRY_NAME");
        fields.add("INSTITUTION_CODE");
        fields.add("INSTITUTION_NAME");
        fields.add("TRANSFER_TYPE");
        fields.add("TRANSFER_TYPE_CATEGORY");
        fields.add("WALLET_SHORT_CODE");
        fields.add("GROUP_TRANSFER_SETUP_ID");
        fields.add("STATUS");
        fields.add("CLIENT_ID");
        fields.add("RECEIVER_TYPE");
        fields.add("WALLET_PROVIDER");
        fields.add("CURRENCY");
        fields.add("PAYMENT_ALIAS");
        fields.add("MOBILE_NUMBER");
        fields.add("IS_DELETED");
        fields.add("VALIDATION_ERROR");
        fields.add("GAR_ID");
        fields.add("PAYMENT_METHOD_TYPE");

        fields.add("ACCOUNT_ID");
        fields.add("CUSTOMER_ID");
        fields.add("CUSTOMER_FEE_PROFILE_ID");
        fields.add("GROUP_NAME");
        return fields;
    }

}