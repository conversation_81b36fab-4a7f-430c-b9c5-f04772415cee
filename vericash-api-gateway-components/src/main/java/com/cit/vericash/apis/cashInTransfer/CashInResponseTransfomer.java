package com.cit.vericash.apis.cashInTransfer;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CashInResponseTransfomer implements MuleResponseTransformer {

    Map<String, Object> result = new HashMap<String, Object>();

    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        // TODO Auto-generated method stub
        return result;
    }

}
