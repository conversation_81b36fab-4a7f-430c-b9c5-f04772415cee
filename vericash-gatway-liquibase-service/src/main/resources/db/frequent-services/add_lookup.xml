<databaseChangeLog
xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

<changeSet failOnError="true" author="michael" id="add-lookup-frequent-service">

    <sql>
        INSERT INTO LOOKUP_FRAMEWORK_CONFIG (ID, CACHING_KEY_PATTERN, CACHING_TYPE, IS_CACHED_LOOKUP, LOOKUP_NAME, LOOKUP_TYPE, RESPONSE_TRANSFORMER_MAPPING, CUSTOME_RESPONSE_TRANSFORMER, CUSTOM_TRANSFORMER_CONFIG_FILE, ENDPOINT_NAME, SERVICE_NAME) VALUES((select max(ID) +1 from LOOKUP_FRAMEWORK_CONFIG), '0', 0, 0, 'get.frequent.services.lookup', 0, NULL, 'Get Feature Services', NULL, NULL, NULL);
    </sql>
    <sql>
        INSERT INTO PORTAL_LOOKUPS_CONFIG (ID, LOOKUP_NAME, LOOKUP_KEY, LOOKUP_QUERY, CACHEABLE) VALUES((select max(ID) +1 from PORTAL_LOOKUPS_CONFIG), 'Get Feature Services', 'get.frequent.services.lookup', 'SELECT API_CODE, am.MENU_ITEM_NAME, NAME, AMOUNT, IS_AMOUNT_ENABLED, STATUS, OPERATOR_ID FROM FREQUENT_CONFIG fc INNER JOIN API_VERICASH_APIS ava ON ava.CODE = fc.API_CODE INNER JOIN APP_MENUS am ON am.SERVICE_TYPE_ID = ava.SERVICE_CODE', 0);
    </sql>

</changeSet>
</databaseChangeLog>