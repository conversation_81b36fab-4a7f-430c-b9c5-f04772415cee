package com.cit.vericash.apis.assignandredeem;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("AssignAndRedeemResbonseTransformer")
public class AssignAndRedeemResbonseTransformer implements MuleResponseTransformer {

    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, String> result = new HashMap<String, String>();
        result.put("redemptionCode", ((Map) businessMessage.getMicroServicesResponse()).get("redemptionCode").toString());
        result.put("serialNumber", ((Map) businessMessage.getMicroServicesResponse()).get("redemptionCode").toString());
        return result;
    }

}
