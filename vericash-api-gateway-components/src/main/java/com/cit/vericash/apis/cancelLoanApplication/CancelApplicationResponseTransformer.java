package com.cit.vericash.apis.cancelLoanApplication;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.File;
import java.text.MessageFormat;
import java.util.*;

@Component
@Qualifier("com.cit.vericash.apis.cancelLoanApplication.CancelApplicationResponseTransformer")
public class CancelApplicationResponseTransformer implements MuleResponseTransformer {
    
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String,String> result = new HashMap<String,String>();


        ArrayList<HashMap> microServicesResponse = (ArrayList<HashMap>)businessMessage.getMicroServicesResponse();
        result.put("refNo",String.valueOf(microServicesResponse.get(0).get("refNo")));
//        String STUBS_CONFIG_PATH=System.getenv("STUBS_HOME");
//        String fullPath=STUBS_CONFIG_PATH+ File.separator+"Lending"+File.separator+"2020"+File.separator+"response"+File.separator+"cancelApplication.json";
//        ObjectMapper objectMapper = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT);
//        try {
//            File responseFile = new File(fullPath);
//            JsonNode jsonNode = objectMapper.readTree(responseFile);
//            String applicationId= jsonNode.get("integrationResponse").get(0).get("result").get("data").get("refNo").asText();
//            .resultput("ref No of canceled application : ", applicationId);
//        }
//
//        catch (Exception e){
//            e.printStackTrace();
//            throw new Exception("Can not find the file");
//        }
        return result;
    }
}
