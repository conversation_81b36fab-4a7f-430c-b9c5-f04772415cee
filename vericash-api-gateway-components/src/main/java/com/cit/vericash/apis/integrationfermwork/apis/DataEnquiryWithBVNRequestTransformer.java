package com.cit.vericash.apis.integrationfermwork.apis;

import com.cit.vericash.api.components.impl.integrationfremwork.MessageToDynamicPaylodTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Qualifier("com.cit.vericash.apis.integrationfermwork.apis.DataEnquiryWithBVNRequestTransformer")
public class DataEnquiryWithBVNRequestTransformer extends MessageToDynamicPaylodTransformerImpl {
    // todo: edit the file before this complete the mapping and the file for the api
    @Autowired
    PropertyLoaderComponent propertyLoader;

    private final String OpenBankAccountServiceName = "OpenBankAccountName";
    private final String bvnNumParamNum = "261";
    private final String birthdateParamNum = "219";
    String serviceCode = "serviceCode";

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        validateRequest(message);

        Payload payload = message.getDynamicPayload().getPayload();
        if (payload.containsKey("customerId")) ;
            payload.remove("customerId");

//        ArrayList<String> pmsTypeKey = new ArrayList(Arrays.asList(propertyLoader.getPropertyAsString("pmsTypeKey", "application", "application").split(",")));
//        for (String type : pmsTypeKey) {
//            System.out.println(type);
//            if (payload.containsKey(type))
//                payload.remove(type);
//
//        }

        Payload targetPayload = new Payload();
        for (var entry : payload.entrySet()) {
            System.out.println(entry.getKey() + "/" + (LinkedHashMap<String, Object>) entry.getValue());
            targetPayload.put(entry.getKey(), ((LinkedHashMap<String, Object>) entry.getValue()).get("parameterValue"));
        }

//        message.getDynamicPayload().getHeader().put( PMTypeCode, targetPmType);
        message.getDynamicPayload().getHeader().put(serviceCode, message.getHeader().get("apiCode"));

      /*  ArrayList<String> pmsTypeKey = new ArrayList(Arrays.asList(propertyLoader.getPropertyAsString("pmsTypeKey=", "application", "application").split(",")));
        for (String type : pmsTypeKey) {
            System.out.println(type);
            if (targetPayload.containsKey(type))
                targetPayload.remove(type);

        }*/


        String serviceName = propertyLoader.getPropertyAsString(OpenBankAccountServiceName);
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setPayload((Payload) targetPayload);
        dynamicPayload.setHeader(message.getDynamicPayload().getHeader());
        message.getPayload().put("integration_serviceName", serviceName);
        message.setDynamicPayload(dynamicPayload);


    }


    private void validateRequest(Message message) throws Exception {

        Map<String, String> birthdateParam = (Map<String, String>) message.getDynamicPayload().getPayload().get(birthdateParamNum);
        String birthdate = birthdateParam.get("parameterValue");
        Map<String, String> bvnNumParam = (Map<String, String>) message.getDynamicPayload().getPayload().get(bvnNumParamNum);
        String bvnNum = bvnNumParam.get("parameterValue");
        Date date = new Date();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        int thisYear = calendar.get(Calendar.YEAR);



        if ((birthdate == null || birthdate.trim().isEmpty()) && (bvnNum == null || bvnNum.trim().isEmpty())) {
            throw new QuickActionException("birthdate or bvnNum Must be Found",
                    ErrorCode.MISSING_PARAMETER.getErrorCode());
        }

        String[] splitDate =birthdate.split("-");
        Integer userYear= Integer.valueOf(splitDate[2]);

        if (thisYear-userYear<18)
        {
            throw new QuickActionException("You are allowed to open an account only after turning to 18 years old",
                    ErrorCode.USER_SMALLER_THAN_18.getErrorCode());
        }
    }

}


