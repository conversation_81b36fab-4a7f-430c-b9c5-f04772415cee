package com.cit.vericash.apis.forgetusernamebyconsumer;

import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.forgetusernamebyconsumer.InitiateForgetUserNameByConsumerRequestTransformer")
public class InitiateForgetUserNameByConsumerRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        String msisdn = message.getPayload().getAttributeAsString("msisdn");
        String userKey = message.getHeader().getAttributeAsString("walletShortCode") + msisdn;
        message.getPayload().put("userKey", userKey);

    }
}
