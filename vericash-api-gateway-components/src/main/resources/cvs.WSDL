<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://com.ubagrp.waei.impl" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  name="cardvalidatn" targetNamespace="http://com.ubagrp.waei.impl">
    <wsdl:documentation>
        <wsdl:appinfo source="WMQI_APPINFO">
            <MRWSDLAppInfo imported="true">
                <binding hasEncoding="false" imported="true" name="cardvalidatnSOAP_HTTP_Binding"
                         originalBindingStyle="document"/>
            </MRWSDLAppInfo>
        </wsdl:appinfo>
    </wsdl:documentation>


    <wsdl:types>
        <xsd:schema targetNamespace="http://com.ubagrp.waei.impl">
            <xsd:include schemaLocation="cvs_xsd0.xml"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="validateCardDetails_in">
        <wsdl:part element="tns:CardValidationRequest" name="CardValidationRequest"/>
    </wsdl:message>
    <wsdl:message name="validateCardDetails_out">
        <wsdl:part element="tns:CardValidationResponse" name="CardValidationResponse"/>
    </wsdl:message>
    <wsdl:message name="validateCardDetails_fault1">
        <wsdl:part element="tns:CardValidationResponse" name="CardValidationResponse"/>
    </wsdl:message>
    <wsdl:portType name="cardvalidatnPortType">
        <wsdl:operation name="validateCardDetails">
            <wsdl:input message="tns:validateCardDetails_in" name="validateCardDetails_Input"/>
            <wsdl:output message="tns:validateCardDetails_out" name="validateCardDetails_Output"/>
            <wsdl:fault message="tns:validateCardDetails_fault1" name="validateCardDetails_Fault"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="cardvalidatnSOAP_HTTP_Binding" type="tns:cardvalidatnPortType">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="validateCardDetails">
            <soap:operation soapAction=""/>
            <wsdl:input name="validateCardDetails_Input">
                <soap:body parts="CardValidationRequest" use="literal"/>
            </wsdl:input>
            <wsdl:output name="validateCardDetails_Output">
                <soap:body parts="CardValidationResponse" use="literal"/>
            </wsdl:output>
            <wsdl:fault name="validateCardDetails_Fault">
                <soap:fault name="validateCardDetails_Fault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="cardvalidatnService">
        <wsdl:port binding="tns:cardvalidatnSOAP_HTTP_Binding" name="cardvalidatnPort">
            <soap:address location="http://************:7808/ebanking/selfreg/cvs"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>