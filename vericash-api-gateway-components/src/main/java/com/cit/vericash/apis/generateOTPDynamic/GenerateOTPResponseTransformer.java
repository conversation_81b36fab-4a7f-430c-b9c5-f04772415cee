package com.cit.vericash.apis.generateOTPDynamic;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.generateOTPDynamic.GenerateOTPResponseTransformer")
public class GenerateOTPResponseTransformer implements MuleResponseTransformer {

    private final String successMsgKey = "success_msg";
    private String maskedMsisdn = "masked_Msisdn";
    private String otpLength = "otp_Length";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage message) throws Exception {
        Map<String, String> result = new HashMap<String, String>();
        String maskedMsisdnVal = cacheableFieldParameters.getResponseParameters(maskedMsisdn);
        String otpLengthVal = cacheableFieldParameters.getResponseParameters(otpLength);
        result.put(maskedMsisdnVal, message.getPrimarySenderInfo().getMaskedMsisdn());
        result.put(otpLengthVal, message.getPrimarySenderInfo().getOtpLength());

        result.put(cacheableFieldParameters.getResponseParameters(successMsgKey), "Success");

        return result;
    }
}