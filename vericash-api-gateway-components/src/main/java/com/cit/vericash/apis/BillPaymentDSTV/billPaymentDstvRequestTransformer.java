package com.cit.vericash.apis.BillPaymentDSTV;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.util.CacheableWalletInfo;
import com.cit.vericash.apis.commons.util.MsisdnFormatterImpl;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.backend.commons.dynamicinputparameters.mapping.DynamicPayloadHashMapMapping;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;


@Component
public class billPaymentDstvRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private static final String INVALID_MOBILE_NUMBER = "invalidMobileNumberFormatErrorCode";
    private static final PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();

    @Autowired
    DynamicPayloadHashMapMapping dynamicPayloadHashMapMapping;
    @Autowired
    private PropertyLoaderComponent propertyLoaderComponent;
    @Autowired
    private CacheableWalletInfo walletInfoCache;
    @Autowired
    MsisdnFormatterImpl msisdnFormatter;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
      }
}
