execute=SELECT t.TRANSACTION_NAME ,t.<PERSON><PERSON><PERSON>_MSISDN ,t.<PERSON>NDER_NAME, t.<PERSON><PERSON><PERSON>_PAYMENT_METHOD , \
  t.SENDER_USER_ID ,t.SENDER_WALLET_SHORTCODE ,t.TRANSACTION_AMOUNT ,tu.MO<PERSON>LE_OPERATOR_PRODUCT , \
  tu.PAYMENT_OPTION ,tu.PURCHASE_TYPE ,tu.TOPUP_PHONENUMBER ,tu.MO<PERSON>LE_OPERATOR \
  FROM TRANSACTIONDETAILS t JOIN TOPUPDETAILS tu ON t.TRANSACTION_ID = tu.TRANSACTION_ID \
  WHERE  t.TASK_ID = %1$s 