getSupportedPaymentMethods=SELECT PM.NAME, PM.ID,PM.AUTOMATICALLY_OPENED,PM.STORAGELOCATION,PM.IS_SINGLE_ACCOUNT,\
  PM.ACCEPT_OVER_DRAFT,PM.IS_CREDIT,PM.IS_DEBIT,PM.IS_DEBT,\
  PM.OPENED_UPON_CUS_REQ,PM.OVER_DRAFT_LIMIT,PM.ALLOW_BALANCE_INQUIRY,PM.CURRENCY_OR_UNIT,\
  PM.HAS_EQUIVALENT_BALANCE,\
  ps.ID PARAMETER_CODE,ps.PARAMETER_NAME, ps.DATATYPE, ps.MANDATORY,\
  ps.MINIMUM_LENGTH, ps.MAXIMUM_LENGTH, ps.EXACT_LENGTH, ps.DEFAULT_VALUE, \
  ps.MINIMUM_VALUE, ps.MAXIMUM_VALUE, ps.VALUE_LIST, ps.IS_UNIQUE, ps.FORMAT,\
  ps.IS_UPDATABLE,PM.ALLOW_UPDATE,PM.ALLOW_DELETE,PM.ALLOW_FREEZE,pc.GROUP_ID,pc.GROUP_NAME,\
  ps.DECIMAL_PLACES,ps.PARAM_ORDER,ps.MASKED_ON_STORE,ps.VIEWABLE,ps.MASKED_ON_VIEW,ps.MASK_PATTERN \
  FROM DYNAMIC_PARAMETER_GROUP pc \
  JOIN DYNAMIC_PARAMETERS ps ON ps. DYNAMIC_PARAMETER_GROUP_ID = pc.GROUP_ID \
  JOIN PAYMENT_METHOD PM ON PM. DYNAMIC_PARAMETER_GROUP_ID = pc.GROUP_ID \
  JOIN PAYMENT_METHOD_CUST_PROF_CG pcg ON PM.ID = pcg.PAYMENT_METHOD_ID \
  JOIN CUSTOMER C ON C.CUSTOMER_TYPE_ID=pcg.CUSTOMER_TYPE_ID \
  WHERE C.USER_ID = ? \
  ORDER BY ps.PARAM_ORDER ASC