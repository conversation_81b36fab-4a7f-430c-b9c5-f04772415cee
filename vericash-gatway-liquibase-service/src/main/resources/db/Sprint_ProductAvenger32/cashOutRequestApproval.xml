<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="create-receiver-request-approval-status-table" author="<PERSON>">
        <createTable tableName="RECEIVER_REQUEST_APPROVAL_STATUS">
            <column name="ID" type="NUMBER(19,0)">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="STATUS_NAME" type="VARCHAR2(255 CHAR)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!--changeSet id="create-receiver-request-approval-status-index" author="<PERSON>">
        <createIndex indexName="SYS_C0033463" tableName="RECEIVER_REQUEST_APPROVAL_STATUS">
            <column name="ID"/>
        </createIndex>
    </changeSet>-->

    <changeSet id="create-receiver-request-approval-table" author="Abdullah Khamis">
        <createTable tableName="RECEIVER_REQUEST_APPROVAL" >
            <column name="ID" type="NUMBER(19,0)">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="API_CODE" type="VARCHAR2(255 CHAR)">
                <constraints nullable="false"/>
            </column>
            <column name="CREATION_DATE" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>
            <column name="EXPIRY_DATE" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>
            <column name="LAST_UPDATE_DATE" type="TIMESTAMP(6)" />
            <column name="RECEIVER_CUSTOMER_ID" type="NUMBER(19,0)">
                <constraints nullable="false"/>
            </column>
            <column name="SENDER_CUSTOMER_ID" type="NUMBER(19,0)">
                <constraints nullable="false"/>
            </column>
            <column name="STATUS" type="NUMBER(19,0)" />
            <column name="CORRELATION_ID" type="VARCHAR2(100 CHAR)" />
            <column name="AMOUNT" type="NUMBER(38,2)" />
        </createTable>
    </changeSet>

  <!--  <changeSet id="create-receiver-request-approval-index" author="Abdullah Khamis">
        <createIndex indexName="SYS_C0033460" tableName="RECEIVER_REQUEST_APPROVAL" >
            <column name="ID"/>
        </createIndex>
    </changeSet>-->

    <changeSet id="add-foreign-key-status" author="Abdullah Khamis">
        <addForeignKeyConstraint
                constraintName="FKABFK929DY7353LQWO2IW9CKVK"
                baseTableName="RECEIVER_REQUEST_APPROVAL"
                baseColumnNames="STATUS"
                referencedTableName="RECEIVER_REQUEST_APPROVAL_STATUS"
                referencedColumnNames="ID"
                 />
    </changeSet>

    <changeSet id="portal-lookups-config" author="Abdullah Khamis">
        <insert tableName="PORTAL_LOOKUPS_CONFIG">
            <column name="ID" valueComputed="(SELECT MAX(ID) + 1 FROM PRO_MI_NO_C.PORTAL_LOOKUPS_CONFIG)" />
            <column name="LOOKUP_NAME" value="RECEIVER_REQUEST_APPROVAL_TIMEOUT_SECONDS" />
            <column name="LOOKUP_KEY" value="RECEIVER_REQUEST_APPROVAL_TIMEOUT_SECONDS" />
            <column name="LOOKUP_QUERY" value="SELECT LOOKUP_KEY, LOOKUP_VALUE FROM ( SELECT LOOKUP_KEY AS LOOKUP_KEY, CAST(LOOKUP_VALUE AS VARCHAR2(100)) AS LOOKUP_VALUE FROM GENERAL_LOOKUPS WHERE LOOKUP_KEY = 'RECEIVER_REQUEST_APPROVAL_TIMEOUT_SECONDS')" />
            <column name="CACHEABLE" valueNumeric="1" />
        </insert>
    </changeSet>

    <changeSet id="lookup-framework-config" author="Abdullah Khamis">
        <insert tableName="LOOKUP_FRAMEWORK_CONFIG">
            <column name="ID" valueComputed="(SELECT MAX(ID) + 1 FROM PRO_MI_NO_C.LOOKUP_FRAMEWORK_CONFIG)" />
            <column name="CACHING_KEY_PATTERN" value="0" />
            <column name="CACHING_TYPE" valueNumeric="0" />
            <column name="IS_CACHED_LOOKUP" valueNumeric="0" />
            <column name="LOOKUP_NAME" value="RECEIVER_REQUEST_APPROVAL_TIMEOUT_SECONDS" />
            <column name="LOOKUP_TYPE" valueNumeric="0" />
            <column name="RESPONSE_TRANSFORMER_MAPPING" value="NULL" />
            <column name="CUSTOME_RESPONSE_TRANSFORMER" value="NULL" />
            <column name="CUSTOM_TRANSFORMER_CONFIG_FILE" value="NULL" />
            <column name="ENDPOINT_NAME" value="NULL" />
            <column name="SERVICE_NAME" value="NULL" />
        </insert>
    </changeSet>

</databaseChangeLog>
