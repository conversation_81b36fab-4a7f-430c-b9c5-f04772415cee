<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet failOnError="false" author="Abubakr" id="add MoneyRequest-SendPayFromMe record to SERVICE_CONFIG_MAP">
        <sql>
			INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'payForMeMoneyRequest - Wallet', 0, NULL, NULL, NULL, '878,799,976', 1, 0, 0, 0, 0, NULL, NULL, NULL, 99348, 0);
			INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'payForMeMoneyRequest - CIB', 0, NULL, NULL, NULL, '878,799,977', 1, 0, 0, 0, 0, NULL, NULL, NULL, 99348, 0);
			INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'payForMeMoneyRequest - Prepaid', 0, NULL, NULL, NULL, '878,799,978', 1, 0, 0, 0, 0, NULL, NULL, NULL, 99348, 0);
			INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'payForMeMoneyRequest - Virtual Card', 0, NULL, NULL, NULL, '878,799,979', 1, 0, 0, 0, 0, NULL, NULL, NULL, 99348, 0);
			INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'payForMeMoneyRequest - Debit', 0, NULL, NULL, NULL, '878,799,980', 1, 0, 0, 0, 0, NULL, NULL, NULL, 99348, 0);
		</sql>
    </changeSet>
	
	<changeSet failOnError="false" author="Abubakr" id="add MoneyRequest-SendPayFromMe record to BUSINESS_SERVICE_CONFIG">
		<sql>
			INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES((select max(ID) +1 from BUSINESS_SERVICE_CONFIG), 0, 0, 1, 0, 0, 'payForMeMoneyRequest - Wallet', *********, 3, 2421, NULL, 1, NULL, NULL);
			INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES((select max(ID) +1 from BUSINESS_SERVICE_CONFIG), 0, 0, 1, 0, 0, 'payForMeMoneyRequest - CIB', *********, 3, 2421, NULL, 1, NULL, NULL);
			INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES((select max(ID) +1 from BUSINESS_SERVICE_CONFIG), 0, 0, 1, 0, 0, 'payForMeMoneyRequest - Prepaid', *********, 3, 2421, NULL, 1, NULL, NULL);
			INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES((select max(ID) +1 from BUSINESS_SERVICE_CONFIG), 0, 0, 1, 0, 0, 'payForMeMoneyRequest - Virtual Card', *********, 3, 2421, NULL, 1, NULL, NULL);
			INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES((select max(ID) +1 from BUSINESS_SERVICE_CONFIG), 0, 0, 1, 0, 0, 'payForMeMoneyRequest - Debit', *********, 3, 2421, NULL, 1, NULL, NULL);
		</sql>
	</changeSet>


</databaseChangeLog>