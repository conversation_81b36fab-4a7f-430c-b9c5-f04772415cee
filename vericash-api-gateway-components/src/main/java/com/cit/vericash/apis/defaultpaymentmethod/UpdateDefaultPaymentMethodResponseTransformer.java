package com.cit.vericash.apis.defaultpaymentmethod;

import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.defaultpaymentmethod.UpdateDefaultPaymentMethodResponseTransformer")
public class UpdateDefaultPaymentMethodResponseTransformer implements VericashAction {

    @Override
    public Object process(Request request) throws Exception {
        return null;
    }
}
