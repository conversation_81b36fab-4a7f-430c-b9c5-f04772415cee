//package com.cit.vericash.apis.commons.util;
//
//import static org.junit.Assert.assertNotNull;
//import static org.junit.Assert.assertNull;
//import static org.mockito.Matchers.anyList;
//import static org.mockito.Matchers.anyString;
//
//import java.math.BigDecimal;
//import java.sql.SQLException;
//import java.util.ArrayList;
//import java.util.List;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.BDDMockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import com.cit.vericash.apis.commons.model.VericashAPI;
//import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
//import com.cit.vericash.apis.commons.util.connection.Record;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest(ConnectionUtil.class)
//public class ApiVericashApiLoaderTest {
//
//	@Test
//	public void test_LoadVericashApi_withValidApiCode_LoadSuccess() throws Exception {
//		Long apiCode = 1234L;
//
//		List<Record> records = new ArrayList<Record>();
//		records.add(initSampleRecord());
//
////		PowerMockito.mockStatic(ConnectionUtil.class);
////		BDDMockito.given(ConnectionUtil.executeSelect(anyString(), anyList(), anyList()))
////		.willReturn(records);
//
//		VericashApiLoader apiLoader = new VericashApiLoader();
//		VericashAPI vericashAPI = apiLoader.LoadVericahApi(apiCode);
//
//		assertNotNull(vericashAPI);
//		assertNotNull(vericashAPI.getCode());
//		assertNotNull(vericashAPI.getName());
//
//	}
//
//	private Record initSampleRecord() {
//		Record record = new Record();
//
//		record.setField("CODE", 123L);
//		record.setField("NAME", "uba");
//    	record.setField("SERVICE_CODE", "123");
//    	record.setField("REQUEST_ACTION", null);
//    	record.setField("RESPONSE_ACTION", null);
//    	record.setField("OUTBOUND_TO_MULE",new BigDecimal(0));
//    	record.setField("SUCCESS_MESSAGE", null);
//    	record.setField("INBOUND_QUEUE", null);
//    	record.setField("OUTBOUND_QUEUE", null);
//    	record.setField("SCHEMA_CONFIG_FILE", null);
//    	record.setField("MAPPING_CONFIG_FILE", null);
//
//    	return record;
//	}
//
//	@Test
//	public void test_LoadVericashApi_withNotValidApiCode_LoadFailed() throws Exception {
//		Long apiCode = 12L;
//
//		List<Record> records = new ArrayList<Record>();
//
//
////		PowerMockito.mockStatic(ConnectionUtil.class);
////		BDDMockito.given(ConnectionUtil.executeSelect(anyString(), anyList(), anyList()))
////		.willReturn(records);
//
//		VericashApiLoader apiLoader = new VericashApiLoader();
//		VericashAPI vericashAPI = apiLoader.LoadVericahApi(apiCode);
//		assertNull(vericashAPI);
//	}
//
//	@Test(expected = Exception.class)
//	public void test_LoadVericashApi_withDBError_ThrowException() throws Exception {
//		Long apiCode = 12L;
//
////		PowerMockito.mockStatic(ConnectionUtil.class);
////		BDDMockito.given(ConnectionUtil.executeSelect(anyString(), anyList(), anyList()))
////		.willThrow(SQLException.class);
//
//		VericashApiLoader apiLoader = new VericashApiLoader();
//		apiLoader.LoadVericahApi(apiCode);
//	}
//
//}
