viewAllNotification=SELECT NOTIFICATION_ID,ISREADING,NOTIFICATION_DATE,TITLE,CONTENT  \
  FROM MOBILE_NOTIFICATION \
  WHERE CUSTOMER_KEY = '%1$s' AND STATUS = 1 ORDER BY NOTIFICATION_DATE DESC
viewRecentNotification=SELECT NOTIFICATION_ID,ISREADING,NOTIFICATION_DATE,TITLE,CONTENT \
  FROM MOBILE_NOTIFICATION \
  WHERE CUSTOMER_KEY = '%1$s' AND STATUS = 1 ORDER BY NOTIFICATION_DATE DESC
deleteNotification=UPDATE CUSTOMER_NOTIFICATION SET STATUS = 0 WHERE NOTIFICATION_ID = %1$s
readNotification=UPDATE CUSTOMER_NOTIFICATION SET ISREADING = 1 WHERE NOTIFICATION_ID = %1$s
