package com.cit.vericash.apis.fcy;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.when;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.api.entity.GeneralLookups;
import com.cit.vericash.api.repository.GeneralLookupsRepository;
import com.cit.vericash.apis.commons.util.VericashLogger;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.AdditionalData;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.EchoData;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(MockitoJUnitRunner.class)
public class UpdateFCYFavoriteCurrenciesTest {


    @InjectMocks
    private UpdateFCYFavoriteCurrencies updateFCYFavoriteCurrencies;
    @Mock
    VericashLogger vericashLogger;
    @Mock
    ConnectionUtil connectionUtil;
    @Mock
    GeneralLookupsRepository generalLookupsRepository;
    @Mock
    ServiceQueryEngine serviceQueryEngine;

    public static final String CUSTOMER_ID = "101566";
    public static final String MAX_FAVORITES_NUMBER= "4";
    public static final List<String> FAVORITES_CURRENCIES = List.of("1","2","3");
    public static final List<Parameter> PARAMETERS = List.of(new Parameter(1,CUSTOMER_ID));

    public final List<Record> records = new ArrayList<>();
    private final Record record = new Record();
    private final Header header = new Header();
    private final Payload payload = new Payload();
    private final Message message = new Message();
    private final Request request = new Request();





    @Before
    public void setUp(){
        MockitoAnnotations.initMocks(this);
        record.put("CUSTOMER_ID",101566);
        record.put("CURRENCY_ID",1);
        records.add(record);
        payload.put("customerId", CUSTOMER_ID);
        payload.put("favorites",List.of("1","2","3"));
        message.setHeader(header);
        message.setPayload(payload);
        request.setMessage(message);
    }


    @Test
    public void testProcess() throws Exception {
        GeneralLookups generalLookups = new GeneralLookups();
        generalLookups.setLookupkey("FCY_BOARD_MAX_NO_FAVORITE");
        generalLookups.setLookupvalue("4");
        List<String>favorite = (List) request.getMessage().getPayload().getAttribute("favorites");
        when(generalLookupsRepository.findLookupvalueByLookupkey(eq("FCY_BOARD_MAX_NO_FAVORITE"))).thenReturn(Optional.of(generalLookups));

        assertDoesNotThrow(
                ()->updateFCYFavoriteCurrencies.process(request)
        );
    }

    @Test
    public void testProcessWithExceedSizeOfFavorites() throws Exception {
        GeneralLookups generalLookups = new GeneralLookups();
        generalLookups.setLookupkey("FCY_BOARD_MAX_NO_FAVORITE");
        generalLookups.setLookupvalue("2");
        List<String>favorite = (List) request.getMessage().getPayload().getAttribute("favorites");
        when(generalLookupsRepository.findLookupvalueByLookupkey(eq("FCY_BOARD_MAX_NO_FAVORITE"))).thenReturn(Optional.of(generalLookups));

        assertThrows(Exception.class,()->updateFCYFavoriteCurrencies.process(request));
    }

    @Test
    public void testProcessWithCheckCurrencies() throws Exception {
        GeneralLookups generalLookups = new GeneralLookups();
        generalLookups.setLookupkey("FCY_BOARD_MAX_NO_FAVORITE");
        generalLookups.setLookupvalue("3");
        payload.put("favorites",List.of("100","200","30"));
        when(generalLookupsRepository.findLookupvalueByLookupkey(eq("FCY_BOARD_MAX_NO_FAVORITE"))).thenReturn(Optional.of(generalLookups));

        assertThrows(Exception.class,()->updateFCYFavoriteCurrencies.process(request));
    }

    @Test
    public void testProcessWithDeleteFavorites() throws Exception {
        GeneralLookups generalLookups = new GeneralLookups();
        generalLookups.setLookupkey("FCY_BOARD_MAX_NO_FAVORITE");
        generalLookups.setLookupvalue("3");
        when(generalLookupsRepository.findLookupvalueByLookupkey(eq("FCY_BOARD_MAX_NO_FAVORITE"))).thenReturn(Optional.of(generalLookups));
        request.getMessage().getPayload().setAttribute("customerId",null);
        assertDoesNotThrow(
                ()->updateFCYFavoriteCurrencies.process(request)
        );
    }

    @Test
    public void testProcessWithInsertNewFavorites() throws Exception {
        GeneralLookups generalLookups = new GeneralLookups();
        generalLookups.setLookupkey("FCY_BOARD_MAX_NO_FAVORITE");
        generalLookups.setLookupvalue("3");
        when(generalLookupsRepository.findLookupvalueByLookupkey(eq("FCY_BOARD_MAX_NO_FAVORITE"))).thenReturn(Optional.of(generalLookups));
        payload.put("favorites",List.of("100","150","30"));
        assertDoesNotThrow(
                ()->updateFCYFavoriteCurrencies.process(request)
        );
    }

}
