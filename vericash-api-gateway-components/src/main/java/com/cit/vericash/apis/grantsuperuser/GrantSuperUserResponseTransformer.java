package com.cit.vericash.apis.grantsuperuser;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.grantsuperuser.GrantSuperUserResponseTransformer")
public class GrantSuperUserResponseTransformer implements MuleResponseTransformer {


    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        return null;
    }
}
