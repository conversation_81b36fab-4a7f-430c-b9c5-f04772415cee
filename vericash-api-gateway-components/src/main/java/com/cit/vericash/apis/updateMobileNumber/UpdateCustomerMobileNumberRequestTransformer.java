package com.cit.vericash.apis.updateMobileNumber;

import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.CacheableWalletInfo;
import com.cit.vericash.apis.commons.util.MsisdnFormatterImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.updateMobileNumber.UpdateCustomerMobileNumberRequestTransformer")
public class UpdateCustomerMobileNumberRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    private final String WALLET_SHORT_CODE = "wallet-Short-Code";
    @Autowired
    MsisdnFormatterImpl msisdnFormatter;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    @Autowired
    private CacheableWalletInfo walletInfoCache;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        String walletShortCodeKey = cacheableFieldParameters.getRequestParameters(WALLET_SHORT_CODE);
        String walletShortCode = message.getHeader().getAttributeAsString(walletShortCodeKey);
        WalletInfo walletInfo = walletInfoCache.getWalletInfo(walletShortCode);
        String msisdn = message.getPayload().getAttributeAsString("newMsisdn");
        String region = walletInfo.getCountryIso2(); //
        msisdn = msisdnFormatter.formatMSISDN(msisdn, region, walletShortCode);
    }
}
