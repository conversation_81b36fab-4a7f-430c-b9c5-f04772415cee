package com.cit.vericash.apis.integrationfermwork.apis;

import com.cit.vericash.api.components.impl.integrationfremwork.MessageToDynamicPaylodTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.integrationfermwork.apis.ValidateOTPRequestTransformer")
public class ValidateOTPRequestTransformer extends MessageToDynamicPaylodTransformerImpl {
    // todo: edit the file before this complete the mapping and the file for the api
    @Autowired
    PropertyLoaderComponent propertyLoader;

    private final String ValidateOTPServiceName = "ValidateOTP";
    private final String otpParamNum = "262";
    String serviceCode = "serviceCode";

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        validateRequest(message);

        Payload payload = message.getDynamicPayload().getPayload();
        if (payload.containsKey("customerId")) ;
            payload.remove("customerId");

        Payload targetPayload = new Payload();
        for (var entry : payload.entrySet()) {
            System.out.println(entry.getKey() + "/" + (LinkedHashMap<String, Object>) entry.getValue());
            targetPayload.put(entry.getKey(), ((LinkedHashMap<String, Object>) entry.getValue()).get("parameterValue"));
        }

        message.getDynamicPayload().getHeader().put(serviceCode, message.getHeader().get("apiCode"));

        String serviceName = propertyLoader.getPropertyAsString(ValidateOTPServiceName);
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setPayload((Payload) targetPayload);
        dynamicPayload.setHeader(message.getDynamicPayload().getHeader());
        message.getPayload().put("integration_serviceName", serviceName);
        message.setDynamicPayload(dynamicPayload);


    }


    private void validateRequest(Message message) throws Exception {

        Map<String, String> otpParam = (Map<String, String>) message.getDynamicPayload().getPayload().get(otpParamNum);
        String otp = otpParam.get("parameterValue");


        if (otp == null || otp.trim().isEmpty()) {
            throw new QuickActionException("otp Must be Found",
                    ErrorCode.MISSING_PARAMETER.getErrorCode());
        }

    }

}


