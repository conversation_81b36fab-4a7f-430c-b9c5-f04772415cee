package com.cit.vericash.apis.getBillersBills;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.mongo.beans.Bill;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Component
public class GetBillersBillsResponseTransformer implements MuleResponseTransformer {
    private final String billsKey = "bills";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        Gson gson = new Gson();
        try {

            if (businessMessage.getSoftFields().get(billsKey) != null) {
                Set<Bill> bills = (Set<Bill>) businessMessage
                        .getSoftFields().get(billsKey);
                result.put(cacheableFieldParameters.getResponseParameters(billsKey), bills);

            }

        } catch (Exception e) {
            throw e;
        }

        return result;
    }
}



