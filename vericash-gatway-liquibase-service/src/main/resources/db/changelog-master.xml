<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
		xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
		xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
   http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

	<include relativeToChangelogFile="true" file="Sprint_Product33/update-country-icon-url.xml"/>


<!--	 <include relativeToChangelogFile="true" file="Sprint_ProudtAvenger10/addNewPMs.xml"/>-->
<!--	 <include relativeToChangelogFile="true" file="Sprint_ProudtAvenger10/ServicesPms.xml"/>-->
	<!-- <include relativeToChangelogFile="true" file="Sprint_ProudtAvenger12/customer_segmentation.xml"/> -->
<!--	<include relativeToChangelogFile="true" file="Sprint_productAvenger15/VNP-3319/sendRequestAndCalcFees.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint_productAvenger15/moneyRequestChildren.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint_ProudtAvenger12/customer_segmentation.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="service_builder/change_validation_type.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="api_vericash_apis/update_api_vericash_apis.xml"/>-->
	<!-- <include relativeToChangelogFile="true" file="Sprint11/updateApiError.xml"/> -->
<!--	<include relativeToChangelogFile="true" file="fcy/create_tables.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="fcy/add_configurations.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint_productAvenger15/MoneyRequestGetAvilableServices/GetAvilableServicesLookup.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint_productAvenger15/VNP-3319/sendRequestAndCalcFees.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint_productAvenger15/VNP-3549-MoneyRequestStatusAndMonthsLookups/GetMoneyRequestMonthsAndStatusLookup.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint_productAvenger15/BlockedUsersLookup.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint_productAvenger15/EnableOrDiableMoneyRequestLookup.xml"/>-->
	<!-- <include relativeToChangelogFile="true" file="Sprint_ProductAvenger23/VNP-4701_appMenus.xml"/>
	<include relativeToChangelogFile="true" file="sprint_productAvergers25/sucessmassagesforsplitservices.xml"/>
	 -->
<!--
	<include relativeToChangelogFile="true" file="sprint-product33/update-country-icon-url.xml"/>
-->



<!--	<include relativeToChangelogFile="true" file="Sprint_ProductAvenger23/VNP-4701_appMenus.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="multiLang/updateAppMenu.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="multiLang/updateSmeAgencyAppMenu.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="multiLang/updateServiceConfigMap.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="multiLang/updateLanguage.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="multiLang/updateApiErrorStructure.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint 26/Send-Money/send-money-alias.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint 26/Send-Money/send-money-alias-business-service-config.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint26.UpdateDefaultPaymentMethod/updateDefaultPaymentMethod.xml"/>-->


<!--	<include relativeToChangelogFile="true" file="open_bank_account/alias_integration_step_changes.xml"/>-->

<!--	<include relativeToChangelogFile="true" file="open_bank_account/validate_open_account_changes.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="sprint-28/beneficiary.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="transaction-history/create-screen-navigation-table.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint_ProductAvenger31/VNP-6537_cashOutRequestApproval.xml"/>-->
<!--	<include relativeToChangelogFile="true" file="Sprint_ProductAvenger35/fileDownloadEnhance.xml"/>-->
	<!--<include relativeToChangelogFile="true" file="Sprint_ProductAvenger36/PaymentMethodType.xml"/>-->
	<!--<include relativeToChangelogFile="true" file="Sprint_ProductAvenger35/fileDownload.xml"/>-->
	<!--<include relativeToChangelogFile="true" file="Sprint_ProductAvenger35/fileDownloadEnhance.xml"/>-->
	<!--<include relativeToChangelogFile="true" file="Sprint_Product33/update-country-icon-url.xml"/>-->



	<!--<include relativeToChangelogFile="true"  file="Sprint_product-Avengers39/VNP-7666-Notifacation.xml"/>-->
<!--<include relativeToChangelogFile="true"  file="Sprint_ProductAvenger35/GroupTypesStatus.xml"/>-->
	<!--<include relativeToChangelogFile="true"  file="Sprint_ProductAvenger35/fileDownload.xml"/>-->
	<!--<include relativeToChangelogFile="true" file="Sprint_ProductAvenger35/fileDownloadEnhance.xml"/>-->	
<!--<include relativeToChangelogFile="true" file="Sprint_ProductAvenger36/PaymentMethodType.xml"/>-->


<!--<include relativeToChangelogFile="true" file="Sprint_ProductAvenger31/VNP-6537_cashOutRequestApproval.xml"/>-->
<!--<include relativeToChangelogFile="true" file="Sprint_ProductAvenger32/cashOutRequestApproval.xml"/>-->
<!--<include relativeToChangelogFile="true" file="Sprint_ProductAvenger35/cashOutRequestApprovalStatus.xml"/>-->
<!--<include relativeToChangelogFile="true"  file="Sprint_product-Avengers41/VNP-7708-Evoucher.xml"/>-->
<!--<include relativeToChangelogFile="true"  file="sprint_productAvengers42/VNP-7296-assingCustomerServices.xml"/>-->
<!--<include relativeToChangelogFile="true"  file="sprint_productAvengers45/SMEAppMenus.xml"/>-->
<!--	<include relativeToChangelogFile="true"  file="app-rating/appRaing.xml"/>-->

<include relativeToChangelogFile="true"  file="sprint_productAvengers45/SMEaddbecustomer.xml"/>

<include relativeToChangelogFile="true"  file="sprint_productAvengers45/SMEaddvaildationdynmicforaddandactivatrbecustomer.xml"/>

<include relativeToChangelogFile="true"  file="sprint_productAvengers45/SMEAppMenusforOwnntransfer.xml"/>
</databaseChangeLog>


	
	

