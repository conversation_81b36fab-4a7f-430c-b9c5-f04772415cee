package com.cit.vericash.apis.recurringMonthTransaction;


import com.cit.vericash.apis.recurringTransaction.TransactionRecurringDTO;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.recurringMonthTransaction.RecurringMonthTransactionDTO")
public class RecurringMonthTransactionDTO {
    private Object day;
    private int countOfRecurringTransactions;
    private List<TransactionRecurringDTO> recurringTransactions;

    public RecurringMonthTransactionDTO() {
    }

    public Object getDay() {
        return day;
    }

    public void setDay(Object day) {
        this.day = day;
    }

    public int getCountOfRecurringTransactions() {
        return countOfRecurringTransactions;
    }

    public void setCountOfRecurringTransactions(int countOfRecurringTransactions) {
        this.countOfRecurringTransactions = countOfRecurringTransactions;
    }

    public List<TransactionRecurringDTO> getRecurringTransactions() {
        return recurringTransactions;
    }

    public void setRecurringTransactions(List<TransactionRecurringDTO> recurringTransactions) {
        this.recurringTransactions = recurringTransactions;
    }
}
