<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">


<!--    <changeSet failOnError="false" author="Abubakr" id="OwnTransferAddChildrenToSERVICE_CONFIG_MAP">-->
<!--        <sql>-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Debit Card To New Bank Account', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Debit Card To MKENTO Wallet ', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Debit Card To Prepaied Card GTP', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'New Bank Account To Virtual Card', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'New Bank Account To New Bank Account', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'New Bank Account To MKENTO Wallet ', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'New Bank Account To Prepaied Card GTP', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Virtual Card To New Bank Account', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'MKENTO Wallet To New Bank Account', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Prepaied Card GTP To New Bank Account', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Debit Card To Virtual Card', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->

<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'New Bank Account To Debit Card', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Virtual Card To Debit Card', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'MKENTO Wallet To Debit Card', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Prepaied Card To Debit Card', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->
<!--            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Debit Card To Debit Card', 0, 3, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, 1, 77777, 0);-->

<!--        </sql>-->
<!--    </changeSet>-->

<!--    <changeSet failOnError="false" author="Abubakr" id="OwnTransferAddChildrenToBUSINESS_SERVICE_CONFIG">-->
<!--        <sql>-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'Debit Card To New Bank Account', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'Debit Card To MKENTO Wallet ', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'Debit Card To Prepaied Card GTP', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'New Bank Account To Virtual Card', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'New Bank Account To New Bank Account', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'New Bank Account To MKENTO Wallet ', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'New Bank Account To Prepaied Card GTP', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'Virtual Card To New Bank Account', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'MKENTO Wallet To New Bank Account', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'Prepaied Card GTP To New Bank Account', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'Debit Card To Virtual Card', *********, 0, 2421, NULL, 1, NULL, NULL);-->

<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'New Bank Account To Debit Card', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'Virtual Card To Debit Card', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'MKENTO Wallet To Debit Card', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'Prepaied Card To Debit Card', *********, 0, 2421, NULL, 1, NULL, NULL);-->
<!--            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***********, 0, 0, 1, 0, 0, 'Debit Card To Debit Card', *********, 0, 2421, NULL, 1, NULL, NULL);-->

<!--        </sql>-->
<!--    </changeSet>-->

    <changeSet failOnError="false" author="Abubakr" id="AddChildrenToKyc">
        <sql>
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(7806531, ***********);
        </sql>
    </changeSet>
</databaseChangeLog>