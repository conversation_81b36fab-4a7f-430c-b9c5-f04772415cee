package com.cit.vericash.apis.sanValidateOtp;

import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.CacheableWalletInfo;
import com.cit.vericash.apis.commons.util.MsisdnFormatterImpl;
import com.cit.vericash.backend.commons.dynamicinputparameters.mapping.DynamicPayloadTransformer;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.sanValidateOtp.SanValidateOTPRequestTransformer")
public class SanValidateOTPRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Autowired
    MsisdnFormatterImpl msisdnFormatter;
    @Autowired
    private CacheableWalletInfo walletInfoCache;
    private final String MSISDN = "msisdn";
    private final String WALLET_SHORT_CODE = "wallet-Short-Code";
    private final String USER_KEY = "user-Key";
    private final String IS_SELF_REG = "is-self-reg";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    @Autowired
    DynamicPayloadTransformer dynamicPayloadTransformer;

    @Override
    @SneakyThrows
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        Payload payload = (Payload) dynamicPayloadTransformer.transform(message.getDynamicPayload());
        String walletShortCodeKey = cacheableFieldParameters.getRequestParameters(WALLET_SHORT_CODE);
        String walletShortCode = message.getHeader().getAttributeAsString(walletShortCodeKey);
        WalletInfo walletInfo = walletInfoCache.getWalletInfo(walletShortCode);
        String msisdn = payload.getAttributeAsString("msisdn");
        String region = walletInfo.getCountryIso2();
        msisdn = msisdnFormatter.formatMSISDN(msisdn, region, walletShortCode);
        Map msisdnInputParam = (LinkedHashMap) (message.getDynamicPayload().getPayload().getAttribute("218"));
        if (msisdnInputParam != null && msisdnInputParam.size() > 0)
            msisdnInputParam.put("parameterValue", msisdn);
        message.getDynamicPayload().getPayload().put("218", msisdnInputParam);
    }

}
