package com.cit.vericash.apis.getcashoutrequests;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.VoucherTypeCustom;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.getcashoutrequests.GetCashOutResponseTransformer")
public class GetCashOutResponseTransformer implements MuleResponseTransformer {
    private final String vouchersKey = "vouchers";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();

        try {
            Map<String, String> result = new LinkedHashMap<String, String>();
            ArrayList<VoucherTypeCustom> vouchersList = (ArrayList<VoucherTypeCustom>) businessMessage.getVoucherTypeList();
            if (vouchersList == null) {
                vouchersList = new ArrayList<VoucherTypeCustom>() {
                };
            }
            ObjectMapper mapper = new ObjectMapper();
            result.put(cacheableFieldParameters.getResponseParameters(vouchersKey), mapper.writeValueAsString(vouchersList));
            return result;

        } catch (Exception e) {
            throw e;
        }
    }

}
