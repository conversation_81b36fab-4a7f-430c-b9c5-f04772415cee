ViewBeneficiary=SELECT B.ID, c.ID AS CURRENCY_ID, B.CURRENCY_CODE, B.NAME, B.INPUT_PARAMETERS, B.PAYMENT_METHOD_TYPE,\
  \ B.STATUS, PM.NAME AS PAYMENT_METHOD_NAME,cs.MSISDN AS MOBILE_NUMBER,co.COUNTRY_CODE,B.ALIAS,B.BENEFICIARY_USER_ID \
  FROM BENEFICIARY B JOIN PAYMENT_METHOD PM ON B.PAYMENT_METHOD_TYPE = PM.ID \
  LEFT JOIN CURRENCY c ON b.CURRENCY_CODE = c.CODE \
  LEFT JOIN CUSTOMER cs ON B.BENEFICIARY_USER_ID = cs.CUSTOMER_ID \
  LEFT JOIN COUNTRY co ON cs.COUNTRY = co.COUNTRY_ID \
  WHERE B.USER_ID = %1$s AND B.STATUS = 1 AND B.%2$s is not NULL

ViewBeneficiaryPaymentMethod=SELECT B.ID, c.ID AS CURRENCY_ID, B<PERSON>CURRENCY_CODE, B.NAME, B.INPUT_PARAMETERS, B.PAYMENT_METHOD_TYPE, B.STATUS, PM.NAME AS PAYMENT_METHOD_NAME \
  FROM BENEFICIARY B JOIN PAYMENT_METHOD PM ON B.PAYMENT_METHOD_TYPE = PM.ID LEFT JOIN CURRENCY c ON b.CURRENCY_CODE = c.CODE  WHERE USER_ID = %1$s AND STATUS = 1 AND PAYMENT_METHOD_TYPE in %2$s
