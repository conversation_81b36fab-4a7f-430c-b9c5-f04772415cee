package com.cit.vericash.util;


import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.api.handler.ExceptionHandlerFactory;
import com.cit.vericash.api.handler.ExceptionHandlerStrategy;
import com.cit.vericash.apis.commons.util.CachableApiError;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.dto.response.Response;
import com.cit.vericash.backend.commons.dynamicpayload.EchoData;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ResponseExceptionBuilder {

    private Logger log = LoggerFactory.getLogger(ResponseExceptionBuilder.class);

    @Autowired
    private CachableApiError cachableApiError;

    @Autowired
    private ExceptionHandlerFactory exceptionHandlerFactory;

    @Autowired
    private ApplicationContext context;

    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    LanguageUtil languageUtil;

    @Autowired
    ValidationRequest validationRequest;

   public Response buildResponse( Throwable ex,Request request ) throws Exception {
       String apiCode = request.getMessage().getHeader().getAttributeAsString("apiCode");
       String channelCode = request.getMessage().getHeader().getAttributeAsString("channelCode");
       validationRequest.validateRequest(request);
       String sessionIdMacKey = request.getMessage().getPayload().containsKey("sessionIdMacKey") ?
               request.getMessage().getPayload().getAttributeAsString("sessionIdMacKey") : null;
       String sessionIdWalletShortCode = request.getMessage().getPayload().containsKey("sessionIdWalletShortCode") ?
               request.getMessage().getPayload().getAttributeAsString("sessionIdWalletShortCode") : null;
       String sessionIdSenderIdentifyValue = request.getMessage().getPayload().containsKey("sessionIdSenderIdentifyValue") ?
               request.getMessage().getPayload().getAttributeAsString("sessionIdSenderIdentifyValue") : null;
       String sessionId= request.getMessage().getHeader().containsKey("sessionId") ?request.getMessage().getHeader().getAttributeAsString("sessionId"):null;
       Boolean isClearCash=null;
       if (request.getMessage().getPayload()!=null&&request.getMessage().getPayload().getAttribute("isClearCash")!=null){
            isClearCash= (boolean) request.getMessage().getPayload().getAttribute("isClearCash");
       }

       ExceptionHandlerStrategy exceptionHandlerStrategy = exceptionHandlerFactory.getExceptionHandler(ex);
      String responseStr = exceptionHandlerStrategy.handleException(ex);
      Response response = new Gson().fromJson(responseStr,Response.class);
       String language = request.getMessage().getPayload().getAttributeAsString("language");
       boolean customExceptionHandler = exceptionHandlerStrategy.equals(context.getBean("customExceptionHandler"));
       if (customExceptionHandler){
           response = getUpdatedErrorMessage(language, response);
       }
       response.getResponse().setApiCode(apiCode);
       response.getResponse().setChannelCode(channelCode);
       if (request.getMessage().getEchoData() != null) {
           response.getResponse().setEchoData(request.getMessage().getEchoData());
       } else {
           EchoData echoData = new EchoData();
           response.getResponse().setEchoData(echoData);
       }
       response.getResponse().getEchoData().setAttribute("sessionIdMacKey", sessionIdMacKey);
       response.getResponse().getEchoData().setAttribute("sessionIdWalletShortCode", sessionIdWalletShortCode);
       response.getResponse().getEchoData().setAttribute("sessionIdSenderIdentifyValue", sessionIdSenderIdentifyValue);
       if (sessionId!=null){
           response.getResponse().setSessionId(sessionId);
       }
       if (isClearCash!=null){
           response.getResponse().setClearCash(isClearCash);
       }
      return response;
    }

    private Response getUpdatedErrorMessage(String language, Response response){
        String translatedFailureMessage = getTranslatedFailureMessage(response.getResponse().getResponseError().getErrorCode(), language);
        response.getResponse().getResponseError().setErrorMessage(translatedFailureMessage);
        return response;
    }

    private String getTranslatedFailureMessage(String errorCode,String language){
        if (language != null){
            if (languageUtil.isAnotherLanguage(language)){
                language="ERROR_DESCRIPTION_"+language.toUpperCase();
            }else{
                language="ERROR_DESCRIPTION";
            }
        }else{
            language="ERROR_DESCRIPTION";
        }

        String sQLQuery = ServiceQueryEngine.getQueryStringToExecute("getFailureMsg",this.getClass(),errorCode);
        List<Parameter> parameters = new ArrayList<>();
        parameters.add(new Parameter(1,errorCode));
        List<Record> records = connectionUtil.executeSelect(sQLQuery, parameters);
        if (records != null && !records.isEmpty()){
            return records.get(0).getValueAsString(language);
        }else{
            log.error("Error message not found in Api Error table");
            return null;
        }
    }


}
