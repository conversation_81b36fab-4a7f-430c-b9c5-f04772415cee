package com.cit.vericash.apis.IswCustomerValidation;

import com.cit.mpaymentapp.common.customer.message.IswValidationResponseFiled;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class IswCustomerValidationResponseTransformer implements MuleResponseTransformer {
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();

        List<IswValidationResponseFiled> iswValidationResponseFileds = businessMessage.getIswValidationResponse();
        result.put("IswValidationResponse", iswValidationResponseFileds);
        return result;
    }

}
