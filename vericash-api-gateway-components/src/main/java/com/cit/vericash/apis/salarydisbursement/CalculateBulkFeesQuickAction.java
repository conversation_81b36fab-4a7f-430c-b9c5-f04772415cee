package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.service.commons.codeMapping.PaymentMethodDto;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.CalculateBulkFeesQuickAction")
public class CalculateBulkFeesQuickAction implements VericashAction {


    @Autowired
    CalculateBulkFeesCommonService calculateBulkFeesCommonService;

    @Autowired
    ConnectionUtil connectionUtil;

    @Override
    public Object process(Request request) throws Exception {
        BusinessMessage businessMessage = new BusinessMessage();
        businessMessage = getRecipientsAndCalculateFee(request);
        BigDecimal totalFees = businessMessage.getParameters().getAttributeAsBigDecimal("totalFees");
//        BigDecimal totalVat=businessMessage.getParameters().getAttributeAsBigDecimal("totalVat");
        BigDecimal totalRecipientsAmount = businessMessage.getParameters().getAttributeAsBigDecimal("totalRecipientsAmount");
        PaymentMethodDto sourcePaymentMethodMap = (PaymentMethodDto) request.getMessage().getPayload().getAttribute("sourcePaymentMethod");

        String currency = String.valueOf(sourcePaymentMethodMap.getCurrency());
        String totalAmount = totalFees.add(totalRecipientsAmount).toString();

        HashMap<String, Object> calculateBulkFeesMap = new HashMap<>();
        calculateBulkFeesMap.put("TransactionAmount", currency + totalRecipientsAmount.toString());
        calculateBulkFeesMap.put("TransactionFeeAmount", currency + totalFees.toString());
        calculateBulkFeesMap.put("TransactionTotalAmount", currency + totalAmount);
        return calculateBulkFeesMap;
    }

    public BusinessMessage getRecipientsAndCalculateFee(Request request) throws Exception {
        BusinessMessage businessMessage = new BusinessMessage();

        Integer groupTransferSetupId = request.getMessage().getPayload().getAttributeAsInteger("groupSetupId");
        List<Record> recipientRecords = getRecipientsForNormalFlow(groupTransferSetupId);
        businessMessage = (BusinessMessage) calculateBulkFeesCommonService.calculateFee(request, recipientRecords);

        return businessMessage;
    }

    public List<Record> getRecipientsForNormalFlow(Integer groupTransferSetupId) {
        //get all recipients for group transfer setup1
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getRecipientsForNormalFlow" , this.getClass(),String.valueOf(groupTransferSetupId));
/*
        String transferRecipientsQuery = "SELECT gts.GROUP_NAME,c.CUSTOMER_FEE_PROFILE_ID ,gtr.TRANSFER_ID,gtr.AMOUNT,gtr.BENEFICIARY_ID,gtr.BENEFICIARY_NAME,gtr.BRANCH_CODE,gtr.BRANCH_NAME,gtr.CARD_NUMBER,gtr.COUNTRY_NAME,gtr.INSTITUTION_CODE,gtr.INSTITUTION_NAME,gtr.TRANSFER_TYPE,gtr.TRANSFER_TYPE_CATEGORY,gtr.WALLET_SHORT_CODE,gtr.GROUP_TRANSFER_SETUP_ID,gtr.STATUS,gtr.CLIENT_ID,gtr.RECEIVER_TYPE,gtr.WALLET_PROVIDER,gtr.CURRENCY,gtr.PAYMENT_ALIAS,gtr.MOBILE_NUMBER,gtr.IS_DELETED,gtr.VALIDATION_ERROR,gtr.GAR_ID,gtr.PAYMENT_METHOD_TYPE ,cg.ACCOUNT_ID, gtr.CUSTOMER_ID  FROM GROUP_TRANSFER_RECIPIENT gtr INNER JOIN CUSTOMER_GAR cg  ON gtr.GAR_ID = cg.GARID INNER JOIN CUSTOMER c ON c.CUSTOMER_ID = gtr.CUSTOMER_ID INNER JOIN GROUP_TRANSFER_SETUP gts ON gts.GROUP_TRANSFER_SETUP_ID = gtr.GROUP_TRANSFER_SETUP_ID  WHERE gtr.GROUP_TRANSFER_SETUP_ID= " + groupTransferSetupId;
*/
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields = setRecipientsFields(fields);
        List<Record> recipientRecords = connectionUtil.executeSelect(sqlQuery, parameters, fields);

        return recipientRecords;
    }

    public List<String> setRecipientsFields(List<String> fields) {
        fields.add("TRANSFER_ID");
        fields.add("AMOUNT");
        fields.add("BENEFICIARY_ID");
        fields.add("BENEFICIARY_NAME");
        fields.add("BRANCH_CODE");
        fields.add("BRANCH_NAME");
        fields.add("CARD_NUMBER");
        fields.add("COUNTRY_NAME");
        fields.add("INSTITUTION_CODE");
        fields.add("INSTITUTION_NAME");
        fields.add("TRANSFER_TYPE");
        fields.add("TRANSFER_TYPE_CATEGORY");
        fields.add("WALLET_SHORT_CODE");
        fields.add("GROUP_TRANSFER_SETUP_ID");
        fields.add("STATUS");
        fields.add("CLIENT_ID");
        fields.add("RECEIVER_TYPE");
        fields.add("WALLET_PROVIDER");
        fields.add("CURRENCY");
        fields.add("PAYMENT_ALIAS");
        fields.add("MOBILE_NUMBER");
        fields.add("IS_DELETED");
        fields.add("VALIDATION_ERROR");
        fields.add("GAR_ID");
        fields.add("PAYMENT_METHOD_TYPE");

        fields.add("ACCOUNT_ID");
        fields.add("CUSTOMER_ID");
        fields.add("CUSTOMER_FEE_PROFILE_ID");
        fields.add("GROUP_NAME");
        return fields;
    }
}