package com.cit.vericash.dynamicrequest.transformer;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

public enum DynamicObjectTypeEnum {

    MAP("1"),
    ARRAY("2"),
    OBJECT("3");
    private String type;

    DynamicObjectTypeEnum(String type) {
        this.type = type;

    }

    public static Object getObjectType(String type) {
        if (Objects.equals(type, "1"))
            return new HashMap<>();
        else if (Objects.equals(type, "2")) {
              List<HashMap<String, Object>> lst = new ArrayList <HashMap<String,Object>>();
              lst.add(new HashMap<String,Object>());
              return  lst ;
        }
        else if (Objects.equals(type, "3"))
            return new Object();
        else return null;
    }



}
