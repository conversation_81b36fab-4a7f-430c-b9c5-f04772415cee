package com.cit.vericash.apis.forgetusernamebyconsumer;

import com.cit.mpaymentapp.common.message.AuthContainer;
import com.cit.mpaymentapp.common.message.AuthenticationModel;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.forgetusernamebyconsumer.ForgetUserNameByConsumerResponseTransformer")
public class ForgetUserNameByConsumerResponseTransformer implements MuleResponseTransformer {
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    private String userNameKey = "user_name";

    public Object transform(BusinessMessage businessMessage) throws Exception {
//        cacheableFieldParameters= CacheableFieldParametersImpl.getInstance();
        String userId = businessMessage.getPrimarySenderInfo().getUserId();
        Map<String, String> result = new HashMap<String, String>();

        HashMap<Object, List<Map<String, Object[]>>> resultMissingAuth = new HashMap<Object, List<Map<String, Object[]>>>();

        boolean skipDynamicAuth = businessMessage.getParameters().getAttributeAsBoolean("skipValidateMissingDynamicAuthentication");
        if (!skipDynamicAuth) {
            AuthContainer authContainer = businessMessage.getAuthContainer();
            AuthenticationModel[] authenticationModelList = authContainer.getAuthenticationModels();
            List<Map<String, Object[]>> authContainers = new LinkedList<Map<String, Object[]>>();
            Map<String, Object[]> authmodel = new HashMap<String, Object[]>();
            authmodel.put("authenticationModels", authenticationModelList);
            authContainers.add(authmodel);
            resultMissingAuth.put("authContainers", authContainers);
            return resultMissingAuth;
        } else {
            //        String userName=message.getPrimarySenderInfo().getUserId();
            String userNameVal = cacheableFieldParameters.getResponseParameters(userNameKey);
            result.put(userNameVal, userId);
            return result;
        }

    }
}

