package com.cit.vericash.apis.activateCustomerByAgent;

import com.cit.mpaymentapp.common.message.Gender;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.EnumTransformer;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.activateCustomerByAgent.ActivateCustomerByAgentRequestTransformer")
public class ActivateCustomerByAgentRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    private final String GENDER = "gender";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

        try {
//			cacheableFieldParameters = CacheableFieldParametersImpl.getInstance();
            //	String ownerTypeKey = cacheableFieldParameters.getRequestParameters(OWNERTYPE);
            String genderKey = cacheableFieldParameters.getRequestParameters(GENDER);
            String gender = message.getPayload().getAttributeAsString(genderKey);
            //	String ownerTypeString = message.getPayload().getAttributeAsString(ownerTypeKey);
            Gender genderEnum = EnumTransformer.convertGender(gender);
            message.getPayload().put(genderKey, genderEnum);


        } catch (Exception e) {
            throw e;
        }
    }

}
