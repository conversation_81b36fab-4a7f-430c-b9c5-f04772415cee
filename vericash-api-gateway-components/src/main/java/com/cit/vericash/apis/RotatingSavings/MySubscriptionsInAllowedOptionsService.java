package com.cit.vericash.apis.RotatingSavings;

import com.cit.vericash.api.commons.dto.SimpleGenericDTO;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.MyOrganizedAllowedOptionsService")
public class MySubscriptionsInAllowedOptionsService {
    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    List<SimpleGenericDTO> allowedOptionsList;
    List<SimpleGenericDTO> allowedOptionsFullList;

    public void processAllowedOptionsJson() throws JsonProcessingException {
        String json = propertyLoaderComponent.getPropertyAsString("my.subscriptions.in.options", "application", "application");
        ObjectMapper mapper = new ObjectMapper();
        mapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
        this.allowedOptionsFullList = mapper.readValue(json, new TypeReference<List<SimpleGenericDTO>>() {
        });
    }

    public List<SimpleGenericDTO> getAllowedOptionsList(Integer status, Integer memberStatus, Date subscriptionStartDate, Date subscriptionEndDate) throws Exception {
        processAllowedOptionsJson();
        handleAllowedOptions(status, memberStatus, subscriptionStartDate, subscriptionEndDate);
        return this.allowedOptionsList;
    }

    private void handleAllowedOptions(Integer status, Integer memberStatus, Date subscriptionStartDate, Date subscriptionEndDate) {
        this.allowedOptionsList = new ArrayList<>();
        checkUnsubscribe(status, subscriptionEndDate);
        checkReviewChange(memberStatus);
        checkPayContributionAmount(status);
        checkViewMyPaymentHistory(status);

    }

    private void checkUnsubscribe(Integer status, Date subscriptionEndDate) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
        Date currentDate = java.util.Calendar.getInstance().getTime();
        if (status == 0 || status == 1 || currentDate.before(subscriptionEndDate)) {
            this.allowedOptionsList.add(allowedOptionsFullList.get(0));
        }
    }

    private void checkReviewChange(Integer memberStatus) {
        if (memberStatus == 3) {
            this.allowedOptionsList.add(allowedOptionsFullList.get(1));
        }
    }

    private void checkPayContributionAmount(Integer status) {
        if (status == 3 || status == 6) {
            this.allowedOptionsList.add(allowedOptionsFullList.get(2));
        }
    }

    private void checkViewMyPaymentHistory(Integer status) {
        if (status == 3 || status == 5 || status == 6 || status == 7) {
            this.allowedOptionsList.add(allowedOptionsFullList.get(3));
        }
    }

}
