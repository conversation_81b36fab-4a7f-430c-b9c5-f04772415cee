package com.cit.vericash.apis.calculatefee;

import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.mastercard.api.core.exception.ApiException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class FeeProfileLoaderComponentTest {
    @Mock
    private ConnectionUtil connectionUtil;

    @InjectMocks
    private FeeProfileLoaderComponent feeProfileLoaderComponent;

    @Before
    public void setup() {
        // Initialize fields and parameters
        feeProfileLoaderComponent.init();
    }

    @Test
    public void testLoadCustomerFeeProfile_HappyPath() throws Exception {
        // Arrange
        Long customerId = 1L;
        String queryName = "getCustomerFeeProfile";
        List<Record> records = new ArrayList<>();
        Record record = new Record();
        record.put("FEE_PROFILE_ID", 123L);
        records.add(record);
        doReturn(records).when(connectionUtil).executeSelect(anyString(), anyList(), anyList());

        // Act
        Long feeProfileId = feeProfileLoaderComponent.loadCustomerFeeProfile(customerId);

        // Assert
        assertNotNull(feeProfileId);
        assertEquals(123L, feeProfileId.longValue());
        verify(connectionUtil).executeSelect(anyString(), anyList(), anyList());
    }

    @Test
    public void testLoadCustomerFeeProfile_NoRecordsFound() throws Exception {
        // Arrange
        Long customerId = 1L;
        String queryName = "getCustomerFeeProfile";
        List<Record> records = new ArrayList<>();
        doReturn(records).when(connectionUtil).executeSelect(anyString(), anyList(), anyList());

        // Act and Assert
        try {
            feeProfileLoaderComponent.loadCustomerFeeProfile(customerId);
            fail("Expected ApiException to be thrown");
        } catch (ApiException e) {
            assertEquals(ErrorCode.UNABLE_TO_LOAD_CUSTOMER.getErrorCode(), e.getMessage());
        }
    }

    @Test
    public void testLoadCustomerFeeProfile_MultipleRecordsFound() throws Exception {
        // Arrange
        Long customerId = 1L;
        String queryName = "getCustomerFeeProfile";
        List<Record> records = new ArrayList<>();
        Record record1 = new Record();
        record1.put("FEE_PROFILE_ID", 123L);
        Record record2 = new Record();
        record2.put("FEE_PROFILE_ID", 456L);
        records.add(record1);
        records.add(record2);
        doReturn(records).when(connectionUtil).executeSelect(anyString(), anyList(), anyList());

        // Act and Assert
        try {
            feeProfileLoaderComponent.loadCustomerFeeProfile(customerId);
            fail("Expected ApiException to be thrown");
        } catch (ApiException e) {
            assertEquals(ErrorCode.UNABLE_TO_LOAD_CUSTOMER.getErrorCode(), e.getMessage());
        }
    }

    @Test
    public void testLoadAgentFeeProfile_HappyPath() throws Exception {
        // Arrange
        Long customerId = 1L;
        String queryName = "getAgentFeeProfile";
        List<Record> records = new ArrayList<>();
        Record record = new Record();
        record.put("FEE_PROFILE_ID", 123L);
        records.add(record);
        doReturn(records).when(connectionUtil).executeSelect(anyString(), anyList(), anyList());

        // Act
        Long feeProfileId = feeProfileLoaderComponent.loadAgentFeeProfile(customerId);

        // Assert
        assertNotNull(feeProfileId);
        assertEquals(123L, feeProfileId.longValue());
        verify(connectionUtil).executeSelect(anyString(), anyList(), anyList());
    }
    @Test
    public void testLoadAgentFeeProfile_NoRecordsFound() throws Exception {
        // Arrange
        Long customerId = 1L;
        String queryName = "getAgentFeeProfile";
        List<Record> records = new ArrayList<>();
        doReturn(records).when(connectionUtil).executeSelect(anyString(), anyList(), anyList());

        // Act and Assert
        try {
            feeProfileLoaderComponent.loadAgentFeeProfile(customerId);
            fail("Expected ApiException to be thrown");
        } catch (ApiException e) {
            assertEquals(ErrorCode.UNABLE_TO_LOAD_CUSTOMER.getErrorCode(), e.getMessage());
        }
    }

    @Test
    public void testLoadAgentFeeProfile_MultipleRecordsFound() throws Exception {
        // Arrange
        Long customerId = 1L;
        String queryName = "getAgentFeeProfile";
        List<Record> records = new ArrayList<>();
        Record record1 = new Record();
        record1.put("FEE_PROFILE_ID", 123L);
        Record record2 = new Record();
        record2.put("FEE_PROFILE_ID", 456L);
        records.add(record1);
        records.add(record2);
        doReturn(records).when(connectionUtil).executeSelect(anyString(), anyList(), anyList());

        // Act and Assert
        try {
            feeProfileLoaderComponent.loadAgentFeeProfile(customerId);
            fail("Expected ApiException to be thrown");
        } catch (ApiException e) {
            assertEquals(ErrorCode.UNABLE_TO_LOAD_CUSTOMER.getErrorCode(), e.getMessage());
        }
    }
}