<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">


    <changeSet failOnError="false" author="Abubakr" id="AddValidationWithoutAuth">
        <sql>
            INSERT INTO API_VERICASH_APIS (CODE, NAME, SERVICE_CODE, REQUEST_ACTION, RESPONSE_ACTION, OUTBOUND_TO_MULE, SUCCESS_MESSAGE, INBOUND_QUEUE, OUTBOUND_QUEUE, SCHEMA_CONFIG_FILE, MAPPING_CONFIG_FILE, REQUEST_TRANSFORMER, COMPLETE_API_CODE, TRANSACTION_API, IS_ASYNC, VALIDATION_TYPE, DTO_CLASS_PATH, END_POINT, SERVICE_NAME, REQUEST_TYPE, IS_EXTERNAL_INTEGRATION) VALUES('889100', 'Validate PM And Get Details without dynamic auth', '889099', NULL, 'com.cit.vericash.apis.integrationfermwork.apis.VaildatePMSResponseTransformer', 0, 'Validate PM And Get Details Successfully!', 'jms/uba/service/api/request/synch/input', 'jms/uba/service/api/response/synch/output', 'ValidatePMAndGetDetails.json', 'ValidatePMAndGetDetails.xml', 'com.cit.vericash.apis.integrationfermwork.apis.VaildatePMSRequestTransformer', NULL, 0, 0, 2, NULL, NULL, NULL, 1, 1);
            INSERT INTO API_CHANNEL_JSON_SCHEMA (API_CODE, FILE_NAME, CHANNEL_ID) VALUES('889100', 'ValidatePMAndGetDetails.json', 1);
            INSERT INTO API_CHANNEL_JSON_SCHEMA (API_CODE, FILE_NAME, CHANNEL_ID) VALUES('889100', 'ValidatePMAndGetDetails.json', 2);
        </sql>
    </changeSet>
</databaseChangeLog>