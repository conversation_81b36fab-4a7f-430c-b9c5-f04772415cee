package com.cit.vericash.apis.validateberoleserviceaccessibility;

import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.role.ValidateBeRoleServiceAccessibility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.validateberoleserviceaccessibility.ValidateBeRoleServiceAccessibilityRequestAction")
public class ValidateBeRoleServiceAccessibilityRequestAction implements VericashAction {
    @Autowired
    ValidateBeRoleServiceAccessibility validateBeRoleServiceAccessibility;
    @Autowired
    private CachableVericashApi vericashApiCache;

    @Override
    public Object process(Request request) throws Exception {
        Long apiCode = request.getMessage().getPayload().getAttributeAsLong("serviceApiCode");
        VericashAPI vericashAPI = vericashApiCache.getVericashAPI(apiCode);
        Long beCustomerId = request.getMessage().getHeader().getAttributeAsLong("customerId");
        Long serviceCode = Long.valueOf(vericashAPI.getServiceCode());
        boolean hasAccess = validateBeRoleServiceAccessibility.checkAccess(beCustomerId, serviceCode);
        if (!hasAccess)
            throw new APIException("Sorry", " you don’t have access to perform this action");

        return null;
    }
}
