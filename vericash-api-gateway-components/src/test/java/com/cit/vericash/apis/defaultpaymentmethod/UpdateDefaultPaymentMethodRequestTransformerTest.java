package com.cit.vericash.apis.defaultpaymentmethod;

import com.cit.mpaymentapp.common.paymentmethod.SourcePaymentMethodDTO;
import com.cit.mpaymentapp.model.customer.Customer;
import com.cit.mpaymentapp.model.customer.CustomerGar;
import com.cit.mpaymentapp.model.payment.SourcePaymentMethods;
import com.cit.shared.error.exception.GarException;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
@RunWith(MockitoJUnitRunner.class)
class UpdateDefaultPaymentMethodRequestTransformerTest {


    private ConnectionUtil connectionUtil= mock(ConnectionUtil.class);


    private UpdateDefaultPaymentMethodRequestTransformer transformer;

    private Message message;
    private SourcePaymentMethodDTO paymentMethodDTO;
    private CustomerGar customerGar;
    private SourcePaymentMethods paymentMethodInSystem;
    private Record garRecord;
    private Record paymentMethodRecord;
    @BeforeEach
    void setUp() {
        garRecord = new Record();
        paymentMethodRecord= new Record();
        transformer = new UpdateDefaultPaymentMethodRequestTransformer(connectionUtil);
        // Setup mock data
        message = new Message();
        Payload payload = new Payload();
        message.setPayload(payload);

        paymentMethodDTO = new SourcePaymentMethodDTO();
        paymentMethodDTO.setPaymentMethodCode(871L);
        paymentMethodDTO.setIsDefaultSender(true);
        paymentMethodDTO.setIsDefaultReciever(true);
        Map<String,Object> pmParams = new HashMap<>();
        pmParams.put("paymentMethodType", 871L);
        pmParams.put("isDefaultSender", 1);
        pmParams.put("isDefaultReciever", 1);
        pmParams.put("paymentMethodCode", 113262L);
        payload.put("sourcePaymentMethod", pmParams);

        customerGar = new CustomerGar();
        customerGar.setGarId(115775L);
        customerGar.setIsDefaultSender(false);
        customerGar.setIsDefaultReciever(false);
        Customer customer = mock(Customer.class);
        customerGar.setCustomer(customer);

        paymentMethodInSystem = new SourcePaymentMethods();
        paymentMethodInSystem.setAllowDefaultSender(true);
        paymentMethodInSystem.setAllowDefaultReceiver(true);

        garRecord.put("CUSTOMER_ID", 115775L);
        garRecord.put("IS_DEFAULT_SENDER", 1);
        garRecord.put("IS_DEFAULT_RECEIVER", 1);
        garRecord.put("GARID",115775L);

        paymentMethodRecord.put("ALLOW_DEFAULT_SENDER", 1);
        paymentMethodRecord.put("ALLOW_DEFAULT_RECEIVER", 1);
    }
//TODO: this is an integration test as Customer class is not mocked
// And it depends on environment variables to create new instance

//    @Test
//    void testSetPaymentMethodDefaultSenderAndReceiver_ValidCase() throws Exception {
//        // Mock methods
//
//        when(connectionUtil.executeSelect(any(String.class), anyList())).thenReturn(List.of(garRecord));
//        when(connectionUtil.executeSelect(anyString(),anyList())).thenReturn(List.of(paymentMethodRecord));
//        // Execute the method under test
//        transformer.setPaymentMethodDefaultSenderAndReceiver(message);
//
//        // Verify interactions and state changes
//        assertFalse(customerGar.getIsDefaultReciever());
//    }

    @Test
    void testSetPaymentMethodDefaultSenderAndReceiver_CustomerGarNotFound() throws GarException {
        // Mock methods
        when(transformer.getCustomerGar(115775L)).thenReturn(null);

        // Assert exception is thrown
        GarException exception = assertThrows(GarException.class, () -> {
            transformer.setPaymentMethodDefaultSenderAndReceiver(message);
        });

        assertEquals("VAL302145", exception.getMessage());
    }

    @Test
    void testUpdateIsDefaultSenderAndReceiver_ExceptionForSenderNotAllowed() {
        paymentMethodInSystem.setAllowDefaultSender(false);

        // Assert exception is thrown when the payment method doesn't allow default sender
        GarException exception = assertThrows(GarException.class, () -> {
            transformer.updateIsDefaultSenderAndReceiver(
                    customerGar.getCustomer(),
                    customerGar,
                    paymentMethodDTO,
                    paymentMethodInSystem
            );
        });

        assertEquals(GarException.PAYMENT_METHOD_DOES_NOT_ALLOW_DEFAULT_SENDER, exception.getMessage());
    }

    @Test
    void testUpdateIsDefaultSenderAndReceiver_ExceptionForReceiverNotAllowed() {
        paymentMethodInSystem.setAllowDefaultReceiver(false);

        // Assert exception is thrown when the payment method doesn't allow default receiver
        GarException exception = assertThrows(GarException.class, () -> {
            transformer.updateIsDefaultSenderAndReceiver(
                    customerGar.getCustomer(),
                    customerGar,
                    paymentMethodDTO,
                    paymentMethodInSystem
            );
        });

        assertEquals(GarException.PAYMENT_METHOD_DOES_NOT_ALLOW_DEFAULT_RECEIVER, exception.getMessage());
    }
}
