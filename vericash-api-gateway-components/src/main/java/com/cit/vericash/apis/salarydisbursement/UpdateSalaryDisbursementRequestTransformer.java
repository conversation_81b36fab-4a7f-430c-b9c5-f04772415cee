package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.common.group.transfers.SetupGroupTransferDTO;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.UpdateSalaryDisbursementRequestTransformer")
public class UpdateSalaryDisbursementRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    private final String setupGroupTransferId = "setupGroupTransferId";
    private CacheableFieldParameters cacheableFieldParameters;

    public UpdateSalaryDisbursementRequestTransformer(CacheableFieldParameters cacheableFieldParameters) {
        this.cacheableFieldParameters = cacheableFieldParameters;
    }

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        try {
            //String groupIdKey = cacheableFieldParameters.getRequestParameters(setupGroupTransferId);
            Long groupId = message.getPayload().getAttributeAsLong("groupId");
            List<SetupGroupTransferDTO> myGroupTransfers = new ArrayList<SetupGroupTransferDTO>();
            SetupGroupTransferDTO setupGroupTransferDTO = new SetupGroupTransferDTO();
            setupGroupTransferDTO.setId(groupId);
            myGroupTransfers.add(setupGroupTransferDTO);
            message.getPayload().put("myGroupTransfersList", myGroupTransfers);
        } catch (Exception e) {
            throw e;
        }
    }
}
