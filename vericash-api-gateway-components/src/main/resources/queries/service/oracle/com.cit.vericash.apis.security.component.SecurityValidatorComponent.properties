getSecurityConfiguration=SELECT API_CHANNEL_SECURITY_CONFIG.ID, API_CODE, API_CHANNEL.CHANNEL_NAME, \
  \"SCOPE\", CLASS_IMPL, API_CHANNEL.WALLET_SHORT_CODE \
  FROM \
  API_CHANNEL_SECURITY_CONFIG \
  LEFT JOIN API_SECURITY_IMPL ON (API_SECURITY_IMPL.ID = API_CHANNEL_SECURITY_CONFIG.SECURITY_IMPL_ID) \
  LEFT JOIN API_CHANNEL ON (API_CHANNEL_SECURITY_CONFIG.CHANNEL_IDD = API_CHANNEL.ID) \
  WHERE ENABLED = 1 ORDER BY \"ORDER\" ASC
apiChannelSecurityParameter=SELECT KEY,VALUE FROM API_CHANNEL_SECURITY_PARAMS WHERE ID in \
  (SELECT SECURITY_PARAMS_ID FROM API_SECURITY_CONFIG_PARAMS WHERE SECUIRTY_CONFIG_ID = %1$s)