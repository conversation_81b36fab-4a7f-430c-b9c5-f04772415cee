<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">



    <changeSet id="updated API configs" author="Amir Nabil">
        <sql>
            UPDATE PRO_MI_NO_C.API_VERICASH_APIS t
            SET t.SCHEMA_CONFIG_FILE  = 'OpenBankAccount.json',
                t.MAPPING_CONFIG_FILE = 'OpenBankAccount.xml',
                t.REQUEST_TRANSFORMER        = 'com.cit.vericash.apis.openBankAccount.createBankAccountTransformer'
            WHERE t.CODE LIKE '112007' ESCAPE '#';
        </sql>
    </changeSet>

    <changeSet id="added channel json records" author="Amir Nabil">
        <sql>
            INSERT INTO PRO_MI_NO_C.API_CHANNEL_JSON_SCHEMA (API_CODE, FILE_NAME, CHANNEL_ID)
            VALUES ('112007', 'OpenBankAccount.json', 1);

            INSERT INTO PRO_MI_NO_C.API_CHANNEL_JSON_SCHEMA (API_CODE, FILE_NAME, CHANNEL_ID)
            VALUES ('112007', 'OpenBankAccount.json', 2);

        </sql>
    </changeSet>

    <changeSet id="remove service steps" author="Amir Nabil">
        <sql>
            DELETE
            FROM PRO_MI_NO_C.BUSINESS_SERVICE_STEP
            WHERE ID = 166565657780022277;

            DELETE
            FROM PRO_MI_NO_C.BUSINESS_SERVICE_STEP
            WHERE ID = 166565657780022276;

            UPDATE PRO_MI_NO_C.BUSINESS_SERVICE_STEP t
            SET t.STEP_ORDER = 4
            WHERE t.ID = 166565657780022278;

            UPDATE PRO_MI_NO_C.BUSINESS_SERVICE_STEP t
            SET t.STEP_ORDER = 5
            WHERE t.ID = 166565657780022279;


        </sql>
    </changeSet>
</databaseChangeLog>