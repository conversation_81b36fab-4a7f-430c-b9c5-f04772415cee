package com.cit.vericash.util;

import com.cit.mpaymentapp.model.Enums;
import com.cit.service.commons.codeMapping.ServiceCodeMappingComponent;
import com.cit.vericash.api.commons.dto.DynamicGroupParametersDTOValidator;
import com.cit.vericash.api.commons.dto.DynamicParametersDTOValidator;
import com.cit.vericash.api.components.JsonValidationStrategy;
import com.cit.vericash.api.components.impl.DynamicServiceValidationImpl;
import com.cit.vericash.api.transformerfactory.RequestTransformer;
import com.cit.vericash.api.transformerfactory.TransformerFactory;
import com.cit.vericash.apis.commons.exceptions.ApiValidationException;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.pupulateSenderInfoUtil;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicmodelmapper.RequestType;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.*;
import java.util.regex.Pattern;
@Component
@RequiredArgsConstructor
@Slf4j
public class RequestParsingUtils {
    public static final DateTimeFormatter FORMATTER = new DateTimeFormatterBuilder()
            .appendPattern("yyyy-MM-dd HH:mm:ss")
            .optionalStart()
            .appendPattern(".SSS")
            .optionalEnd()
            .toFormatter();
    public static final String DOT = ".";
    public static final String T = "T";
    private final CachableVericashApi vericashApiCache;
    private final TransformerFactory transformerFactory;
    private final PropertyLoaderComponent propertyLoaderComponent;
    private final ServiceCodeMappingComponent serviceCodeMappingComponent;
    private final CachableVericashApi cachableVericashApi;
    private final DynamicServiceValidationImpl dynamicServiceValidation;
    private final JsonValidationStrategy jsonValidationStrategy;
    private final pupulateSenderInfoUtil populateSenderInfoUtil;
    private final ObjectMapper mapper = new ObjectMapper();
    /**
     * getValidDateTime: parses a given dateTimeString object for given dateTimeFormatter and returns the LocalDateTimeObject for that format
     * @param dateTimeString: the string that you want to parse
     * @param dateTimeFormatter: the format you want to extract from the string
     * @param isEndOfDay: boolean to decide the time to return if the string didn't provide time
     * @return A LocalDateTime object with the specified date and time in the string,
     *          if the string doesn't contain the time information in it it returns based on the boolean if it is at end of day or not
     *         or {@code null} if the string is null
     * @throws ApiValidationException If an error occurs during date time parsing of the string.
     * */
    public static LocalDateTime getValidDateTime(String dateTimeString, DateTimeFormatter dateTimeFormatter, boolean isEndOfDay) {
        if (dateTimeString == null) {
            return null;
        }
        dateTimeString = dateTimeString.trim();
        if (dateTimeString.contains(DOT)) {
            dateTimeString = dateTimeString.substring(0, dateTimeString.indexOf(DOT));
        }
        if (dateTimeString.contains(T)) {
            dateTimeString = dateTimeString.replace(T, " ");
        }
        if (dateTimeFormatter == null) {
            dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        }
        try {
            Pattern timePattern = Pattern.compile("\\d{2}:\\d{2}:\\d{2}");

            if (timePattern.matcher(dateTimeString).find()) {
                return LocalDateTime.parse(dateTimeString, dateTimeFormatter);
            } else {
                if (isEndOfDay){
                    return LocalDateTime.parse(dateTimeString + " 23:59:59", dateTimeFormatter);
                }
                return LocalDateTime.parse(dateTimeString + " 00:00:00", dateTimeFormatter);

            }
        } catch (Exception e) {
            throw new ApiValidationException(List.of("invalid date time format must be of pattern yyyy-MM-dd HH:mm:ss"));
        }
    }

    public static String getValidDateTimeString(LocalDateTime date, DateTimeFormatter formatter) {
        String formattedDate = date.format(formatter).replace("T", " ");
        if (formattedDate.contains(DOT)) {
            formattedDate = formattedDate.substring(0, formattedDate.indexOf(DOT));
        }
        return formattedDate;
    }

    /**
     * parseRequestPayloadForGivenKeys: parses a given request object for given keys if present in the request payload it returns the keys
     * @param originalRequest: the request that you want to parse
     * @param inputParameterCodes: the input parameters codes you want to extract from the request dynamic group
     * @param keys: the keys you want to extract from the request
     * @return A map containing the specified keys and their corresponding values if found in the request payload,
     *         or {@code null} if the request payload cannot be parsed or if the specified keys are not found.
     * @throws JsonProcessingException If an error occurs during JSON parsing of the request payload.
     * @throws NullPointerException If the original request is {@code null}.
     * */
    public Map<Object, Object> parseRequestPayloadForGivenKeys(Object originalRequest, String[] inputParameterCodes, String[] keys){
        if (originalRequest == null) {
            return null;
        }
        Request request;
        ObjectMapper objectMapper = new ObjectMapper();
        if (originalRequest instanceof String){
            try {
                request = objectMapper.readValue((String) originalRequest, Request.class);
            } catch (JsonProcessingException e) {
                System.err.println(e.getMessage());
                return null;
            }
        }
        else if (originalRequest instanceof Request){
//            Request originalRequestObj = (Request) originalRequest;
//            request = new Request();
//            request.setMessage(originalRequestObj.getMessage());
//            request.setSignature(originalRequestObj.getSignature());
//            request.setEncyptedMessage(originalRequestObj.getEncyptedMessage());
            try {
                request = objectMapper.readValue(objectMapper.writeValueAsString(originalRequest), Request.class);
            } catch (JsonProcessingException e) {
                System.err.println(e.getMessage());
                return null;
            }
        }
        else {
            return null;
        }
        Map<Object, Object> transactionDetails = new HashMap<>();
        try {
            if (request.getMessage().getPayload().get("dynamicGroup") != null && inputParameterCodes != null && inputParameterCodes.length > 0){
                List<String> inputParamsCodes = Arrays.asList(inputParameterCodes);
                List<DynamicGroupParametersDTOValidator> inputParametersGroups = (List<DynamicGroupParametersDTOValidator>) request.getMessage().getPayload().getAttribute("dynamicGroup");
                List<DynamicGroupParametersDTOValidator> inputParamtersGroupsList = objectMapper.convertValue(inputParametersGroups, new TypeReference<>() {});
                inputParamtersGroupsList.forEach(inputParametersGroup -> {
                    if (transactionDetails.get("groupId") == null){
                        transactionDetails.put("groupId", new ArrayList<String>(Arrays.asList(inputParametersGroup.getGroupId())));
                    }else {
                        ((ArrayList<String>) transactionDetails.get("groupId")).add(inputParametersGroup.getGroupId());
                    }
                    LinkedHashMap<String , DynamicParametersDTOValidator> inputParameters = inputParametersGroup.getInputParameters();
                    inputParameters.forEach((inputParameterCode, inputParameter) -> {
                        if (inputParamsCodes.contains(inputParameterCode)){
                            transactionDetails.put(inputParameterCode, inputParameter);
                        }
                    });
                });
            }
            if (keys != null && keys.length > 0) {
                Long apiCode = request.getMessage().getHeader().getAttributeAsLong("apiCode");
                VericashAPI vericashAPI = vericashApiCache.getVericashAPI(apiCode);
                RequestTransformer requestTransformer =
                        transformerFactory.getTransformerFactory(vericashAPI.getRequestType());
                Message message = requestTransformer.transform(request.getMessage());
                for (String key : keys) {
                    if (message.getPayload().get(key) != null) {
                        transactionDetails.put(key, message.getPayload().get(key));
                    }
                }
            }
        } catch (Exception e) {
            System.err.println(e.getMessage());
        }

        return transactionDetails;
    }
    public String getFromDeviceInfo(Request request, String key) {
        if (request == null ||
                request.getMessage() == null ||
                request.getMessage().getPayload() == null) {
            return null;
        }
        Map<Object, Object> deviceInfo = getDeviceInfo(request);
        String value = null;
        try {
            value = (String) deviceInfo.get(key);
        }catch (Exception ex){
            System.err.println(ex.getMessage());
        }
        return value;
    }
    public Map<Object, Object> getDeviceInfo(Request request){
        if (request == null ||
                request.getMessage() == null ||
                request.getMessage().getPayload() == null) {
            return null;
        }
        Map<Object, Object> deviceInfo = null;
        try{

            deviceInfo = (Map<Object, Object>) request.getMessage().getPayload().getAttribute("deviceInfo");
            if (deviceInfo == null) {
                deviceInfo = (Map<Object, Object>) request.getMessage().getAdditionalData().getAttribute("deviceInfo");
            }
        }catch (Exception ex){
            System.err.println(ex.getMessage());
        }
        if (deviceInfo == null){
            throw new ApiValidationException(List.of(propertyLoaderComponent.getPropertyAsString("API_11142", "application", "error")));
        }
        return deviceInfo;
    }

    public JsonNode getJsonNode(Object message, String[] pathToJson) {
        JsonNode currentNode = mapper.valueToTree(message);
        for (String pathElement : pathToJson) {
            if (pathElement.endsWith("]")) {
                int index = Integer.parseInt(pathElement.substring(pathElement.indexOf("[") + 1, pathElement.indexOf("]")));
                currentNode = currentNode.path(pathElement.substring(0, pathElement.indexOf("[")));
                currentNode = currentNode.get(index);
            } else {
                currentNode = currentNode.path(pathElement);
            }
        }
        return currentNode;
    }

    public String getChildServiceCode(Message message) throws Exception {
        return serviceCodeMappingComponent.excute(getParentServiceCode(message), message);
    }
    public Message getTransformedMessage(Request request) throws Exception {
        Long apiCode = request.getMessage().getHeader().getAttributeAsLong("apiCode");
        log.info("extracting dynamic payload for apiCode : {}", apiCode);
        VericashAPI vericashAPI = vericashApiCache.getVericashAPI(apiCode);
        executeValidation(request, vericashAPI.getValidationType());
        Message transformMessage = transformMessage(request, vericashAPI.getRequestType());
        return populateSenderInfoUtil.getUserInformation(transformMessage);

    }

    public Message transformMessage(Request request, RequestType requestType) throws Exception {
        RequestTransformer requestTransformer = transformerFactory.getTransformerFactory(requestType);
        return requestTransformer.transform(request.getMessage());
    }

    public void executeValidation(Request request, Enums.ValidationType validationType) throws Exception {
        if (validationType.equals(Enums.ValidationType.COMPONENT) || validationType.equals(Enums.ValidationType.MIXED))
            dynamicServiceValidation.validateDynamicService(request);
        if (validationType.equals(Enums.ValidationType.SCHEMA) || validationType.equals(Enums.ValidationType.MIXED)) {
            jsonValidationStrategy.validateJsonStrategy(request);
        }
    }

    public String getParentServiceCode(Message message) throws Exception {
        Long apiCode = message.getHeader().getAttributeAsLong("apiCode");
        VericashAPI vericashApi = this.cachableVericashApi.getVericashAPI(apiCode);
        return vericashApi.getServiceCode();
    }
}
