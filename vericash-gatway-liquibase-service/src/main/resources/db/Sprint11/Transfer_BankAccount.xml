<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">



    <changeSet failOnError="false" author="Abubakr" id="AddTransferBankAccountChildrenToSERVICE_CONFIG_MAP">
        <sql>
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account CIB To CIB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account CIB To Al Ahly', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account CIB To QNB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Debit Card To CIB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account CIB To NBK', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account CIB To HSBC', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account CIB To Wallet', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account CIB To GTP', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account CIB To Virtual Card', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);


            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Debit To CIB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Debit To Al Ahly', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Debit To NBK', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Debit To HSBC', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Debit To Wallet', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Debit To GTP', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Debit To Virtual Card', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Debit To QNB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);


            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Wallet To CIB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account GTP To CIB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Virtual Card To CIB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);

            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Wallet To ALAhly', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account GTP To ALAhly', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Virtual Card To ALAhly', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);

            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Wallet To QNB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account GTP To QNB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Virtual Card To QNB', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);

            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Wallet To HSBC', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account GTP To HSBC', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Virtual Card To HSBC', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);

            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Wallet To NBK', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account GTP To NBK', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);
            INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, CATEGORY_ID, ORGANIZATION_ID, SERVICE_TYPE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, SERVICE_CATEGORY_ID, HOME_SCREEN_CONFIG, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED) VALUES(*********, 0, 'Transfer Bank Account Virtual Card To NBK', 0, NULL, NULL, NULL, '*********', 1, 0, 0, 0, 0, NULL, NULL, NULL, *********, 0);

        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="AddTransferBankAccountChildrenToBUSINESS_SERVICE_CONFIG">
        <sql>
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***************, 0, 0, 1, 0, 0, 'Transfer Bank Account CIB To CIB', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***************, 0, 0, 1, 0, 0, 'Transfer Bank Account CIB To Al Ahly', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***************, 0, 0, 1, 0, 0, 'Transfer Bank Account CIB To QNB', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES(***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Debit Card To CIB', *********, 3, 2421, NULL, 1, NULL, NULL);

            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account CIB To HSBC', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account CIB To NBK', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account CIB To Wallet', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account CIB To GTP', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account CIB To Virtual Card', *********, 3, 2421, NULL, 1, NULL, NULL);





            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Debit Card To CIB', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Debit Card To QNB', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Debit Card To AL Ahly', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Debit Card To HSBC', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Debit Card To NBK', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Debit Card To Wallet', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Debit Card To GTP', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Debit Card To Virtual Card', *********, 3, 2421, NULL, 1, NULL, NULL);


            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Wallet To CIB', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account GTP To CIB', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Virtual Card To CIB', *********, 3, 2421, NULL, 1, NULL, NULL);

            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Wallet To QNB', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account GTP To QNB', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Virtual Card To QNB', *********, 3, 2421, NULL, 1, NULL, NULL);

            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Wallet To AL Ahly', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account GTP To AL Ahly', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Virtual Card To AL Ahly', *********, 3, 2421, NULL, 1, NULL, NULL);

            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Wallet To HSBC', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account GTP To HSBC', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Virtual Card To HSBC', *********, 3, 2421, NULL, 1, NULL, NULL);


            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Wallet To NBK', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account GTP To NBK', *********, 3, 2421, NULL, 1, NULL, NULL);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID, BANKING_AGENT, BUSINESSSERVICECATEGORY, ISDEFAULTSERVICE, IS_EXPOSABLE, IS_MULTI_WALLET, NAME, BUSINESS_SERVICE_TYPE, SEGMENTATIONTYPE_ID, ORGANIZATION_ID, VERSION, IS_ENABLED, CATEGORY, NARRATION) VALUES (***************, 0, 0, 1, 0, 0, 'Transfer Bank Account Virtual Card To NBK', *********, 3, 2421, NULL, 1, NULL, NULL);

        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="AddScreensForTransferToBankAccount">
        <sql>
            INSERT INTO SERVICES_PAYMENTMETHOD (ID, API_CODE, PAYMENMETHODTYPE, LOOKUP_ENUM, GAR_STATUS, USER_TYPE, VAILDATE_PM) VALUES(171, 1*********1, 871, 1, '1', 1, NULL);
            INSERT INTO SERVICES_PAYMENTMETHOD (ID, API_CODE, PAYMENMETHODTYPE, LOOKUP_ENUM, GAR_STATUS, USER_TYPE, VAILDATE_PM) VALUES(172, 1*********1, 870, 1, '1', 1, NULL);
            INSERT INTO SERVICES_PAYMENTMETHOD (ID, API_CODE, PAYMENMETHODTYPE, LOOKUP_ENUM, GAR_STATUS, USER_TYPE, VAILDATE_PM) VALUES(173, 1*********1, 857, 3, '1', 1, NULL);
            INSERT INTO SERVICES_PAYMENTMETHOD (ID, API_CODE, PAYMENMETHODTYPE, LOOKUP_ENUM, GAR_STATUS, USER_TYPE, VAILDATE_PM) VALUES(174, 1*********1, 858, 3, '1', 1, NULL);
            INSERT INTO SERVICES_PAYMENTMETHOD (ID, API_CODE, PAYMENMETHODTYPE, LOOKUP_ENUM, GAR_STATUS, USER_TYPE, VAILDATE_PM) VALUES(175, 1*********1, 859, 3, '1', 1, NULL);
        </sql>
    </changeSet>
    <changeSet failOnError="false" author="Abubakr" id="AddScreensForTransferToBankAccount">
        <sql>
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(*********, ***************);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(*********, ***************);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(*********, ***************);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(*********, ***************);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(*********, ***************);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(*********, ***************);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(*********, ***************);
            INSERT INTO CUST_GROUP_SERVICES (CUSTOMER_TYPE_ID, BUSINESS_SERVICE_ID) VALUES(*********, ***************);
        </sql>
    </changeSet>
</databaseChangeLog>