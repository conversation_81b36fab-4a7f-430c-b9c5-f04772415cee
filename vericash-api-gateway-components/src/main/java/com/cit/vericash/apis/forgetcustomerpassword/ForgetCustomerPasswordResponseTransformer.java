package com.cit.vericash.apis.forgetcustomerpassword;

import com.cit.mpaymentapp.common.message.AuthContainer;
import com.cit.mpaymentapp.common.message.AuthenticationModel;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.forgetcustomerpassword.ForgetCustomerPasswordResponseTransformer")
public class ForgetCustomerPasswordResponseTransformer implements MuleResponseTransformer {

    public Object transform(BusinessMessage businessMessage) throws Exception {
        HashMap<Object, List<Map<String, Object[]>>> result = new HashMap<Object, List<Map<String, Object[]>>>();
        AuthContainer authContainer = businessMessage.getAuthContainer();
        boolean skipDynamicAuth = businessMessage.getParameters().getAttributeAsBoolean("skipValidateMissingDynamicAuthentication");
        if (skipDynamicAuth)
            return result;
        AuthenticationModel[] authenticationModelList = authContainer.getAuthenticationModels();
        List<Map<String, Object[]>> authContainers = new LinkedList<Map<String, Object[]>>();
        Map<String, Object[]> authmodel = new HashMap<String, Object[]>();
        authmodel.put("authenticationModels", authenticationModelList);
        authContainers.add(authmodel);
        result.put("authContainers", authContainers);
        return result;
    }
}
