package com.cit.vericash.util;

import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.stereotype.Service;

@Service
public class ValidationRequest {

    public Request validateRequest(Request request) {
        if ( request.getMessage().getPayload() == null || request.getMessage().getPayload().isEmpty())
            throw new APIException("Invalid Payload: ",
                    ErrorCode.PAYLOAD_CAN_NOT_BE_NULL_OR_EMPTY.getErrorCode());
        return request;
    }
}
