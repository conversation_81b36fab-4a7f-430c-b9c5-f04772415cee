<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet failOnError="false" author="Abubakr" id="add LOOKUP_FRAMEWORK_CONFIG RecordOfEnableOrDisableMoney">
        <sql>
			INSERT INTO LOOKUP_FRAMEWORK_CONFIG (ID, CACHING_KEY_PATTERN, CACHING_TYPE, IS_CACHED_LOOKUP, LOOKUP_NAME, LOOKUP_TYPE, RESPONSE_TRANSFORMER_MAPPING, CUSTOME_RESPONSE_TRANSFORMER, CUSTOM_TRANSFORMER_CONFIG_FILE, ENDPOINT_NAME, SERVICE_NAME) VALUES((select max(ID) +1 from LOOKUP_FRAMEWORK_CONFIG), '0', 0, 0, 'moneyRequest.enableOrdisable.receiving.request', 0, NULL, 'getEnableOrDisable', NULL, NULL, NULL);
		</sql>
    </changeSet>
	
	<changeSet failOnError="false" author="Abubakr" id="add PORTAL_LOOKUPS_CONFIG RecordOfEnableOrDisableMoney">
		<sql>
			INSERT INTO PORTAL_LOOKUPS_CONFIG (ID, LOOKUP_NAME, LOOKUP_KEY, LOOKUP_QUERY, CACHEABLE) VALUES((select max(ID) +1 from PORTAL_LOOKUPS_CONFIG), 'requestMoney enable or disable receiving requests', 'moneyRequest.enableOrdisable.receiving.request', 'select ID,USER_ID,TYPE from MONEY_REQUEST_USER_DEACTIVATE where USER_ID in (select c.CUSTOMER_ID  from CUSTOMER c where USER_ID=''${filters.getAttributeAsString(''userId'')}'')', 0);
		</sql>
	</changeSet>


</databaseChangeLog>