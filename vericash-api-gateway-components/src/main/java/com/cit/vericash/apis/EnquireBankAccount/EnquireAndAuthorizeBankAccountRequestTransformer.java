package com.cit.vericash.apis.EnquireBankAccount;


import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.dynamicrequest.transformer.PaymentMethodDynamicGroupTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.EnquireBankAccount.EnquireAndAuthorizeBankAccountRequestTransformer")
public class EnquireAndAuthorizeBankAccountRequestTransformer extends MessageToBusinessMessageTransformerImpl {


    @Autowired
    PaymentMethodDynamicGroupTransformer paymentMethodDynamicGroupTransformer;

    @Override
    protected void mapCustomValues(Message message) throws Exception {
        Map<String, Object> sourcePaymentMethod = new HashMap<>();
        List<Map<String, Object>> originalDynamicGroup = (List<Map<String, Object>>) message.getPayload().get("tmpDynamicGroup");
        sourcePaymentMethod.put("paymentMethodType", new Long("852"));
        message.getPayload().put("sourcePaymentMethod", sourcePaymentMethod);


        paymentMethodDynamicGroupTransformer.transform(message, originalDynamicGroup);
        message.getAdditionalData().put("isSelfReg", new Boolean(true));


    }

}
