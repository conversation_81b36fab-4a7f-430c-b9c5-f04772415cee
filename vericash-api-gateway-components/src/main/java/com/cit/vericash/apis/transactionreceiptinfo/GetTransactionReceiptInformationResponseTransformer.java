package com.cit.vericash.apis.transactionreceiptinfo;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.TransactionReceiptInformation;
import com.cit.vericash.api.components.transactionhistorymodel.TransactionReceiptInformationDto;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class GetTransactionReceiptInformationResponseTransformer implements MuleResponseTransformer {
    private final String TRANSACTION_STATUS = "transaction-status";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        ObjectMapper mapper = new ObjectMapper();

        TransactionReceiptInformation transactionReceiptInformation = businessMessage.getTransactionReceiptInformation();
        TransactionReceiptInformationDto transactionReceiptInfoResult = new TransactionReceiptInformationDto();
        transactionReceiptInfoResult.setTransactionDate(transactionReceiptInformation.getTransactionDate() != null ? transactionReceiptInformation.getTransactionDate() : null);
        transactionReceiptInfoResult.setTransactionReference(transactionReceiptInformation.getTransactionReference() != null ? transactionReceiptInformation.getTransactionReference() : null);
        transactionReceiptInfoResult.setSenderName(transactionReceiptInformation.getSenderName() != null ? transactionReceiptInformation.getSenderName() : null);
        transactionReceiptInfoResult.setSenderAccountNumber(transactionReceiptInformation.getSenderAccountNumber() != null ? transactionReceiptInformation.getSenderAccountNumber() : null);
        transactionReceiptInfoResult.setTransactionAmount(transactionReceiptInformation.getTransactionAmount() != null ? transactionReceiptInformation.getTransactionAmount() : null);
        transactionReceiptInfoResult.setTransactionAmountWithoutFee(transactionReceiptInformation.getTransactionAmountWithoutFee() != null ? transactionReceiptInformation.getTransactionAmountWithoutFee() : null);
        transactionReceiptInfoResult.setTransactionStatus(transactionReceiptInformation.getTransactionStatus() != null ? transactionReceiptInformation.getTransactionStatus() : null);
        transactionReceiptInfoResult.setTransactionType(transactionReceiptInformation.getTransactionType() != null ? transactionReceiptInformation.getTransactionType() : null);
        transactionReceiptInfoResult.setReceiverName(transactionReceiptInformation.getReceiverName() != null ? transactionReceiptInformation.getReceiverName() : null);
        transactionReceiptInfoResult.setReceiverAccountNumber(transactionReceiptInformation.getReceiverAccountNumber() != null ? transactionReceiptInformation.getReceiverAccountNumber() : null);
        transactionReceiptInfoResult.setAdveImageUrl(transactionReceiptInformation.getAdveImageUrl() != null ? transactionReceiptInformation.getAdveImageUrl() : null);
        transactionReceiptInfoResult.setReceiverInfo(transactionReceiptInformation.getReceiverInfo() != null ? transactionReceiptInformation.getReceiverInfo() : null);
        transactionReceiptInfoResult.setNarration(transactionReceiptInformation.getNarration() != null ? transactionReceiptInformation.getNarration() : null);
        transactionReceiptInfoResult.setDisputeTransaction(transactionReceiptInformation.isDisputeTransaction());
        transactionReceiptInfoResult.setActivityType(transactionReceiptInformation.getActivityType());

        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        result.put("transactionReceiptInformation", transactionReceiptInfoResult);
//		result.put(transactionStatusKey,businessMessage.getSoftFields().get("trxStatus").toString());
        return result;
    }
}
