package com.cit.vericash.LookupFramework.entity;

import com.cit.vericash.LookupFramework.common.util.enums;
import lombok.Data;
//import org.springframework.data.annotation.Id;
import javax.persistence.*;
import java.io.Serializable;

@Entity (name = "Lookup_Framework_Config")
@Table
@Data
public class LookupFrameworkConfig implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name= "LOOKUP_NAME")
    private String lookupName;

    @Column(name= "LOOKUP_TYPE")
    @Enumerated(EnumType.ORDINAL)
    private enums.lookupType lookupType ;

    @Column(name= "IS_CACHED_LOOKUP")
    private boolean isCached ;

    @Column(name= "CACHING_KEY_PATTERN")
    private String cachingKeyPattern ;

    @Enumerated(EnumType.ORDINAL)
    @Column(name= "CACHING_TYPE")
    private enums.cachingType cachingType;

    @Column(name= "RESPONSE_TRANSFORMER_MAPPING")
    private String responseTransformerMapping;

    @Column(name= "CUSTOME_RESPONSE_TRANSFORMER")
    private String customResponseTransformer ;

    @Column(name= "CUSTOM_TRANSFORMER_CONFIG_FILE")
    private String customTransformerConfigFile ;

    @Column(name= "SERVICE_NAME")
    private String serviceName;

    @Column(name= "ENDPOINT_NAME")
    private String endpointName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLookupName() {
        return lookupName;
    }

    public void setLookupName(String lookupName) {
        this.lookupName = lookupName;
    }

    public enums.lookupType getLookupType() {
        return lookupType;
    }

    public void setLookupType(enums.lookupType lookupType) {
        this.lookupType = lookupType;
    }

    public boolean isCached() {
        return isCached;
    }

    public void setCached(boolean cached) {
        isCached = cached;
    }

    public String getCachingKeyPattern() {
        return cachingKeyPattern;
    }

    public void setCachingKeyPattern(String cachingKeyPattern) {
        this.cachingKeyPattern = cachingKeyPattern;
    }

    public enums.cachingType getCachingType() {
        return cachingType;
    }

    public void setCachingType(enums.cachingType cachingType) {
        this.cachingType = cachingType;
    }

    public String getResponseTransformerMapping() {
        return responseTransformerMapping;
    }

    public void setResponseTransformerMapping(String responseTransformerMapping) {
        this.responseTransformerMapping = responseTransformerMapping;
    }

    public String getCustomResponseTransformer() {
        return customResponseTransformer;
    }

    public void setCustomResponseTransformer(String customResponseTransformer) {
        this.customResponseTransformer = customResponseTransformer;
    }

    public String getCustomTransformerConfigFile() {
        return customTransformerConfigFile;
    }

    public void setCustomTransformerConfigFile(String customTransformerConfigFile) {
        this.customTransformerConfigFile = customTransformerConfigFile;
    }

    @Override
    public String toString() {
        return "LookupFrameworkConfig{" +
                "id=" + id +
                ", lookupName='" + lookupName + '\'' +
                ", lookupType=" + lookupType +
                ", isCached=" + isCached +
                ", cachingKeyPattern='" + cachingKeyPattern + '\'' +
                ", cachingType=" + cachingType +
                ", responseTransformerMapping=" + responseTransformerMapping +
                ", customTransformerConfigFile=" + customTransformerConfigFile +
                '}';
    }
}





