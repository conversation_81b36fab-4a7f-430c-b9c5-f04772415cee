<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
		xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
		xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
   http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">


   <changeSet failOnError="true" author="Mohamed Orabi" id="add new column into APP_MENUS_WALLET">

	  <sql>
          ALTER TABLE APP_MENUS_WALLET
              ADD APP_TYPE NUMBER(1,0) DEFAULT NULL;
    </sql>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="update constrains into APP_MENUS_WALLET remove">

        <sql>
            ALTER TABLE APP_MENUS_WALLET DROP PRIMARY KEY;
        </sql>
    </changeSet>
    <changeSet failOnError="true" author="Mohamed Orabi" id="Update the values in new column into APP_MENUS_WALLET">

        <sql>
            UPDATE APP_MENUS_WALLET amw SET APP_TYPE = CASE
                WHEN  amw.APP_MENUS_ID  IN (SELECT ID FROM APP_MENUS am) THEN 2
                WHEN amw.APP_MENUS_ID IN (SELECT ID FROM SME_APP_MENUS sam  ) THEN 1
                WHEN amw.APP_MENUS_ID IN (SELECT ID FROM SME_AGENCY_APP_MENUS saam ) THEN 1
                ELSE 3
                END
        </sql>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="update constrains into APP_MENUS_WALLET Add">

        <sql>
            ALTER TABLE APP_MENUS_WALLET
                ADD CONSTRAINT ck_APP_MENUS_WALLET PRIMARY KEY (APP_MENUS_ID, APP_TYPE);
</sql>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="Add new column into APP_MENUS">

        <sql>
            ALTER TABLE APP_MENUS
                ADD SERVICE_TYPE_ID NUMBER(10,0) DEFAULT NULL;
           </sql>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="Add missed menu into APP_MENUS_WALLET">

        <sql>
            MERGE INTO APP_MENUS_WALLET
                USING APP_MENUS
                ON (APP_MENUS_WALLET.APP_MENUS_ID  = APP_MENUS.ID)
                WHEN NOT MATCHED THEN
                    INSERT (APP_MENUS_WALLET.APP_MENUS_ID, APP_MENUS_WALLET.WALLET_SHORT_CODE, APP_MENUS_WALLET.APP_TYPE)
                        VALUES (APP_MENUS.ID, '2020', 2);
        </sql>
    </changeSet>
	

</databaseChangeLog>
	
