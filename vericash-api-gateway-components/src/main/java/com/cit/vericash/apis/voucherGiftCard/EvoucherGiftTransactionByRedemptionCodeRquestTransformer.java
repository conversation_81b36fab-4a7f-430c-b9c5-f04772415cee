package com.cit.vericash.apis.voucherGiftCard;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.stereotype.Component;

@Component("EvoucherGiftTransactionByRedemptionCodeRquestTransformer")
public class EvoucherGiftTransactionByRedemptionCodeRquestTransformer extends MessageToBusinessMessageTransformerImpl {
    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

    }
}


