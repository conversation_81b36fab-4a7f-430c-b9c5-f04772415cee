package com.cit.vericash.apis.customerDevices;

import com.cit.mpaymentapp.model.Enums;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Component
@Qualifier("com.cit.vericash.apis.customerDevices.UpdateCustomerDeviceName")
public class UpdateCustomerDeviceName implements VericashAction {
    private static final int DISABLED = 0;
    private static final int ACTIVE = 1;
    private static final int BLOCKED = 2;
    private static final int DELETED = 3;

    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;
    public static Map<String, String> errorMsgVar;
    static {

    }
    @Override
    public Object process(Request request) throws Exception {
        String deviceId = request.getMessage().getPayload().getAttributeAsString("deviceId");
        String deviceName = request.getMessage().getPayload().getAttributeAsString("deviceName");
        Long customerId = request.getMessage().getPayload().getAttributeAsLong("customerId");
        HashMap<String, String> vars = new HashMap<String, String>();
        /*
        fix issue ATL-307 to validate empty deviceId
         */
        //        if (deviceId != null && customerId!=null) {
        if (!StringUtils.isBlank(deviceId) && customerId != null) {
            if (deviceName == null || "".equals(deviceName) || !deviceName.isEmpty()) {
                if (Pattern.compile("[^a-zA-Z\\d\\s:]").matcher(deviceName).find()) {
                    Map<String, String> errorMsg = new HashMap<>();
                    errorMsg.put("deviceName", propertyLoaderComponent.getPropertyAsString("invalid.device.name"));
                    throw new GeneralFailureException(ErrorCode.INVALID_DEVICE_NAME.getErrorCode(), errorMsg);
                }
                if (customerId != null) {
                    StringBuffer devicesNameQuerySb = new StringBuffer(ServiceQueryEngine.getQueryStringToExecute("UpdateCustomerDeviceName_1",this.getClass()));
                    if (isDeviceNameExists(deviceName, customerId)) {
                        throw new GeneralFailureException(ErrorCode.EXISTING_DEVICE_NAME.getErrorCode(),
                                Map.of("deviceName", propertyLoaderComponent.getPropertyAsString("device.name.exists")));
                    }
//                    StringBuffer devicesNameQuerySb = new StringBuffer("SELECT  * ")
//                            .append(" FROM  CUSTOMER_DEVICES x")
//                            .append(" WHERE x.DEVICE_VDI= ? and x.CUSTOMER_ID= ?");

                    StringBuffer getAllDevices = new StringBuffer(ServiceQueryEngine.getQueryStringToExecute("getAllDevices",this.getClass()));
//                    StringBuffer getAllDevices = new StringBuffer("SELECT  * ")
//                            .append(" FROM  CUSTOMER_DEVICES x")
//                            .append(" WHERE  x.CUSTOMER_ID= ? AND STATUS !=3");


                    List<Parameter> parameters = new ArrayList<Parameter>();
                    parameters.add(new Parameter(1, deviceId));
                    parameters.add(new Parameter(2, customerId));

//                    List<Parameter> parametersAllDevices = new ArrayList<Parameter>();
//                    parametersAllDevices.add(new Parameter(1, customerId));
                    List<String> fields = new ArrayList<String>();
                    fields.add("NAME");
                    fields.add("STATUS");
                    fields.add("CUSTOMER_ID");
                    fields.add("DEVICE_VDI");
                    fields.add("CUSTOMER_DEVICE_ID");

                    List<Record> deviceRecords = connectionUtil.executeSelect(devicesNameQuerySb.toString(), parameters, fields);
//                    List<Record> allDevices = connectionUtil.executeSelect(getAllDevices.toString(), parametersAllDevices, fields);
                    /*
                        fix issue ATL-305 add validation to device id
                    */
                    if (deviceRecords.isEmpty()) {
                        errorMsgVar = new HashMap<>();
                        errorMsgVar.put("invalid.device.id", propertyLoaderComponent.getPropertyAsString("invalid.device.id"));
                        throw new GeneralFailureException(ErrorCode.INVALID_DEVICE_ID.getErrorCode(), errorMsgVar);
                    }
                    Record device = deviceRecords.get(0);

                    String name = device.getValueAsString("NAME");
                    int statusOrdinal = deviceRecords.get(0).getValueAsBigDecimal("STATUS").intValue();

//                        if (deviceRecords.size()!=0) {
//                            if(allDevices.size()!=0){
//                                for (Record userDevice : allDevices) {
//                                        String oldDeviceName = userDevice.getValueAsString("NAME");
//                                    if (oldDeviceName.equals(deviceName)) {
//                                        throw new GeneralFailureException(propertyLoaderComponent.getPropertyAsString("device.name.exists"));
//                                    }
//                                }
//                            }
//
//                        }

                    if (statusOrdinal == Enums.DeviceStatus.DELETED.ordinal()) {
                        throw new GeneralFailureException(propertyLoaderComponent.getPropertyAsString("rename.deleted.status.error"));
                    } else if (statusOrdinal == Enums.DeviceStatus.BLOCKED.ordinal()) {
                        throw new GeneralFailureException(propertyLoaderComponent.getPropertyAsString("rename.blocked.status.error"));
                    } else if (statusOrdinal == Enums.DeviceStatus.DISABLED.ordinal()) {
                        Map<String, String> errorMsg = new HashMap<>();
                        errorMsg.put("deviceName", propertyLoaderComponent.getPropertyAsString("rename.disabled.status.error"));
                        throw new GeneralFailureException(ErrorCode.RENAME_DISABLED_DEVICE.getErrorCode(), errorMsg);
                    }

                }
                StringBuffer devicesQuerySb = new StringBuffer(ServiceQueryEngine.getQueryStringToExecute("UpdateCustomerDeviceName_2",this.getClass()));

                List<Parameter> parameters = new ArrayList<Parameter>();
                parameters.add(new Parameter(1, deviceName));
                parameters.add(new Parameter(2, deviceId));


                ConnectionUtil.executeTransactionUpdate(connectionUtil.getConnection(), devicesQuerySb.toString(), parameters);
                return null;
            }
        }
        /**
         * FIX ISSUE ATL-303 NO EXIST ERROR CODE
         */
        // throw new GeneralFailureException(propertyLoaderComponent.getPropertyAsString("rename.null.error"));
        //  Map<String, String>  errorMsgVar = new HashMap<>();
        errorMsgVar = new HashMap<>();
        errorMsgVar.put("rename.null.error", propertyLoaderComponent.getPropertyAsString("rename.null.error"));
        throw new GeneralFailureException(ErrorCode.RENAME_NULL_ERROR.getErrorCode(), errorMsgVar);

    }

    private boolean isDeviceNameExists(String deviceName, Long customerId) {
        String fieldName = "COUNT";
        String query = "SELECT COUNT(cd.CUSTOMER_DEVICE_ID) AS " +
                fieldName +
                " " +
                "FROM CUSTOMER_DEVICES cd " +
                "WHERE cd.CUSTOMER_ID = ? " +
                "AND cd.NAME = ? " +
                "AND cd.STATUS != "+
                DELETED;
        List<Parameter> params = new ArrayList<>();
        params.add(new Parameter(1, customerId));
        params.add(new Parameter(2, deviceName));
        List<String> fields = new ArrayList<>(List.of(fieldName));
        List<Record> recordsExists = connectionUtil.executeSelect(query, params, fields);
        return recordsExists != null &&
                !recordsExists.isEmpty() &&
                recordsExists.get(0).getValueAsLong(fieldName) != null &&
                recordsExists.get(0).getValueAsLong(fieldName) > 0;
    }

    public String setDeviceStatusName(Long status) {
        String statusName = "";
        switch (status.intValue()) {
            case 1:
                statusName = "Active";
                break;
            case 2:
                statusName = "Blocked";
                break;
            case 3:
                statusName = "Deleted";
                break;
        }
        return statusName;
    }
}
