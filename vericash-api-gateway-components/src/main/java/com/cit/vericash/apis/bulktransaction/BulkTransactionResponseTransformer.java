package com.cit.vericash.apis.bulktransaction;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.bulktransaction.BulkTransactionResponseTransformer")
public class BulkTransactionResponseTransformer implements MuleResponseTransformer {
    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashedMap();
        return result;
    }
}
