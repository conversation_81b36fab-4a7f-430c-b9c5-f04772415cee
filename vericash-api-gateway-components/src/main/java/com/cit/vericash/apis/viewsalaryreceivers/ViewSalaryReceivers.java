package com.cit.vericash.apis.viewsalaryreceivers;

import com.cit.mpaymentapp.common.group.transfers.GroupTransferRecipientDTO;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.viewsalaryreceivers.ViewSalaryReceivers")
public class ViewSalaryReceivers implements VericashAction {
    ArrayList<GroupTransferRecipientDTO> validReceiversMap;
    ArrayList<GroupTransferRecipientDTO> notValidReceiversMap;

    HashMap<String, ArrayList<GroupTransferRecipientDTO>> result;

    @Autowired
    ConnectionUtil connectionUtil;

    public Object process(Request request) throws Exception {
        validReceiversMap = new ArrayList<GroupTransferRecipientDTO>();
        notValidReceiversMap = new ArrayList<GroupTransferRecipientDTO>();
        result = new HashMap<String, ArrayList<GroupTransferRecipientDTO>>();
        try {
            String query = ServiceQueryEngine.getQueryStringToExecute("ViewSalaryReceivers_process",this.getClass());
            List<Parameter> parameters = new ArrayList<Parameter>();
            List<String> fields = new ArrayList<String>();
            fields.add("TRANSFER_ID");
            fields.add("AMOUNT");
            fields.add("BENEFICIARY_ID");
            fields.add("BENEFICIARY_NAME");
            fields.add("BRANCH_CODE");
            fields.add("BRANCH_NAME");
            fields.add("CARD_NUMBER");
            fields.add("COUNTRY_NAME");
            fields.add("INSTITUTION_CODE");
            fields.add("INSTITUTION_NAME");
            fields.add("TRANSFER_TYPE");
            fields.add("GROUP_TRANSFER_SETUP_ID");
            fields.add("STATUS");
            fields.add("CLIENT_ID");
            fields.add("RECEIVER_TYPE");
            fields.add("WALLET_PROVIDER");
            fields.add("CURRENCY");
            fields.add("PAYMENT_ALIAS");
            fields.add("MOBILE_NUMBER");

            parameters.add(new Parameter(1, request.getMessage().getPayload().getAttributeAsLong("groupId")));
            List<Record> records = connectionUtil.executeSelect(query, parameters, fields);
            if (records.size() > 0) {
                for (Record rec : records) {
                    GroupTransferRecipientDTO groupTransferRecipientDTO = new GroupTransferRecipientDTO();
                    groupTransferRecipientDTO.setId(rec.getValueAsLong("TRANSFER_ID"));
                    groupTransferRecipientDTO.setBeneficiaryName(rec.getValueAsString("BENEFICIARY_NAME"));
                    groupTransferRecipientDTO.setBeneficiaryId(rec.getValueAsString("BENEFICIARY_ID"));
                    groupTransferRecipientDTO.setTransferType(rec.getValueAsString("TRANSFER_TYPE"));
                    groupTransferRecipientDTO.setCardNumber(rec.getValueAsString("CARD_NUMBER"));
                    groupTransferRecipientDTO.setInstitutionName(rec.getValueAsString("INSTITUTION_NAME"));
                    groupTransferRecipientDTO.setStatus(rec.getValueAsString("STATUS"));
                    groupTransferRecipientDTO.setClientId(rec.getValueAsString("CLIENT_ID"));
                    groupTransferRecipientDTO.setMobileNumber(rec.getValueAsString("MOBILE_NUMBER"));
                    groupTransferRecipientDTO.setPaymentAlias(rec.getValueAsString("PAYMENT_ALIAS"));
                    groupTransferRecipientDTO.setAmount(rec.getValueAsBigDecimal("AMOUNT").doubleValue());
                    groupTransferRecipientDTO.setBranchCode(rec.getValueAsString("BRANCH_CODE"));
                    groupTransferRecipientDTO.setBranchName(rec.getValueAsString("BRANCH_NAME"));
                    String status = rec.getValueAsString("STATUS");

                    if (status.equals("Valid")) {
                        validReceiversMap.add(groupTransferRecipientDTO);
                    } else {
                        notValidReceiversMap.add(groupTransferRecipientDTO);
                    }


                }
            }

            System.out.println("inside quick action method");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
        result.put("validReceivers", validReceiversMap);
        result.put("notValidReceivers", notValidReceiversMap);

        return result;
    }
}
