listOfMonthTransaction = SELECT SCHEDULER.ID,SCHEDULER.NAME,SCHEDULER_TYPE.NAME as TYPE,SCHEDULER.END_TIME,SCHEDULER.REPEAT_COUNT,SCHEDULER.START_TIME,\
SCHEDULER_RECURRING_PATTERN.NAME as FREQUENCY,SCHEDULER_STATUS.NAME as STATUS,SCHEDULER.LAST_OCCURRENCE_STATUS,SCHEDULER_OCCURRENCE.DETAILS,SCHEDULER_OCCURRENCE.DATE_TIME as DATIUM, EXTRACT (day from DATE_TIME) Day from SCHEDULER \
INNER JOIN  SCHEDULER_TYPE ON SCHEDULER.TYPE=SCHEDULER_TYPE.ID \
INNER JOIN  SCHEDULER_RECURRING_PATTERN ON SCHEDULER.RECURRING_PATTERN=SCHEDULER_RECURRING_PATTERN.ID \
INNER JOIN  SCHEDULER_STATUS ON SCHEDULER.STATUS=SCHEDULER_STATUS.ID \
INNER JOIN  SCHEDULER_OCCURRENCE ON SCHEDULER.ID=SCHEDULER_OCCURRENCE.SCHEDULER_ID \
WHERE CUSTOMER_ID =%1$s AND SCHEDULER_OCCURRENCE.DATE_TIME >= TO_DATE('%2$s-%3$s-01','YYYY-MM-DD') AND SCHEDULER_OCCURRENCE.DATE_TIME < TO_DATE('%4$s-%5$s-01','YYYY-MM-DD')  order by DATIUM DESC