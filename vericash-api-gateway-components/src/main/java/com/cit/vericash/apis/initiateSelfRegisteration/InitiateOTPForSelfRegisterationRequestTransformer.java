package com.cit.vericash.apis.initiateSelfRegisteration;

import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.CacheableWalletInfo;
import com.cit.vericash.apis.commons.util.MsisdnFormatterImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.initiateSelfRegisteration.InitiateOTPForSelfRegisterationRequestTransformer")
public class InitiateOTPForSelfRegisterationRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    private final String WALLET_SHORT_CODE = "wallet-Short-Code";
    private final String MSISDN = "msisdn";
    private final String USER_KEY = "user-Key";
    private final String IS_SELF_REG = "is-self-reg";
    @Autowired
    MsisdnFormatterImpl msisdnFormatter;
    @Autowired
    private CacheableWalletInfo walletInfoCache;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        String walletShortCodeKey = cacheableFieldParameters.getRequestParameters(WALLET_SHORT_CODE);
        String walletShortCode = message.getHeader().getAttributeAsString(walletShortCodeKey);
        WalletInfo walletInfo = walletInfoCache.getWalletInfo(walletShortCode);

        try {
            String msisdnKey = cacheableFieldParameters.getRequestParameters(MSISDN);
            String msisdn = message.getPayload().getAttributeAsString(msisdnKey);
            String region = walletInfo.getCountryIso2();
            msisdn = msisdnFormatter.formatMSISDN(msisdn, region, walletShortCode);
            //String userKey = message.getHeader().getAttributeAsString(walletShortCodeKey)+msisdn;
            //userKey
            String userKey_Key = cacheableFieldParameters.getRequestParameters(USER_KEY);
            //selfReg
            String selfReg_Key = cacheableFieldParameters.getRequestParameters(IS_SELF_REG);


            //message.getPayload().put(userKey_Key, userKey);
            message.getPayload().put(selfReg_Key, new Boolean(true));
            message.getPayload().put(msisdnKey, msisdn);

        } catch (Exception e) {
            throw e;
        }
    }


}
