package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.SalaryResponseTransformer")
public class SalaryResponseTransformer implements MuleResponseTransformer {

    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        if (businessMessage.getGroupTransfer() != null) {
            result.put("groupTransfer", businessMessage.getGroupTransfer());
        }
        return result;
    }
}
