<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- ChangeSet for inserting data into SERVICE_CONFIG_MAP -->
    <changeSet failOnError="false" author="Abubakr" id="remove mobile number for send money">
        <sql>
            DELETE FROM INPUT_PARAMS_GROUPS ipg2
            WHERE ipg2.ID  in  (
                SELECT ipg.id FROM INPUT_PARAM_GROUP_DEF ipgd JOIN
                                   INPUT_PARAM_GROUP_WALLET ipgw ON IPGD .ID =ipgw.GROUP_DEF_ID JOIN
                                   INPUT_PARAMS_GROUPS ipg ON IPG.GROUP_WALLET_ID =ipgw.ID JOIN
                                   INPUT_PARAMS ip ON ip.ID =ipg.INPUT_PARAM_ID JOIN
                                   INPUT_PARAM_DICTIONARY ipd ON ipd.ID =ip.DICTIONARY_ID
                WHERE ipgd.GROUP_CODE =27 AND ipd.CODE =104
            );

        </sql>
    </changeSet>




</databaseChangeLog>
