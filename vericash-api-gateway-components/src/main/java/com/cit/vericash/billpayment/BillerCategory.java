package com.cit.vericash.billpayment;

import com.cit.mpaymentapp.mongo.beans.BillerPerCategory;

import java.io.Serializable;
import java.util.Set;

public class BillerCategory implements Serializable {

    private static final long serialVersionUID = 1L;
    private String id;
    private String name;
    private String description;
    private String iconCategory;
    private String sorted;
    private Set<com.cit.mpaymentapp.mongo.beans.BillerPerCategory> billersList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }


    public Set<com.cit.mpaymentapp.mongo.beans.BillerPerCategory> getBillersList() {
        return billersList;
    }

    public void setBillersList(Set<BillerPerCategory> billersList) {
        this.billersList = billersList;
    }

    public String getIconCategory() {
        return iconCategory;
    }

    public void setIconCategory(String iconCategory) {
        this.iconCategory = iconCategory;
    }

    public String getSorted() {
        return sorted;
    }

    public void setSorted(String sorted) {
        this.sorted = sorted;
    }

    @Override
    public String toString() {
        return "BillerCategory [id=" + id + ", name=" + name + ", description="
                + description + ", iconCategory=" + iconCategory + "]";
    }

}
