package com.cit.vericash.apis.recurringTransaction;


import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;


@Component
@Qualifier("com.cit.vericash.apis.recurringTransaction.recurringTransactionRequestTransformer")
public class recurringTransactionRequestTransformer implements VericashAction {
    @Autowired
    RecurringTransactionImpl recurringTransactionImpl;

    @Override
    public Object process(Request request) throws Exception {
        System.out.println("test");
        return recurringTransactionImpl.listOfTransaction(request);
    }
}
