package com.cit.vericash.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;

@Configuration
@PropertySource("classpath:/vericashBackendConfig.properties")
@PropertySource("classpath:application.properties")
@Getter
public class VericashBackendConfig {

    private Environment env;

    private String LOOKUP_FRAMEWORK_URL;

    @Value("${enableBackendSecurity}")
    Boolean enableBackendSecurity;
    @Value("${Authentications_key}")
    String Authentications_key;

    @Value("${lookupFW_url}")
    String loofupFW_Url;

  /*  public String getLOOKUP_FRAMEWORK_URL() {
        LOOKUP_FRAMEWORK_URL = env.getProperty("LOOKUP_FRAMEWORK_URL");
        return  LOOKUP_FRAMEWORK_URL ;
    }*/

    public String getAuthentications_key() {
        return Authentications_key;
    }


    public void setAuthentications_key(String authentications_key) {
        Authentications_key = authentications_key;
    }

 public boolean isTransactionHistorySeparatedList() {
        return transactionHistorySeparatedList;
    }



    public String getTransactionHistoryListOrder() {
        return transactionHistoryListOrder;
    }

    @Value("${transactionHistorySeparatedList}")
    boolean transactionHistorySeparatedList;

    @Value("${transactionHistoryListOrder}")
    String transactionHistoryListOrder;

   @Value("${entrustUrl}")
    String entrustUrl;




    @Value("${debitCardValidationUrl}")
    String debitCardValidationUrl;

    @Value("${cardAuthenticationSuccesCode}")
    String cardAuthenticationSuccesCode;


    Boolean enableDynamicAuth=Boolean.valueOf(System.getenv("ENABLE_DYNAMIC_AUTHENTICATION"));

    public boolean getEnableDynamicAuth() {
        return enableDynamicAuth;
    }
}

