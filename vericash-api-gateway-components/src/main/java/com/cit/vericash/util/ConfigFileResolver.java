package com.cit.vericash.util;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Getter
public class ConfigFileResolver {
    private final Environment environment;
    private String vericashRootFolder;
    @PostConstruct
    public void init(){
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(Objects.requireNonNullElse(environment.getProperty("VERICASH_APIS_CONFIG"), ""));
        stringBuilder.append(File.separator);
        stringBuilder.append(Objects.requireNonNullElse(environment.getProperty("PROJECT"), ""));
        stringBuilder.append("-config");
        vericashRootFolder = stringBuilder.toString();
    }

}
