package com.cit.vericash.apis.unblockbecustomer;

import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.dto.response.Response;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.unblockbecustomer.UnBlockBECustomerRequestTransformer")
public class UnBlockBECustomerRequestTransformer implements VericashAction {
    @Override
    public Object process(Request request) throws Exception {
        Response response = null;

        try {

        } catch (Exception e) {

        }
        return null;
    }
}
