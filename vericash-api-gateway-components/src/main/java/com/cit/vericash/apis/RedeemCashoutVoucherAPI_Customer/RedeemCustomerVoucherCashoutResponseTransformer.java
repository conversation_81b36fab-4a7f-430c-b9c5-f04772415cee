package com.cit.vericash.apis.RedeemCashoutVoucherAPI_Customer;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.model.transaction.TransactionExecutionSummary;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.RedeemCashoutVoucherAPI_Customer.RedeemCustomerVoucherCashoutResponseTransformer")
public class RedeemCustomerVoucherCashoutResponseTransformer implements MuleResponseTransformer {
    private final String trxAmountKey = "Transaction-Amount";
    private final String totalFeeAmountKey = "Total-Fee-Amount";
    private final String trxIdKey = "Transaction_Execution_Summary_ID";
    private final String terminalIdKey = "terminal_id";
    private final String referenceNumberKey = "reference_number";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();


        Map<String, Object> result = new HashMap<String, Object>();

        try {

            if (businessMessage.getSoftFields().get("TRANS_EXEC_SUMMARY") != null) {
                TransactionExecutionSummary transactionExecutionSummary = (TransactionExecutionSummary) businessMessage
                        .getSoftFields().get("TRANS_EXEC_SUMMARY");
                result.put(cacheableFieldParameters.getResponseParameters(trxIdKey), transactionExecutionSummary.getTransactionExecutionSummaryID());
                result.put(cacheableFieldParameters.getResponseParameters(trxAmountKey), transactionExecutionSummary.getTransactionAmount());
                result.put(cacheableFieldParameters.getResponseParameters(totalFeeAmountKey), (transactionExecutionSummary.getTotalFeeAmount() == null) ? 0 : transactionExecutionSummary.getTotalFeeAmount());
            }
            result.put(cacheableFieldParameters.getResponseParameters(terminalIdKey), businessMessage.getVoucher().getTerminalId());
            result.put(cacheableFieldParameters.getResponseParameters(referenceNumberKey), businessMessage.getTransactionInfo().getVoucher().getVoucherTrxRefID());
        } catch (Exception e) {
            throw e;
        }

        return result;
    }

}
