package com.cit.vericash.LookupFramework.components;

import com.cit.vericash.LookupFramework.common.util.LookupDTO;
import com.cit.vericash.LookupFramework.common.util.enums;
import com.cit.vericash.LookupFramework.common.util.request.LookupPayload;
import com.cit.vericash.LookupFramework.entity.LookupFrameworkConfig;
import com.cit.vericash.LookupFramework.lookupdao.LookupDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class LookupsDetailsComponent {

    @Autowired
    LookupDAO lookupDAO;

    public List<LookupDTO> getLookupsDataFromDB(LookupPayload lookupPayload) {
        List<String> lookupNames = lookupPayload.getLookups();

        List<LookupFrameworkConfig> lookupFrameworkEntitysList = getLookupsDetails(lookupNames);

        if (lookupFrameworkEntitysList == null || lookupFrameworkEntitysList.isEmpty()) {
            return null;
        }

        List<LookupDTO> lookupDTOS = transform(lookupFrameworkEntitysList);

        return lookupDTOS;

    }

    private List<LookupFrameworkConfig> getLookupsDetails(List<String> lookups) {
        List<LookupFrameworkConfig>  lookupFrameworkConfigs= lookupDAO.getLookupConfigEntityByLookupNames(lookups);
        return lookupFrameworkConfigs;
    }

    private List<LookupDTO> transform(List<LookupFrameworkConfig> lookups) {
        LookupDTO lookupDTO=new LookupDTO ();

        // using the verbose statement of function (rahter than a lambda)
        List<LookupDTO> lookupDTOS = lookups.stream().map(new Function<LookupFrameworkConfig, LookupDTO>() {
            @Override
            public LookupDTO apply(LookupFrameworkConfig entity) {
                // a simple mapping from domain to dto
                return new LookupDTO(entity.getId(), entity.getLookupName(),entity.getLookupType(),entity.isCached(),entity.getCachingKeyPattern(),entity.getCachingType(),entity.getResponseTransformerMapping(),entity.getCustomResponseTransformer(), entity.getCustomTransformerConfigFile(),entity.getServiceName(),entity.getEndpointName());
            }
        }).collect(Collectors.toList());
        System.out.println(lookupDTOS);
        return lookupDTOS;
    }

    // should be call in transfromer when applay caching service
    private void setCashKey(List<LookupFrameworkConfig> lookups) {

    }
    
}