package com.cit.vericash.apis.moneyRequest;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.commons.dto.*;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.util.RequestParsingUtils;
import com.cit.vericash.util.WalletTypeUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.IntNode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.context.annotation.Description;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class SplitMoneyRequestTransformerDiffblueTest {
    @Mock
    private WalletTypeUtils walletTypeUtils;

    @Mock
    private PropertyLoaderComponent propertyLoaderComponent;

    @Mock
    private RequestParsingUtils requestParsingUtils;

    @Mock
    private ConnectionUtil connectionUtil;

    private final ObjectMapper mapper = new ObjectMapper();

    @InjectMocks
    private SplitMoneyRequestTransformer splitMoneyRequestTransformer;
    public static final String[] pathToJson = new String[]{"message", "payload", "dynamicPayload"};
    public static final String jsonPath = "message,payload,dynamicPayload";
    public static final String PATH_TO_JSON = "pathToJson";
    public static final String BE_SEARCH_VALUE = "splitBESearchValue";
    public static final String DELIMITER = ",";
    private static final String PARENT_SHORT_CODE = "parentShortCode";
    //    public static final String PATH_TO_MONEY_REQUEST_TYPE = "pathToMoneyRequestType";
    public static final String WALLET_SHORT_CODE = "walletShortCode";

    DynamicPayload dynamicPayload;
    DynamicPayload originalDynamicPayload;
    Payload payload;
    Payload originalPayload;
    Header header;
    Header originalHeader;
    Message message = new Message();
    Message originalMessage = new Message();
    Request originalRequest = new Request();
    JsonNode jsonNode;
//    String jsonString = "{\"signature\":\"123123123\",\"message\":{\"header\":{\"apiCode\":\"2395\",\"timestamp\":1647350817727,\"sessionId\":\"2583691487\",\"walletShortCode\":\"2020\",\"channelCode\":\"test_1\"},\"payload\":{\"dynamicGroup\":[{\"groupId\":107,\"groupName\":\"BIll Payment Purchase Bundles\",\"inputParameters\":{\"106\":{\"parameterLabel\":\"Amount\",\"parameterCode\":106,\"parameterValue\":5},\"202\":{\"parameterLabel\":\"sourcepaymentmethodDropDownList\",\"parameterCode\":202,\"parameterValue\":{\"paymentMethodType\":\"842\",\"paymentMethodCode\":102202}},\"218\":{\"parameterLabel\":\"mobileNumber\",\"parameterCode\":218,\"parameterValue\":\"+27839580444\"},\"312\":{\"parameterLabel\":\"opreator\",\"parameterCode\":312,\"parameterValue\":{\"Name\":\"Vodacom\",\"ID\":\"1\"}},\"313\":{\"parameterLabel\":\"bundle\",\"parameterCode\":313,\"parameterValue\":{\"ID\":\"1001\",\"Name\":\"MTN/Six Month One Time 100MB R59\"}}}],\"authParams\":[{\"authCode\":\"0\",\"authValue\":\"9632\",\"authOrder\":\"1\"}],\"senderOwnerType\":\"1\",\"senderIdentifyValue\":\"+27839580444\"},\"additionalData\":{},\"echoData\":{\"Sample\":\"Sample\"}}}}";
    String jsonString;

    @Before
    public void init() {
        dynamicPayload = new DynamicPayload();
        payload = new Payload();
        header = new Header();
        dynamicPayload.setPayload(payload);
        dynamicPayload.setHeader(header);

        message.setPayload(payload);
        message.setDynamicPayload(dynamicPayload);
        message.setHeader(header);

        jsonNode = mapper.valueToTree(originalRequest);

        originalDynamicPayload = new DynamicPayload();
        originalPayload = new Payload();
        originalHeader = new Header();


        originalHeader.put("apiCode", "2395");
        originalHeader.put("timestamp", 1647350817727L);
        originalHeader.put("sessionId", "2583691487");
        originalHeader.put("walletShortCode", "2020");
        originalHeader.put("channelCode", "test_1");

        originalPayload.put("customerId", 123L);
        DynamicGroupParametersDTOValidator dynamicGroup = getDynamicGroupParametersDTOValidator();

        List<DynamicGroupParametersDTOValidator> dynamicGroups = new ArrayList<>();
        dynamicGroups.add(dynamicGroup);


        originalPayload.put("senderOwnerType", "1");
        originalPayload.put("senderIdentifyValue", "+27839580444");
        originalPayload.put("dynamicGroup", dynamicGroups);


        originalDynamicPayload.setHeader(originalHeader);
        originalDynamicPayload.setPayload(originalPayload);

        originalPayload.put("serviceCode", "100208");
        originalPayload.put("parentServiceCode", "100111");
        originalPayload.put("SplitBEId", 79885353L);
        originalPayload.put("serviceId", 137197842878052L);
        originalPayload.put("parentServiceId", 137197842880L);

        originalMessage.setPayload(originalPayload);
        originalMessage.setDynamicPayload(originalDynamicPayload);
        originalMessage.setHeader(originalHeader);

        originalRequest.setMessage(originalMessage);
    }

    private static DynamicGroupParametersDTOValidator getDynamicGroupParametersDTOValidator() {
        DynamicGroupParametersDTOValidator dynamicGroup = new DynamicGroupParametersDTOValidator();
        dynamicGroup.setGroupId("107");
        dynamicGroup.setGroupName("BIll Payment Purchase Bundles");

        DynamicParametersDTOValidator inputParameter106 = new DynamicParametersDTOValidator();
        inputParameter106.setParameterLabel("Amount");
        inputParameter106.setParameterCode(106L);
        inputParameter106.setParameterValue(5);

        DynamicParametersDTOValidator inputParameter202 = new DynamicParametersDTOValidator();
        inputParameter202.setParameterLabel("sourcepaymentmethodDropDownList");
        inputParameter202.setParameterCode(202L);
        Map<String, Object> inputParameterValue202 = new HashMap<>();
        inputParameterValue202.put("paymentMethodType", "842");
        inputParameterValue202.put("paymentMethodCode", 102202);
        inputParameter202.setParameterValue(inputParameterValue202);

        DynamicParametersDTOValidator inputParameter218 = new DynamicParametersDTOValidator();
        inputParameter218.setParameterLabel("mobileNumber");
        inputParameter218.setParameterCode(218L);
        inputParameter218.setParameterValue("+27839580444");

        DynamicParametersDTOValidator inputParameter312 = new DynamicParametersDTOValidator();
        inputParameter312.setParameterLabel("opreator");
        inputParameter312.setParameterCode(312L);
        Map<String, Object> inputParameterValue312 = new HashMap<>();
        inputParameterValue202.put("name", "Vodacom");
        inputParameterValue202.put("id", "1");
        inputParameter312.setParameterValue(inputParameterValue312);

        DynamicParametersDTOValidator inputParameter313 = new DynamicParametersDTOValidator();
        inputParameter313.setParameterLabel("bundle");
        inputParameter313.setParameterCode(313L);
        Map<String, Object> inputParameterValue313 = new HashMap<>();
        inputParameterValue202.put("name", "MTN/Six Month One Time 100MB R59");
        inputParameterValue202.put("id", "1001");
        inputParameter313.setParameterValue(inputParameterValue313);

        LinkedHashMap<String, DynamicParametersDTOValidator> inputParameters = new LinkedHashMap<>();
        inputParameters.put("106", inputParameter106);
        inputParameters.put("202", inputParameter202);
        inputParameters.put("218", inputParameter218);
        inputParameters.put("312", inputParameter312);
        inputParameters.put("313", inputParameter313);

        dynamicGroup.setInputParameters(inputParameters);
        return dynamicGroup;
    }


    @Test
    @Description("when setting the original request json node as object it should extract it from json node")
    public void testGetOriginalRequestsObject() throws GeneralFailureException, JsonProcessingException {
        when(propertyLoaderComponent.getPropertyAsString(PATH_TO_JSON)).thenReturn(jsonPath);
        jsonNode = mapper.valueToTree(originalRequest);
        when(requestParsingUtils.getJsonNode(message, pathToJson)).thenReturn(jsonNode);
        Request originalRequest1 = splitMoneyRequestTransformer.getOriginalRequest(message);
        verify(requestParsingUtils, times(1)).getJsonNode(message, pathToJson);
        assertNotNull(originalRequest1);
    }

    @Test
    @Description("when setting the original request json node as as string it should parse it from json node")
    public void testGetOriginalRequestsString() throws JsonProcessingException, GeneralFailureException {
        when(propertyLoaderComponent.getPropertyAsString(PATH_TO_JSON)).thenReturn(jsonPath);
        jsonString = mapper.writeValueAsString(originalRequest);
        JsonNode jsonNode1 = mapper.readTree(jsonString);
        when(requestParsingUtils.getJsonNode(message, pathToJson)).thenReturn(jsonNode1);
        Request originalRequest1 = splitMoneyRequestTransformer.getOriginalRequest(message);

        verify(requestParsingUtils, times(1)).getJsonNode(message, pathToJson);
        assertNotNull(originalRequest1);
    }

    @Test
    @Description("other types should throw exception")
    public void testGetOriginalRequestsThrows() throws JsonProcessingException {
        when(propertyLoaderComponent.getPropertyAsString(PATH_TO_JSON)).thenReturn(jsonPath);
        JsonNode jsonNode1 =  new IntNode(1);
        when(requestParsingUtils.getJsonNode(message, pathToJson)).thenReturn(jsonNode1);
        assertThrows(GeneralFailureException.class, () -> splitMoneyRequestTransformer.getOriginalRequest(message));
    }

}
