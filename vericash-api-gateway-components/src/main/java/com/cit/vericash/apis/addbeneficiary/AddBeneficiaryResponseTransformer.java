package com.cit.vericash.apis.addbeneficiary;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
@Qualifier("com.cit.vericash.apis.addbeneficiary.AddBeneficiaryResponseTransformer")
public class AddBeneficiaryResponseTransformer implements MuleResponseTransformer {

    Map<String, Object> result = new HashMap<>();

    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
//        if(businessMessage.getBeneficiary() != null){
//            result.put("beneficiaryId", businessMessage.getBeneficiary().getBeneficiaryId());
//        }
//
//        return result.get("beneficiaryId");
//        Object response = new JSONObject();
//        JsonNode fareJsonNode = null;
//        ObjectMapper objectMapper=new ObjectMapper();
//        try {
//            if (businessMessage.getIntegrationResponse() != null && businessMessage.getIntegrationResponse()!="") {
//                response = businessMessage.getIntegrationResponse();
//
//                Object object=objectMapper.readValue(response.toString(),Object.class);
//                JsonNode jsonNode=objectMapper.valueToTree(object);
//                fareJsonNode = jsonNode.get("events").get(0).get("status");
//                if(fareJsonNode.asText().equals("OPENED"))
//                {
//                    throw new IntegrationException("Integration validation exception","validation failed");
//                }
//            }
//            return fareJsonNode;
//        } catch (Exception e) {
//            throw e;
//        }
        return result;
    }
}
