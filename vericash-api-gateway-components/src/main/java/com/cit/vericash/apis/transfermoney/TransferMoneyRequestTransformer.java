package com.cit.vericash.apis.transfermoney;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.stereotype.Component;

@Component
public class TransferMoneyRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    public static final String sourcePaymentMethod = "sourcePaymentMethod";
    public static final String destinationPaymentMethod = "destinationPaymentMethod";

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
//         try {
//
//        ObjectMapper mapperSource = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//         ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
//
//        Object sourcePaymentMethodObject=message.getPayload().getAttribute(sourcePaymentMethod);
//        Object destinationPaymentMethodObject=message.getPayload().getAttribute(destinationPaymentMethod);
//
//        SourcePaymentMethod sourcePaymentMethod = message.getPayload().getMapper().readValue(ow.writeValueAsString(sourcePaymentMethodObject), SourcePaymentMethod.class);
//        DestinationPaymentMethod destinationPaymentMethod = message.getPayload().getMapper().readValue(ow.writeValueAsString(destinationPaymentMethodObject), DestinationPaymentMethod.class);
//        message.getPayload().put("destinationPaymentMethod", destinationPaymentMethod);
//        message.getPayload().put("sourcePaymentMethod",sourcePaymentMethod);
//        targetObject.setValue("/destinationPaymentMethod", destinationPaymentMethod);
//
//         } catch (Exception e) {
//         throw e;
//         }
    }
}
