createQuery_Retail=SELECT PM. NAME \"paymentMethodName\", SPM. ID \"paymentMethodType\",  \
  SPMS.API_CODE, SPMS.USER_TYPE ,PM.PAYMETN_METHOD_ORDER \
  FROM CUSTOMER C JOIN PAYMENT_METHOD_CUST_PROF_CG PMCP ON C.CUSTOMER_TYPE_ID = PMCP.CUSTOMER_TYPE_ID \
  JOIN PAYMENT_METHOD PM ON PM. ID = PMCP.PAYMENT_METHOD_ID  \
  JOIN SOURCE_PAYMENT_METHOD SPM ON SPM. ID = PM. ID \
  left join SERVICES_PAYMENTMETHOD  SPMS on  SPM.ID =SPMS.PAYMENMETHODTYPE \
  WHERE  C.USER_ID = ? \
  AND   SPMS.LOOKUP_ENUM = ?  AND  SPMS.USER_TYPE = ?
ifCondition_1_Retail=AND   SPMS.API_CODE = ?
ifCondition_2_Retail=AND   SPMS.API_CODE in (
forLoop_1_Retail=? , 
forLoop_2_Retail=? )
