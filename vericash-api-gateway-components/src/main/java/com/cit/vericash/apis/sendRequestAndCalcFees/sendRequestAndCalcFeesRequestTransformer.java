package com.cit.vericash.apis.sendRequestAndCalcFees;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.CacheableVericashAPiImpl;
import com.cit.vericash.backend.commons.dynamicinputparameters.mapping.DynamicPayloadTransformer;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
@Component
public class sendRequestAndCalcFeesRequestTransformer extends MessageToBusinessMessageTransformerImpl{
    public static final String P2P_SERVICE_CODE = "6666";
    private final String OWNERTYPE = "sender-OwnerType";
    @Autowired
    private CachableVericashApi vericashApiCachable;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Autowired
    private DynamicPayloadTransformer dynamicPayloadTransformer;

    public sendRequestAndCalcFeesRequestTransformer() {
        vericashApiCachable = new CacheableVericashAPiImpl();

    }

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
            Payload payload = (Payload) dynamicPayloadTransformer.transform(message.getDynamicPayload());
            int moneyRequestTypeId = Integer.parseInt((String) payload.get("moneyRequestTypeId"));
            if(moneyRequestTypeId < 1 || moneyRequestTypeId > 3){
                throw new APIException("Invalid Money Request Type",
                        ErrorCode.INVALID_REQUEST_TYPE);
            }
        try {
            populateServiceTypeForFee(message);

        } catch (Exception e) {
            throw e;
        }
    }

    private void populateServiceTypeForFee(Message message) throws Exception {
        if (!message.getPayload().getAttributeAsBoolean("is_Routing_PaymentMethod_Service")) {
            String apiCode = message.getPayload().getAttributeAsString("serviceType");
            VericashAPI vericashAPI = vericashApiCachable.getVericashAPI(Long.parseLong(apiCode));
            if (vericashAPI == null) {
                throw new APIException("Unable to load VericashAPI",
                        ErrorCode.UNABLE_TO_LOAD_VERICASH_API.getErrorCode());
            }
            String child_service_code = message.getPayload().getAttributeAsString("child_service_code");
            String amount = message.getPayload().getAttributeAsString("amount");


            message.getPayload().put("servicesFees", child_service_code);
            message.getPayload().put("contributionAmount", amount);

        }


    }
}
