package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.common.group.transfers.SetupGroupTransferDTO;
import com.cit.mpaymentapp.model.group.transfers.GroupTransferSetup;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.SalaryDisbursementSearchComponent")
public class SalaryDisbursementSearchComponent implements VericashAction {
    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    @Override
    public Object process(Request request) throws Exception {
        List<Parameter> parameters = new ArrayList<Parameter>();
        List<String> fields = new ArrayList<String>();
        String groupName = request.getMessage().getPayload().getAttributeAsString("groupName");
        String customerId = request.getMessage().getPayload().getAttributeAsString("customerId");
        String apiCode = request.getMessage().getHeader().getAttributeAsString("apiCode");
        String bulkType = propertyLoaderComponent.getPropertyAsString(apiCode + ".bulk.type", "application", "application");

        fields = setFields(fields);

        //Returns all salary disbursements list
        if (groupName == null) {
            StringBuilder query = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("groupName_if_condition",this.getClass(),customerId,bulkType));
            List<Record> records = connectionUtil.executeSelect(query.toString(), parameters, fields);
            List<SetupGroupTransferDTO> groupTransferSetupList = new ArrayList<>();
            Record record = null;
            SetupGroupTransferDTO groupTransferSetupDTO = null;
            for (int i = 0; i < records.size(); i++) {
                record = records.get(i);
                groupTransferSetupDTO = buildGroupTransfer(record);
                groupTransferSetupList.add(groupTransferSetupDTO);
            }
            return groupTransferSetupList;
        }
        //Returns salary disbursements like groupName sent
        else {
            StringBuilder query = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("groupName_else_condition",this.getClass(),customerId,groupName,customerId,bulkType,groupName));

            List<Record> records = connectionUtil.executeSelect(query.toString(), parameters, fields);
            List<SetupGroupTransferDTO> groupTransferSetupList = new ArrayList<>();
            Record record = null;
            SetupGroupTransferDTO groupTransferSetup = null;
            for (int i = 0; i < records.size(); i++) {
                record = records.get(i);
                groupTransferSetup = buildGroupTransfer(record);
                groupTransferSetupList.add(groupTransferSetup);
            }
            return groupTransferSetupList;
        }
    }

    private SetupGroupTransferDTO buildGroupTransfer(Record record) {
        GroupTransferSetup groupTransferSetup = new GroupTransferSetup();

        if (record.getValueAsString("GROUP_NAME") != null)
            groupTransferSetup.setGroupName(record.getValueAsString("GROUP_NAME"));
        if (record.getValueAsLong("GROUP_TRANSFER_SETUP_ID") != null)
            groupTransferSetup.setGroupTransferSetupID(record.getValueAsLong("GROUP_TRANSFER_SETUP_ID"));
        if (record.getValueAsDate("CREATION_DATE") != null)
            groupTransferSetup.setCreationDate(record.getValueAsDate("CREATION_DATE"));
        if (record.getValueAsLong("CUSTOMER_ID") != null)
            groupTransferSetup.setCustomerId(record.getValueAsLong("CUSTOMER_ID"));
        if (record.getValueAsString("IN_PROGRESS") != null)
            groupTransferSetup.setInProgress(record.getValueAsString("IN_PROGRESS"));
        if (record.getValueAsBigDecimal("NUM_OF_RECIPIENTS") != null)
            groupTransferSetup.setNumOfRecipients(record.getValueAsBigDecimal("NUM_OF_RECIPIENTS"));
        if (record.getValueAsBigDecimal("TOTAL_AMOUNT") != null)
            groupTransferSetup.setTotalAmount(record.getValueAsBigDecimal("TOTAL_AMOUNT"));
        if (record.getValueAsDate("LAST_EXECUTION_DATE") != null)
            groupTransferSetup.setLastExecutionDate(record.getValueAsDate("LAST_EXECUTION_DATE"));
        if (record.getValueAsBigDecimal("HAS_RECURRING") != null)
            groupTransferSetup.setHasRecurring(record.getValueAsBigDecimal("HAS_RECURRING").intValue());
        if (record.getValueAsString("RECURRING_NAME") != null)
            groupTransferSetup.setRecurringName(record.getValueAsString("RECURRING_NAME"));
        if (record.getValueAsString("SERVICE_CODE") != null)
            groupTransferSetup.setServiceCode(record.getValueAsString("SERVICE_CODE"));
        if (record.getValueAsString("VALIDATION_STATUS") != null)
            groupTransferSetup.setValidationStatus(record.getValueAsString("VALIDATION_STATUS"));

        if (record.getValueAsDate("CREATION_DATE") != null)
            groupTransferSetup.setCreationDate(record.getValueAsDate("CREATION_DATE"));

        SetupGroupTransferDTO grSetupGroupTransferDTO = new SetupGroupTransferDTO();

        if (groupTransferSetup.getGroupTransferSetupID() != null)
            grSetupGroupTransferDTO.setId(groupTransferSetup.getGroupTransferSetupID());
        if (groupTransferSetup.getGroupName() != null)
            grSetupGroupTransferDTO.setName(groupTransferSetup.getGroupName());
        if (groupTransferSetup.getLastExecutionDate() != null)
            grSetupGroupTransferDTO.setLastExecutionDate(groupTransferSetup.getLastExecutionDate().toString());
        if (groupTransferSetup.getInProgress() != null)
            grSetupGroupTransferDTO.setInProgress(Boolean.parseBoolean(groupTransferSetup.getInProgress()));
        if (groupTransferSetup.getNumOfRecipients() != null)
            grSetupGroupTransferDTO.setNumberOfRecipients(groupTransferSetup.getNumOfRecipients());
        if (groupTransferSetup.getTotalAmount() != null)
            grSetupGroupTransferDTO.setTotalAmount(groupTransferSetup.getTotalAmount());
        if (groupTransferSetup.getTotalAmount() != null)
            grSetupGroupTransferDTO.setFormattedAmount(formatAmount(groupTransferSetup.getTotalAmount()));
        if (groupTransferSetup.getHasRecurring() != null)
            grSetupGroupTransferDTO.setHasRecurring(groupTransferSetup.getHasRecurring());
        if (groupTransferSetup.getRecurringName() != null)
            grSetupGroupTransferDTO.setRecurringName(groupTransferSetup.getRecurringName());
        if (groupTransferSetup.getCustomerId() != null)
            grSetupGroupTransferDTO.setCustomerId(groupTransferSetup.getCustomerId());
        if (groupTransferSetup.getValidationStatus() != null)
            grSetupGroupTransferDTO.setValidationStatus(groupTransferSetup.getValidationStatus());
        if (groupTransferSetup.getCreationDate() != null)
            grSetupGroupTransferDTO.setCreationDate(groupTransferSetup.getCreationDate());
        return grSetupGroupTransferDTO;
    }

    private String formatAmount(BigDecimal amount) {
        String formattedAmount = null;
        if (amount != null) {
            String pattern = "###,###.###";
            DecimalFormat decimalFormat = new DecimalFormat(pattern);
            formattedAmount = decimalFormat.format(amount);
        }
        return formattedAmount;
    }

    private List<String> setFields(List<String> fields) {
        fields.add("GROUP_TRANSFER_SETUP_ID");
        fields.add("CUSTOMER_ID");
        fields.add("IN_PROGRESS");
        fields.add("NUM_OF_RECIPIENTS");
        fields.add("TOTAL_AMOUNT");
        fields.add("GROUP_NAME");
        fields.add("CREATION_DATE");
        fields.add("LAST_EXECUTION_DATE");
        fields.add("HAS_RECURRING");
        fields.add("RECURRING_NAME");
        fields.add("SERVICE_CODE");
        fields.add("VALIDATION_STATUS");
        return fields;
    }

}
