getCommissionDetails=SELECT ba.ACCOUNT_ID, ba.PUBLIC_SHORT_CODE, ba.ACCOUNT_TYPE , spm.API_CODE \
  FROM BUSINESS_ACCOUNT ba LEFT JOIN SERVICES_PAYMENTMETHOD spm ON ba.ACCOUNT_TYPE = spm.PAYMENMETHODTYPE \
  WHERE ACCOUNT_TYPE = 2 AND spm.GAR_STATUS IS NOT NULL AND ba.ACCOUNTSTATUS in spm.GAR_STATUS AND \
  ba.BUSINESS_ENTITY_ID = \
  (SELECT bc.CORPERATE_ID FROM BE_CUSTOMER bc LEFT JOIN CUSTOMER c ON bc.ID = c.BE_CUSTOMER_ID \
  LEFT JOIN BE_ROLE br ON bc.\"ROLE\" = br.ID \
  LEFT JOIN BUSINESS_ENTITY_HIERARCHY beh ON beh.BUSINESS_ENTITY_ID = bc.CORPERATE_ID \
  WHERE c.USER_ID  = '%1$s' AND br.DISPLAY_COMMISSION_ACCOUNT = 1 AND beh.ALLOW_COMMISSION = 1)
