package com.cit.vericash.apis.salarydisbursement;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.ExecuteBulkTransferRequestTransformer")
public class ExecuteBulkTransferRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
    }
}
