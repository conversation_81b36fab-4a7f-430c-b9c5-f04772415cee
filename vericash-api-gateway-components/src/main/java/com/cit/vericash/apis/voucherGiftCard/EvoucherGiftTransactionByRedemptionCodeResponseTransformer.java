package com.cit.vericash.apis.voucherGiftCard;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.stereotype.Component;

@Component("EvoucherGiftTransactionByRedemptionCodeResponseTransformer")
public class EvoucherGiftTransactionByRedemptionCodeResponseTransformer implements MuleResponseTransformer {

    public Object transform(BusinessMessage businessMessage) throws Exception {
        // TODO Auto-generated method stub
        return null;
    }
}