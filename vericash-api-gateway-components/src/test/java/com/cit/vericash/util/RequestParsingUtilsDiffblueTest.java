package com.cit.vericash.util;

import static org.junit.Assert.assertTrue;

import com.cit.mpaymentapp.model.Enums;
import com.cit.service.commons.codeMapping.ServiceCodeMappingComponent;
import com.cit.vericash.api.components.JsonValidationStrategy;
import com.cit.vericash.api.components.impl.DynamicServiceValidationImpl;
import com.cit.vericash.api.transformerfactory.TransformerFactory;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.backend.commons.dynamicpayload.AdditionalData;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.EchoData;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.fasterxml.jackson.databind.node.TreeTraversingParser;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RequestParsingUtilsDiffblueTest {
    public static final long API_CODE = 123;
    public static final String SERVICE_CODE = "99355";
    public static final String CHILD_SERVICE_CODE = "199935551";
    @Mock
    private CachableVericashApi vericashApiCache;

    @Mock
    private TransformerFactory transformerFactory;

    @Mock
    private PropertyLoaderComponent propertyLoaderComponent;

    @Mock
    private ServiceCodeMappingComponent serviceCodeMappingComponent;

    @Mock
    private CachableVericashApi cachableVericashApi;

    @Mock
    private DynamicServiceValidationImpl dynamicServiceValidation;

    @Mock
    private JsonValidationStrategy jsonValidationStrategy;

    @InjectMocks
    private RequestParsingUtils requestParsingUtils;
    private final VericashAPI vericashAPI = new VericashAPI();
    private final DynamicPayload dynamicPayload = new DynamicPayload();
    private final Payload payload = new Payload();
    private final Message message = new Message();
    @Before
    public  void setup() {
        vericashAPI.setAsync(true);
        vericashAPI.setCode(1L);
        vericashAPI.setCompleteOtp("Complete Otp");
        vericashAPI.setEndPoint("https://config.us-east-2.amazonaws.com");
        vericashAPI.setExternalIntegration(true);
        vericashAPI.setInboundQueue("Inbound Queue");
        vericashAPI.setMappingConfigFile("Mapping Config File");
        vericashAPI.setMuleAction("Mule Action");
        vericashAPI.setName("Name");
        vericashAPI.setOutboundQueue("Outbound Queue");
        vericashAPI.setOutboundToMule(true);
        vericashAPI.setRequestAction("Request Action");
        vericashAPI.setRequestTransformer("Request Transformer");
        vericashAPI.setRequestType(1);
        vericashAPI.setResponseAction("Response Action");
        vericashAPI.setSchemaConfigFile("Schema Config File");
        vericashAPI.setServiceCode(SERVICE_CODE);
        vericashAPI.setServiceName("Service Name");
        vericashAPI.setSuccessMessage("Success Message");
        vericashAPI.setTransactionAPI(true);
        vericashAPI.setValidationType(Enums.ValidationType.SCHEMA);

        Header header = new Header();
        header.put("apiCode", API_CODE);
        dynamicPayload.setHeader(header);
        dynamicPayload.setPayload(new Payload());

        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(header);
        message.setPayload(new Payload());

    }
    /**
     * Method under test: {@link RequestParsingUtils#getJsonNode(Object, String[])}
     */
    @Test
    public void testGetJsonNode() {
        // Arrange

        // Act and Assert
        assertTrue(requestParsingUtils.getJsonNode("Message", new String[]{"Path To Json"})
                .traverse() instanceof TreeTraversingParser);
    }

    /**
     * Method under test: {@link RequestParsingUtils#getJsonNode(Object, String[])}
     */
    @Test
    public void testGetJsonNode3() {
        // Arrange

        // Act and Assert
        assertTrue(requestParsingUtils.getJsonNode("Message", new String[]{"Path To Json"})
                .traverse() instanceof TreeTraversingParser);
    }

}
