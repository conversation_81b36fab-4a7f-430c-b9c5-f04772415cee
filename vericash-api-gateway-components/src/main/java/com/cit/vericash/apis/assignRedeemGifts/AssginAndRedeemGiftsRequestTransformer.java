package com.cit.vericash.apis.assignRedeemGifts;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.stereotype.Component;

@Component("assginAndRedeemGiftsRequestTransformer")
public class AssginAndRedeemGiftsRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

    }
}
