package com.cit.vericash.apis.appRating;

import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.junit.Test;

import static org.junit.jupiter.api.Assertions.assertNull;

public class AppRatingResponseTransformerTest {

    @Test
    public void test_process_returns_null_for_valid_request() throws Exception {
        // Arrange
        AppRatingResponseTransformer myClass = new AppRatingResponseTransformer();
        Request request = new Request();
        request.setSignature("validSignature");
        request.setMessage(new Message());

        // Act
        Object result = myClass.process(request);

        // Assert
        assertNull(result);
    }

}