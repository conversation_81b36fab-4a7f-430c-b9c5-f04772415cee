package com.cit.vericash.apis.reverseCashout;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.reverseCashout.ReverseCashOutResponseTransformer")
public class ReverseCashOutResponseTransformer implements MuleResponseTransformer {
    String reversedTranAmountKey = "Reversed_Transaction_Amount";
    String ReversedFeeAmountKey = "Reversed_Fee_Amount";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
//        cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
        Map<String, Object> result = new HashMap<String, Object>();
        String reversedTranAmountVal = cacheableFieldParameters.getResponseParameters(reversedTranAmountKey);
        String reversedFeeAmountVal = cacheableFieldParameters.getResponseParameters(ReversedFeeAmountKey);

        result.put(reversedTranAmountVal, businessMessage.getTransactionInfo().getReverseTransactionInfo().getReversedAmount());
        return result;
    }
}
