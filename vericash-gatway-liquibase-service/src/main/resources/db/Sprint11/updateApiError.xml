<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet id="update Error Description for error code VAL142601" author="Michael">
        <sql>
            UPDATE API_ERROR
            SET ERROR_DESCRIPTION = 'This mobile number doesn’t exist'
            WHERE ERROR_CODE = 'VAL142601';
        </sql>
    </changeSet>

    <changeSet id="add api error for deleted user" author="Michael">
        <sql>
            INSERT INTO API_ERROR
            (ERROR_CODE,ERROR_DESCRIPTION)
            VALUES
            ('VAL202319','You are currently Closed, please contact APP support or try again later');
        </sql>
    </changeSet>
</databaseChangeLog>