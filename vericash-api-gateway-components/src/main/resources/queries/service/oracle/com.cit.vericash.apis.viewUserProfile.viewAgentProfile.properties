viewAgentProfile_process=SELECT STATE.NAME,BUSINESS_USERS.MOBILE_PHONE,H1.BUSINESS_ENTITY_NAME,COUNTRY.COUNTRY,H1.CITY,H2.BUSINESS_ENTITY_NAME AS parent,H1.CORPORATE_NAME,H1.REGISTRATION_NUMBER,COUNTRY.ISO_CODE,H1.TYPE AS TypeEnum,STATE.ID AS stataCode,\
  (SELECT SHORT_CODE FROM  BUSINESS_ENTITY_HIERARCHY  WHERE BUSINESS_ENTITY_ID = H1.WALLET ) AS HM, \
  CASE H1.TYPE WHEN 0 THEN 'Service Provider' WHEN 1 THEN 'Wallet' WHEN 2 THEN 'Business Entity' WHEN 3 THEN 'Portal Manager' WHEN 4 THEN 'Cash Collection' END as Type \
   FROM BUSINESS_ENTITY_HIERARCHY H1 \
  LEFT JOIN BUSINESS_ENTITY_HIERARCHY H2 ON H1.PARENT_BUSINESS_ENTITY = H2.BUSINESS_ENTITY_ID \
  LEFT JOIN COUNTRY ON H1.COUNTRY = COUNTRY.COUNTRY_ID \
  LEFT JOIN STATE ON STATE.ID = H1.STATE_ID \
  Left join BUSINESS_USERS ON H1.BUSINESS_ENTITY_ID = BUSINESS_USERS.BUSINESS_ENTITY_ID where BUSINESS_USERS.USERNAME=?