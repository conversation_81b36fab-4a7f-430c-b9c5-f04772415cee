GetSupporedSourcePaymnetMethod_BECustomer=SELECT  PM. NAME \"paymentMethodName\" ,PM.CURRENCY_OR_UNIT, SPM. ID \"paymentMethodType\", IPD.CODE, IPGD. NAME ,\
  IPGW.ID AS  \"G<PERSON><PERSON>ID\", SPM.ALLOW_UPDATE, SPM.ALLOW_DELETE, SPM.ALLOW_FREEZE, SPM.ALLOW_UNFREEZE, SPM.AUTOMATICALLY_OPENED, SPM.STORAGELOCATION,\
  SPM.IS_SINGLE_ACCOUNT, SPM.ACCEPT_OVER_DRAFT, SPM.IS_CREDIT, SPM.IS_DEBIT,\
  SPM.IS_DEBT, SPM.OPENED_UPON_CUS_REQ, SPM.OVER_DRAFT_LIMIT, SPM.ALLOW_BALANCE_INQUIRY, \
  IPGD.GROUP_CODE , IPV.DATATYPE, IPV.IS_UPDATEABLE ,  IPV.IS_VIEWABLE , IPVD.DATATYPE  AS DATATYPE_DV , \
  IPVD.IS_UPDATEABLE  AS  IS_UPDATEABLE_DV  ,  IPVD.IS_VIEWABLE AS   IS_VIEWABLE_DV ,  IPG.INPUT_PARAM_ORDER , \
  IPD.NAME \"parametre_Name\" , IPD.DEFAULT_VALIDATION_ID , IP.VALIDATION_ID , PM.PAYMETN_METHOD_ORDER \
  FROM CUSTOMER C JOIN PAYMENT_METHOD_CUST_PROF_CG PMCP ON C.CUSTOMER_TYPE_ID = PMCP.CUSTOMER_TYPE_ID \
  JOIN PAYMENT_METHOD PM ON PM.ID = PMCP.PAYMENT_METHOD_ID \
  JOIN SOURCE_PAYMENT_METHOD SPM ON SPM.ID = PM. ID \
  LEFT JOIN PAYMENT_METHOD_TYPE PMT ON PMT.ID = PM.\"TYPE\" \
  LEFT JOIN INPUT_PARAM_GROUP_WALLET IPGW ON IPGW.ID = PM.GROUP_ID \
  LEFT JOIN INPUT_PARAM_GROUP_DEF IPGD ON IPGD.ID = IPGW.GROUP_DEF_ID \
  LEFT JOIN INPUT_PARAMS_GROUPS IPG ON IPG.GROUP_WALLET_ID = IPGW.ID \
  LEFT JOIN INPUT_PARAMS IP ON IP.ID = IPG.INPUT_PARAM_ID \
  LEFT JOIN INPUT_PARAM_DICTIONARY IPD ON IPD.ID = IP.DICTIONARY_ID \
  LEFT JOIN INPUT_PARAM_VALIDATION IPV ON IPV.ID = IP.VALIDATION_ID \
  LEFT JOIN INPUT_PARAM_VALIDATION IPVD ON IPVD.ID = IPD.DEFAULT_VALIDATION_ID \
  where C.CUSTOMER_ID = ( select  BE.MAIN_CUSTOMER_ID  From  BE_CUSTOMER  BE \
  LEFT JOIN CUSTOMER C  ON C.BE_CUSTOMER_ID = BE.ID   where C.USER_ID= ? ) \
  ORDER BY IPG.INPUT_PARAM_ORDER
GetSupporedSourcePaymnetMethod_retal=SELECT PM. NAME \"paymentMethodName\", PM.CURRENCY_OR_UNIT, \
  SPM. ID \"paymentMethodType\", IPD.CODE, IPGD. NAME  ,IPGW.ID  AS  \"GROUPID\", SPM.ALLOW_UPDATE, \
  SPM.ALLOW_DELETE, SPM.ALLOW_FREEZE, SPM.ALLOW_UNFREEZE, SPM.AUTOMATICALLY_OPENED, SPM.STORAGELOCATION,\
  SPM.IS_SINGLE_ACCOUNT, SPM.ACCEPT_OVER_DRAFT, SPM.IS_CREDIT, SPM.IS_DEBIT,\
  SPM.IS_DEBT, SPM.OPENED_UPON_CUS_REQ, SPM.OVER_DRAFT_LIMIT, SPM.ALLOW_BALANCE_INQUIRY,\
  IPGD.GROUP_CODE , IPV.DATATYPE, IPV.IS_UPDATEABLE,  IPV.IS_VIEWABLE , IPVD.DATATYPE  AS DATATYPE_DV , \
  IPVD.IS_UPDATEABLE  AS  IS_UPDATEABLE_DV  ,  IPVD.IS_VIEWABLE AS   IS_VIEWABLE_DV ,  IPG.INPUT_PARAM_ORDER  , \
  IPD.NAME \"parametre_Name\" , IPD.DEFAULT_VALIDATION_ID , IP.VALIDATION_ID , PM.PAYMETN_METHOD_ORDER \
  FROM CUSTOMER C JOIN PAYMENT_METHOD_CUST_PROF_CG PMCP ON C.CUSTOMER_TYPE_ID = PMCP.CUSTOMER_TYPE_ID \
  JOIN PAYMENT_METHOD PM ON PM.ID = PMCP.PAYMENT_METHOD_ID \
  JOIN SOURCE_PAYMENT_METHOD SPM ON SPM. ID = PM. ID \
  LEFT JOIN PAYMENT_METHOD_TYPE PMT ON PMT. ID = PM.\"TYPE\" \
  LEFT JOIN INPUT_PARAM_GROUP_WALLET IPGW ON IPGW.ID = PM.GROUP_ID \
  LEFT JOIN INPUT_PARAM_GROUP_DEF IPGD ON IPGD.ID = IPGW.GROUP_DEF_ID \
  LEFT JOIN INPUT_PARAMS_GROUPS IPG ON IPG.GROUP_WALLET_ID = IPGW.ID \
  LEFT JOIN INPUT_PARAMS IP ON IP.ID = IPG.INPUT_PARAM_ID \
  LEFT JOIN INPUT_PARAM_DICTIONARY IPD ON IPD.ID = IP.DICTIONARY_ID \
  LEFT JOIN INPUT_PARAM_VALIDATION IPV ON IPV.ID = IP.VALIDATION_ID \
  LEFT JOIN INPUT_PARAM_VALIDATION IPVD ON IPVD.ID = IPD.DEFAULT_VALIDATION_ID \
  WHERE  C.USER_ID = ? \
  ORDER BY IPG.INPUT_PARAM_ORDER