package com.cit.vericash.apis.displayiconhome;

import com.cit.mpaymentapp.common.home.screen.AgentHomeIcon;
import com.cit.mpaymentapp.common.home.screen.CustomerHomeIcone;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.displayiconhome.DisplayIconHomeResponseTransformer")
public class DisplayIconHomeResponseTransformer implements MuleResponseTransformer {
    private final String agentHomeIconsKey = "agent_home_icons";
    private final String customerHomeIconsKey = "customer_home_icons";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();


        String currency = businessMessage.getAppConfig().getExtraData();
        ObjectMapper mapper = new ObjectMapper();

        ArrayList<AgentHomeIcon> homeIcon = businessMessage.getAppConfig().getHomeScreen().getAgentHomeIcon();
        String homeIconString = mapper.writeValueAsString(homeIcon);
        ArrayList<CustomerHomeIcone> homeIconforcustomer = businessMessage.getAppConfig().getHomeScreen().getCustomerHomeIcone();
        String homeIconStringforcustomer = mapper.writeValueAsString(homeIconforcustomer);
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            result.put(cacheableFieldParameters.getResponseParameters(agentHomeIconsKey), homeIconString);
//			result.put("currency",currency);
            result.put(cacheableFieldParameters.getResponseParameters(customerHomeIconsKey), homeIconStringforcustomer);
        } catch (Exception e) {
            throw e;
        }

        return result;
    }
}