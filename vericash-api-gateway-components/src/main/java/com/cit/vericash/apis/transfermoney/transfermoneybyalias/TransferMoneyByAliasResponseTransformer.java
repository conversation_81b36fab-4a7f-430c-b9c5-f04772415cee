package com.cit.vericash.apis.transfermoney.transfermoneybyalias;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class TransferMoneyByAliasResponseTransformer implements MuleResponseTransformer {
    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, Object> result = new HashMap<String, Object>();

        return result;
    }
}
