package com.cit.vericash.apis.ViewPMSDetailesWithBalance;


import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Component
@Qualifier("com.cit.vericash.apis.ViewPMSDetailesWithBalance.ViewPaymentMethodWithDetailesResponseTransformer")
public class ViewPaymentMethodWithDetailesResponseTransformer implements MuleResponseTransformer {


    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, Object> result = new TreeMap<String, Object>();


        if (businessMessage.getParameters().get("mapOfResult") != null) {

            Map<String, List<Map<String, Object>>> output = (Map<String, List<Map<String, Object>>>) businessMessage
                    .getParameters().get("mapOfResult");


            for (Map.Entry<String, List<Map<String, Object>>> entry : output.entrySet()) {

                List<Map<String, Object>> listofMaps = entry.getValue();
                String key = entry.getKey();
                result.put(key, listofMaps);
            }


        }

        return result;
    }
}