package com.cit.vericash.apis.integrationfermwork.apis;

import com.cit.vericash.apis.commons.api.IntegrationResponseTransformer;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
@Component
@Qualifier("com.cit.vericash.apis.integrationfermwork.apis.VaildatePMSResponseTransformerWithOutAuth")
public class VaildatePMSResponseTransformerWithOutAuth implements IntegrationResponseTransformer {
    @Override
    public Object transform(Payload payload) throws Exception {
        Map<String, Object> result = new HashedMap();


        Request orgRequest= (Request) payload.get("originalRequestOBJ");
        String senderMSISDN= (String) orgRequest.getMessage().getPayload().get("senderMSISDN");
        boolean vaildateMSISDN= orgRequest.getMessage().getPayload().getAttributeAsBoolean("vaildateMSISDN");

        if(payload.get("response")!=null )
        {

            Map integ_response=(HashMap<String ,Object>) payload.get("response");


            result.put("successScreenData", (Map<String, Object>) integ_response.get("paymentMethodType"));
        }
        return result ;

    }



}
