package com.cit.vericash.apis.updatecustomerpassword;


import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.updatecustomerpassword.UpdateCustomerPasswordResponseTransformer")
public class UpdateCustomerPasswordResponseTransformer implements MuleResponseTransformer {

	public Object transform(BusinessMessage businessMessage) throws Exception {

		Map<String, String> result = new HashMap<String, String>();

		try {


		} catch (Exception e) {
			throw e;
		}

		return result;
	}
}