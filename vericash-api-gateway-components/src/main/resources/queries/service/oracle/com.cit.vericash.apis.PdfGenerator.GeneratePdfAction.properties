getServiceCode=SELECT SERVICE_CODE, PARENT_SERVICE_CODE, SERVICE_TYPE_NAME \
  FROM SERVICE_CONFIG_MAP \
  WHERE SERVICE_TYPE_ID= (SELECT SERVICE_TYPE_ID  FROM TRANS_EXEC_SUMMARY WHERE TRNAS_EXEC_SUMMARY_ID = ?)
printBills=SELECT tes.SERVICE_NAME AS SERVICE_NAME, \
  tes.TRANSACTION_AMOUNT AS TRANSACTION_AMOUNT, \
  tes.TOTAL_FEE_AMOUNT AS FEE_AMOUNT , \
  COALESCE(tes.TOTAL_FEE_AMOUNT, 0) + COALESCE(tes.TRANSACTION_AMOUNT, 0) AS TOTAL_AMOUNT , \
  tes.TRANSACTION_STATUS AS TRANSACTION_STATUS, \
  tes.TRANSACTION_START_DATE AS TRANSACTION_DATE, \
  tes.TRNAS_EXEC_SUMMARY_ID AS TRNAS_EXEC_SUMMARY_ID, \
  tes.SENDER_ACCOUNT AS SENDER_ACCOUNT, \
  tes.SOURCE_PAYMENTMETHOD_ID AS SOURCE_PAYMENTMETHOD_ID, \
  tes.SENDER_MSISDN AS SENDER_MSISDN, \
  tes.SOURCE_DYNAMIC_PARAMTERS AS SOURCE_PM, \
  tes.RECEIEVER_ACCOUNT AS RECEIEVER_ACCOUNT, \
  tes.DESTINATION_PAYMENTMETHOD_ID AS DESTINATION_PAYMENTMETHOD_ID, \
  tes.RECEIVER_MSISDN AS RECEIVER_MSISDN, \
  tri.TRANSACTION_CATEGORY AS CATEGORY, \
  tes.CURRENCY AS CURRENCY , \
  tes.FAILED_REASON  AS FAILED_REASON, \
  COALESCE(tri.BILLER_NAME, tri.BILLPAYMENT_OPERATOR_NAME) AS BILLER_NAME \
  FROM TRANS_EXEC_SUMMARY tes \
  LEFT OUTER JOIN TRANSACTION_RECEIPT_INFO tri \
  ON tes.TRNAS_EXEC_SUMMARY_ID=tri.TRNAS_EXEC_SUMMARY_ID \
  WHERE tes.TRNAS_EXEC_SUMMARY_ID = %1$s

printTransfer=SELECT tri.SERVICE_NAME AS SERVICE_NAME,\
  tri.TRANSACTION_AMOUNT AS TRANSACTION_AMOUNT,\
  tes.TRANSACTION_STATUS AS TRANSACTION_STATUS,\
  COALESCE(tri.TRANSACTION_DATE, tes.TRANSACTION_START_DATE) AS TRANSACTION_DATE, \
  tes.TRNAS_EXEC_SUMMARY_ID AS TRNAS_EXEC_SUMMARY_ID,\
  tes.SENDER_ACCOUNT AS SENDER_ACCOUNT,\
  tes.SOURCE_PAYMENTMETHOD_ID AS SOURCE_PAYMENTMETHOD_ID,\
  tes.SENDER_MSISDN AS SENDER_MSISDN,\
  tes.SOURCE_DYNAMIC_PARAMTERS AS SOURCE_PM, \
  tes.RECEIEVER_ACCOUNT AS RECEIEVER_ACCOUNT,\
  tes.TOTAL_FEE_AMOUNT   AS FEE_AMOUNT , \
  tes.DESTINATION_PAYMENTMETHOD_ID AS DESTINATION_PAYMENTMETHOD_ID,\
  tes.RECEIVER_MSISDN AS RECEIVER_MSISDN,\
  tes.CURRENCY AS CURRENCY , \
  tes.DESTINATION_DYNAMIC_PARAMTERS AS DESTINATION_PM \
  FROM TRANS_EXEC_SUMMARY tes  \
  LEFT OUTER JOIN TRANSACTION_RECEIPT_INFO tri \
  ON tes.TRNAS_EXEC_SUMMARY_ID=tri.TRNAS_EXEC_SUMMARY_ID \
  WHERE tes.TRNAS_EXEC_SUMMARY_ID =%1$s

printEVoucher=SELECT tri.SERVICE_NAME AS SERVICE_NAME,\
  tri.TRANSACTION_AMOUNT AS TRANSACTION_AMOUNT,\
  tes.TRANSACTION_STATUS AS TRANSACTION_STATUS,\
  tes.TRNAS_EXEC_SUMMARY_ID AS TRNAS_EXEC_SUMMARY_ID,\
  tes.SENDER_ACCOUNT AS SENDER_ACCOUNT,\
  tes.SOURCE_PAYMENTMETHOD_ID AS SOURCE_PAYMENTMETHOD_ID,\
  tes.SENDER_MSISDN AS SENDER_MSISDN,\
  tes.SOURCE_DYNAMIC_PARAMTERS AS SOURCE_PM, \
  tri.PARTNER_NAME AS PARTNER_NAME,\
  tri.EVOUCHER_NAME AS EVOUCHER_NAME,\
  tes.TOTAL_FEE_AMOUNT   AS FEE_AMOUNT , \
  tes.CURRENCY AS CURRENCY , \
  tri.EXPIRATION_DATE AS EXPIRATION_DATE \
  FROM TRANS_EXEC_SUMMARY tes  \
  LEFT OUTER JOIN TRANSACTION_RECEIPT_INFO tri \
  ON tes.TRNAS_EXEC_SUMMARY_ID=tri.TRNAS_EXEC_SUMMARY_ID \
  WHERE tri.TRNAS_EXEC_SUMMARY_ID =%1$s

printDefault=SELECT tes.SERVICE_NAME AS SERVICE_NAME, \
  tes.TRANSACTION_AMOUNT AS TRANSACTION_AMOUNT, \
  tes.TOTAL_FEE_AMOUNT AS FEE_AMOUNT , \
  COALESCE(tes.TOTAL_FEE_AMOUNT, 0) + COALESCE(tes.TRANSACTION_AMOUNT, 0) AS TOTAL_AMOUNT , \
  tes.TRANSACTION_STATUS AS TRANSACTION_STATUS, \
  tes.TRANSACTION_START_DATE AS TRANSACTION_DATE, \
  tes.TRNAS_EXEC_SUMMARY_ID AS TRNAS_EXEC_SUMMARY_ID, \
  tes.SENDER_ACCOUNT AS SENDER_ACCOUNT, \
  tes.SOURCE_PAYMENTMETHOD_ID AS SOURCE_PAYMENTMETHOD_ID, \
  tes.SENDER_MSISDN AS SENDER_MSISDN, \
  tes.SOURCE_DYNAMIC_PARAMTERS AS SOURCE_PM, \
  tes.RECEIEVER_ACCOUNT AS RECEIEVER_ACCOUNT, \
  tes.DESTINATION_PAYMENTMETHOD_ID AS DESTINATION_PAYMENTMETHOD_ID, \
  tes.RECEIVER_MSISDN AS RECEIVER_MSISDN, \
  tri.EVOUCHER_NAME AS EVOUCHER_NAME, \
  tri.PARTNER_NAME AS PARTNER_NAME, \
  tri.TRANSACTION_CATEGORY AS CATEGORY, \
  tes.CURRENCY AS CURRENCY , \
  tes.FAILED_REASON  AS FAILED_REASON, \
  COALESCE(tri.BILLER_NAME, tri.BILLPAYMENT_OPERATOR_NAME) AS BILLER_NAME , \
  c.MSISDN AS RECEIVER_USER_MSISDN \
  FROM TRANS_EXEC_SUMMARY tes \
  LEFT OUTER JOIN TRANSACTION_RECEIPT_INFO tri \
  ON tes.TRNAS_EXEC_SUMMARY_ID=tri.TRNAS_EXEC_SUMMARY_ID \
  LEFT OUTER JOIN MONEY_REQUEST_RECEIVER mrr \
  ON mrr.TRANSACTION_ID =tes.TRNAS_EXEC_SUMMARY_ID \
  LEFT OUTER JOIN MONEY_REQUEST mr \
  ON mr.id=mrr.MONEY_REQUEST_ID \
  LEFT OUTER JOIN CUSTOMER c \
  ON c.CUSTOMER_ID =mr.SENDER_USER_ID \
  WHERE tes.TRNAS_EXEC_SUMMARY_ID = %1$s
