package com.cit.vericash.apis.billpaymentbycustomer;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.billpaymentbycustomer.BillPaymentByCustomerRequestTransformer")
public class BillPaymentByCustomerRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    private final String bill_Key = "bill_key";
    private final String bill_Value = "bill_value";
    private final String billAttributesKey = "bill_attributes";
    private final String amountKey = "amount";
    private final String billAmountKey = "bill_amount";
    private final String OWNERTYPE = "sender-OwnerType";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public BillPaymentByCustomerRequestTransformer() {
    }

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        try {
//			cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
            validationInRequest(message);
            //	populateSenderOwnerType(message);
            setParmeterInMessage(message);

        } catch (Exception e) {
            throw e;
        }
    }


    /**
     * get Paramter from request and set it in payload
     *
     * @param message
     * @return void
     * @throws Exception
     */
    private void setParmeterInMessage(Message message) throws Exception {

        String billKey = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(bill_Key));
        String billValue = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(bill_Value));
        Map<String, String> billAttributes = new HashMap<String, String>();
        billAttributes.put(billKey, billValue);
        message.getPayload().put(cacheableFieldParameters.getRequestParameters(billAttributesKey), billAttributes);


    }


    /**
     * validate in  request
     *
     * @param message
     * @return void
     * @throws Exception
     */
    private void validationInRequest(Message message) throws Exception {
        String transactionAmount = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(amountKey));
        String billAmount = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(billAmountKey));
        if (!transactionAmount.equals(billAmount)) {
            throw new APIException("Transaction amount  must  equal  bill amount ", ErrorCode.TRANSACTION_AMOUNT_SHOULD_EQUAL_BILL_AMOUNT.getErrorCode());
        }


    }

}