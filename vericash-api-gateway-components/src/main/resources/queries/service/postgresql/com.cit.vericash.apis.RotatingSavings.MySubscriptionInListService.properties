getMySubscriptionInList=SELECT rs.ID,rs.STATUS, rs.TOTAL_AMOUNT ,rs.CURRENCY,\
  rs.SUBSCRIPTION_START_DATE,rs.SUBSCRIPTION_END_DATE, rs.CONTRIBUTION_AMOUNT, \
  rs.CONTRIBUTION_FREQUENCY,rsf.NAME AS RSF_NAME,rsmt.TURN_NUMBER,rsmt.STATUS AS mem_status, rs.END_DATE \
  FROM ROTATING_SAVINGS rs \
  INNER JOIN ROTATING_SAVINGS_FREQUENCY rsf ON rs.CONTRIBUTION_FREQUENCY = rsf.ID \
  INNER JOIN ROTATING_SAVINGS_MEMBERS rsm ON rsm.ROTATING_SAVINGS_ID =rs.ID \
  INNER JOIN ROTATING_SAVINGS_MEM_TURNS rsmt ON rsmt.MEMBER_ID = rsm.ID \
  WHERE rsm.CUSTOMER_ID = %1$s AND (rs.STATUS =0 OR  rs.STATUS =1 OR rs.STATUS =2 OR rs.STATUS =3) \
  ORDER BY rs.CREATION_DATE ASC
