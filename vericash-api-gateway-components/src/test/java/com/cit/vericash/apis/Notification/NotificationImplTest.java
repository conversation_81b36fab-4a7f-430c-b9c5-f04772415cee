package com.cit.vericash.apis.Notification;


import com.cit.mpaymentapp.common.customer.message.ClientInfo;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.repository.customer.CustomerRepository;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NotificationImplTest {

    @Mock
    private ConnectionUtil connectionUtil;

    @Mock
    private CustomerRepository customerRepository;

    @Mock
    private PropertyLoaderComponent propertyLoaderComponent;

    @InjectMocks
    private NotificationImpl notificationImpl;

    private Request request;

    @BeforeEach
    void setUp() {
        request = createValidRequest();
    }

    @Test
    void viewAllNotification_WhenNoRecordsAndInvalidUser_ThrowsException() {
        // Mock empty records and user not found
        when(connectionUtil.executeSelect(anyString(), anyList(), anyList())).thenReturn(Collections.emptyList());
        when(customerRepository.existsByIdentificationKey(anyString())).thenReturn(false);

        assertThrows(GeneralFailureException.class, () -> notificationImpl.viewAllNotification(request));
    }

    @Test
    void viewAllNotification_WhenNoRecordsAndValidUser_ReturnsEmptyList() throws GeneralFailureException {
        // Mock empty records and valid user
        when(connectionUtil.executeSelect(anyString(), anyList(), anyList())).thenReturn(Collections.emptyList());
        when(customerRepository.existsByIdentificationKey(anyString())).thenReturn(true);

        List<MobileNotification> result = notificationImpl.viewAllNotification(request);
        assertTrue(result.isEmpty());
    }

    @Test
    void viewAllNotification_WithRecords_ReturnsNotifications() throws Exception {
        // Mock records with data
        Record mockRecord = mock(Record.class);
        Date mockDate = new Date();
        when(mockRecord.getValueAsLong("NOTIFICATION_ID")).thenReturn(1L);
        when(mockRecord.getValueAsBigDecimal("ISREADING")).thenReturn(BigDecimal.ONE);
        when(mockRecord.getValueAsDate("NOTIFICATION_DATE")).thenReturn(mockDate);
        when(mockRecord.getValueAsString("TITLE")).thenReturn("Test Title");
        when(mockRecord.getValueAsString("CONTENT")).thenReturn("Test Content");
        when(connectionUtil.executeSelect(anyString(), anyList(), anyList())).thenReturn(List.of(mockRecord));

        List<MobileNotification> result = notificationImpl.viewAllNotification(request);
        assertEquals(1, result.size());
        MobileNotification notification = result.get(0);
        assertEquals(1L, notification.getId());
        assertTrue(notification.read);
        assertEquals("Test Title", notification.getTitle());
        assertEquals("Test Content", notification.getContent());
        assertEquals(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(mockDate), notification.getDate());
    }

    @Test
    void viewRecentNotification_PlatformAndroid_IncludesAndroidNotifications() throws GeneralFailureException {
        // Setup Android platform and mock records
        HashMap<String, Object> clientInfo = (HashMap<String, Object>) request.getMessage().getPayload().getAttribute("clientInfo");
        clientInfo.put("platformName", "Android"); // Match key used in production code

        // Mock nRecent property
        when(propertyLoaderComponent.getPropertyAsString("nRecent.notification.size", "application", "application"))
                .thenReturn("5");

        // Create mock notification record with Android REQUEST
        Record androidRecord = createMockRecord("Android", 1L);

        // Stub main query (target SQL for notifications)
        when(connectionUtil.executeSelect(
                argThat(sql -> sql.contains("SELECT NOTIFICATION_ID,ISREADING,NOTIFICATION_DATE,TITLE,CONTENT,TYPE,EXTRA_FIELDS,REQUEST,IMAGE_URL FROM MOBILE_NOTIFICATION WHERE CUSTOMER_KEY = '2020+27833505298' AND STATUS = 1 ORDER BY NOTIFICATION_DATE DESC")), // Target main query SQL
                anyList(),
                anyList()
        )).thenReturn(List.of(androidRecord));

        // Stub action queries to return empty lists (prevent NPE)
        when(connectionUtil.executeSelect(
                contains("SELECT na.ACTION, na.EXPIRY_DATE  FROM NOTIFICATION_ACTION na WHERE na.NOTIFICATION_ID ='1'"), // Match action query SQL
                anyList(),
                anyList()
        )).thenReturn(Collections.emptyList());

        // Execute and verify
        List<MobileNotification> result = notificationImpl.viewRecentNotification(request);
        assertEquals(1, result.size());
    }

    @Test
    void viewRecentNotification_PlatformIOS_IncludesIOSNotifications() throws GeneralFailureException {
        // Setup iOS platform and mock records
        HashMap<String, Object> clientInfo = (HashMap<String, Object>) request.getMessage().getPayload().getAttribute("clientInfo");
        clientInfo.put("platformName", "iOS"); // Set platform to iOS

        // Mock nRecent property
        when(propertyLoaderComponent.getPropertyAsString("nRecent.notification.size", "application", "application"))
                .thenReturn("5");

        // Create mock notification record with iOS REQUEST
        Record iosRecord = createMockRecord("iOS", 1L);

        // Stub main query to return iOS record
        when(connectionUtil.executeSelect(
                argThat(sql -> sql.contains("SELECT NOTIFICATION_ID,ISREADING,NOTIFICATION_DATE,TITLE,CONTENT,TYPE,EXTRA_FIELDS,REQUEST,IMAGE_URL FROM MOBILE_NOTIFICATION WHERE CUSTOMER_KEY = '2020+27833505298' AND STATUS = 1 ORDER BY NOTIFICATION_DATE DESC")), // Target main query SQL
                anyList(),
                anyList()
        )).thenReturn(List.of(iosRecord));

        // Stub action queries to return empty lists (prevent NPE)
        when(connectionUtil.executeSelect(
                contains("SELECT na.ACTION, na.EXPIRY_DATE  FROM NOTIFICATION_ACTION na WHERE na.NOTIFICATION_ID ='1'"), // Match action query SQL
                anyList(),
                anyList()
        )).thenReturn(Collections.emptyList());

        // Execute and verify
        List<MobileNotification> result = notificationImpl.viewRecentNotification(request);
        assertEquals(1, result.size());
    }
    @Test
    void viewRecentNotification_RespectsNRecentLimit() throws GeneralFailureException {
        // Mock nRecent property
        when(propertyLoaderComponent.getPropertyAsString("nRecent.notification.size", "application", "application"))
                .thenReturn("2");

        // Create mock notification records
        List<Record> records = new ArrayList<>();
        for (long i = 0; i < 1; i++) {
            records.add(createMockRecord(null, i)); // REQUEST is null
        }
        when(connectionUtil.executeSelect(
                argThat(sql -> !sql.contains("getNotificationActionById")),
                anyList(),
                anyList()
        )).thenReturn(records);

        // Mock action records with valid EXPIRY_DATE (case-sensitive)
        Record validActionRecord = mock(Record.class);
        when(validActionRecord.getValueAsString("ACTION")).thenReturn("VIEW");
        when(validActionRecord.getValueAsDate("EXPIRY_DATE")).thenReturn(new Date()); // Ensure column name matches

        // Stub action queries to return valid records
        when(connectionUtil.executeSelect(
                contains("SELECT na.ACTION, na.EXPIRY_DATE  FROM NOTIFICATION_ACTION na WHERE na.NOTIFICATION_ID ='0'"), // Match SQL fragment
                anyList(),
                anyList()
        )).thenReturn(List.of(validActionRecord));

        // Execute and verify
        List<MobileNotification> result = notificationImpl.viewRecentNotification(request);
        assertEquals(1, result.size());
    }

//    @Test
//    void viewRecentNotification_IncludesActions() {
//        // Mock notification with actions
//        when(propertyLoaderComponent.getPropertyAsString("nRecent.notification.size", "application", "application"))
//                .thenReturn("5");
//        Record mockRecord = createMockRecord(null, 1L);
//        when(connectionUtil.executeSelect(anyString(), anyList(), anyList())).thenReturn(List.of(mockRecord));
//
//        // Mock actions
//        Record actionRecord = mock(Record.class);
//        Date expiryDate = new Date();
//        when(actionRecord.getValueAsString("ACTION")).thenReturn("VIEW");
//        when(actionRecord.getValueAsDate("EXPIRY_DATE")).thenReturn(expiryDate);
//        when(connectionUtil.executeSelect(contains("getNotificationActionById"), anyList(), anyList()))
//                .thenReturn(List.of(actionRecord));
//
//        List<MobileNotification> result = notificationImpl.viewRecentNotification(request);
//        assertEquals(1, result.size());
//        assertEquals(1, result.get(0).getActions().size());
//        assertEquals("VIEW", result.get(0).getActions().get(0).getAction());
//        assertEquals(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(expiryDate),
//                result.get(0).getActions().get(0).getExpiryDate());
//    }

    @Test
    void deleteNotification_ExecutesUpdate() {
        // Setup request with notification ID
        request.getMessage().getPayload().put("notificationId", 123L);

        notificationImpl.deleteNotification(request);
        verify(connectionUtil, times(1)).executeUpdate(anyString(), anyList());
    }

    @Test
    void readNotification_ExecutesUpdate() {
        // Setup request with notification ID
        request.getMessage().getPayload().put("notificationId", 123L);

        notificationImpl.readNotification(request);
        verify(connectionUtil, times(1)).executeUpdate(anyString(), anyList());
    }

    private Request createValidRequest() {
        Request request = new Request();
        Message message = new Message();
        request.setMessage(message);
        Payload payload = new Payload();
        message.setPayload(payload);
        HashMap<String, Object> clientInfo = new HashMap<>();
        payload.put("clientInfo",clientInfo );

        // Add all fields from the example
        payload.put("userKey", "2020+27833505298");
        payload.put("nRecent", true);
        payload.put("senderOwnerType", "1");
        payload.put("senderIdentifyValue", "+27833505298");


        clientInfo.put("platformName", "Android");


        payload.put("clientInfo", clientInfo);

        // Create header if needed
        Header header = new Header();
        header.put("apiCode", "2250");
        header.put("walletShortCode", "2020");
        header.put("timestamp", 1739903800L);
        header.put("sessionId", "1739903505294");
        header.put("channelCode", "test_1");

        request.getMessage().setHeader(header);
        request.getMessage().setPayload(payload);

        return request;
    }


    private Record createMockRecord(String requestPlatform, Long notificationId) {
        Record record = mock(Record.class, withSettings().lenient());
        lenient().when(record.getValueAsLong("NOTIFICATION_ID")).thenReturn(notificationId);
        lenient().when(record.getValueAsBigDecimal("ISREADING")).thenReturn(BigDecimal.ZERO);
        lenient().when(record.getValueAsDate("NOTIFICATION_DATE")).thenReturn(new Date());
        lenient().when(record.getValueAsString("TITLE")).thenReturn("Title " + notificationId);
        lenient().when(record.getValueAsString("CONTENT")).thenReturn("Content " + notificationId);
        lenient().when(record.getValueAsString("REQUEST")).thenReturn(requestPlatform);
        return record;
    }
}