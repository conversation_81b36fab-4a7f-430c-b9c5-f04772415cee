package com.cit.vericash.apis.validatePaymentMethod;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.validatePaymentMethod.validatePaymentMethodRequestTransformer")
public class validatePaymentMethodRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    private String sourcePaymentMethodKey = "source-Payment-Method";
    private String destinationPaymentMethodKey = "destination-Payment-Method";

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {


        // map sourcePaymentMethod in payload to sourcePaymentMethod in BM
        //ObjectMapper mapperSource = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        // SourcePaymentMethod sourcePaymentMethod;


        //Object SourcePaymentMethodObj = message.getPayload().get(cacheableFieldParameters.getRequestParameters(sourcePaymentMethodKey));
        // String sourceJson = ow.writeValueAsString(SourcePaymentMethodObj);
        // sourcePaymentMethod = mapperSource.readValue(sourceJson, SourcePaymentMethod.class);

        //message.getPayload().put("sourcePaymentMethod", sourcePaymentMethod);
        ///////////////////////////////////////////////////////////////////////////////////////
        //targetObject.setValue("/sourcePaymentMethod", sourcePaymentMethod);

        //String paymentMethodName=message.getPayload().getAttributeAsString("paymentMethodName");
        //Long paymentMethodType=message.getPayload().getAttributeAsLong("paymentMethodType");

        //Gson gson = new Gson();
        // String parameters = gson.toJson(message.getPayload().get("dynamicParameters"));
        //DynamicGroupParametersDTOValidator dynamicGroupParametersDTO = gson.fromJson(parameters, DynamicGroupParametersDTOValidator.class);


        //  PaymentMethodDTO paymentMethodDTO=new PaymentMethodDTO();
        // paymentMethodDTO.setDynamicGroup(dynamicGroupParametersDTO);
        //paymentMethodDTO.setPaymentMethodName(paymentMethodName);
        //paymentMethodDTO.setPaymentMethodType(paymentMethodType);
        //message.getPayload().put("paymentMethod", paymentMethodDTO);


    }
}


