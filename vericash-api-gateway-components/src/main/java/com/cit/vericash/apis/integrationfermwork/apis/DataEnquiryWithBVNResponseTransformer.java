package com.cit.vericash.apis.integrationfermwork.apis;

import com.cit.shared.error.exception.IntegrationException;
import com.cit.vericash.apis.commons.api.IntegrationResponseTransformer;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.integrationfermwork.apis.DataEnquiryWithBVNResponseTransformer")

public class DataEnquiryWithBVNResponseTransformer implements IntegrationResponseTransformer {
    @Override
    public Object transform(Payload payload) throws Exception {
        Map<String, Object> result = new HashedMap();
        Request orgRequest= (Request) payload.get("originalRequestOBJ");
        String birthdate= (String) orgRequest.getMessage().getPayload().get("birthdate");


        if(payload.get("response")!=null)
        {

            Map integ_response=(HashMap<String ,Object>) payload.get("response");
            Map<String, Object> bvnData=(Map<String, Object>) integ_response.get("paymentMethodType");
            String bvnBirthDate= (String) bvnData.get("birthdate");

            if(!bvnBirthDate.equals(birthdate))
            {
                throw new IntegrationException(ErrorCode.DATES_NOT_MATCH,"Date of birth entered does not match BVN data");
            }

            result.put("successScreenData", bvnData);
        }
        return result ;

    }

}
