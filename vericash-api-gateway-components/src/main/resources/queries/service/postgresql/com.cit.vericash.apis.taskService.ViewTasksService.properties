History_Status=SELECT * FROM ( SELECT ACTIVITY_TRAIL.TASK_ID,ACTIVITY_TRAIL.STATUS, SERVICE_CONFIG_MAP.SERVICE_TYPE_NAME, \
  ACTIVITY_TRAIL.TASK_DATE, ACTIVITY_TRAIL.USER_NAME, APPROVAL_STATUS. NAME, \
  ACTIVITY_TRAIL.REJECT_REASON,CUSTOMER.PROFILE_PICTURE_LINK, ROW_NUMBER () OVER \
  ( ORDER BY ACTIVITY_TRAIL.TASK_ID,ACTIVITY_TRAIL.STATUS, SERVICE_CONFIG_MAP.SERVICE_TYPE_NAME, \
  ACTIVITY_TRAIL.TASK_DATE, ACTIVITY_TRAIL.USER_NAME, APPROVAL_STATUS. NAME, \
  ACTIVITY_TRAIL.REJECT_REASON,CUSTOMER.PROFILE_PICTURE_LINK ) rn \
  FROM ACTIVITY_TRAIL INNER JOIN SERVICE_CONFIG_MAP \
  ON SERVICE_CONFIG_MAP.SERVICE_TYPE_ID = ACTIVITY_TRAIL.SERVICE_ID \
  INNER JOIN APPROVAL_STATUS ON APPROVAL_STATUS. ID = ACTIVITY_TRAIL.STATUS \
  LEFT JOIN CUSTOMER ON CUSTOMER.CUSTOMER_ID = ACTIVITY_TRAIL.USER_ID \
  WHERE ACTIVITY_TRAIL.USER_ID = %1$s AND ACTIVITY_TRAIL.STATUS = 1
Approved=SELECT * FROM ( SELECT ACTIVITY_TRAIL.TASK_ID,ACTIVITY_TRAIL.STATUS, SERVICE_CONFIG_MAP.SERVICE_TYPE_NAME, ACTIVITY_TRAIL.TASK_DATE, ACTIVITY_TRAIL.USER_NAME, APPROVAL_STATUS. NAME, ACTIVITY_TRAIL.REJECT_REASON, ROW_NUMBER () OVER\
  \ ( ORDER BY ACTIVITY_TRAIL.TASK_ID,ACTIVITY_TRAIL.STATUS, SERVICE_CONFIG_MAP.SERVICE_TYPE_NAME, ACTIVITY_TRAIL.TASK_DATE, ACTIVITY_TRAIL.USER_NAME, APPROVAL_STATUS. NAME, ACTIVITY_TRAIL.REJECT_REASON,CUSTOMER.PROFILE_PICTURE_LINK ) rn \
  FROM ACTIVITY_TRAIL INNER JOIN SERVICE_CONFIG_MAP ON SERVICE_CONFIG_MAP.SERVICE_TYPE_ID = ACTIVITY_TRAIL.SERVICE_ID \
  INNER JOIN APPROVAL_STATUS ON APPROVAL_STATUS. ID = ACTIVITY_TRAIL.STATUS \
  LEFT JOIN CUSTOMER ON CUSTOMER.CUSTOMER_ID = ACTIVITY_TRAIL.USER_ID\
  \ WHERE APPROVAL_STATUS. ID = 1 AND ACTIVITY_TRAIL.USER_ID = %1$s 
PendingApproval=SELECT * FROM \
  ( SELECT ACTIVITY_TRAIL.TASK_ID,ACTIVITY_TRAIL.STATUS, SERVICE_CONFIG_MAP.SERVICE_TYPE_NAME, ACTIVITY_TRAIL.TASK_DATE, ACTIVITY_TRAIL.USER_NAME, \
  APPROVAL_STATUS. NAME, ACTIVITY_TRAIL.REJECT_REASON,CUSTOMER.PROFILE_PICTURE_LINK, \
  ROW_NUMBER () OVER ( ORDER BY ACTIVITY_TRAIL.TASK_ID,ACTIVITY_TRAIL.STATUS, SERVICE_CONFIG_MAP.SERVICE_TYPE_NAME, ACTIVITY_TRAIL.TASK_DATE, \
  ACTIVITY_TRAIL.USER_NAME, APPROVAL_STATUS. NAME, ACTIVITY_TRAIL.REJECT_REASON,CUSTOMER.PROFILE_PICTURE_LINK ) rn \
  FROM ACTIVITY_TRAIL \
  INNER JOIN SERVICE_CONFIG_MAP ON SERVICE_CONFIG_MAP.SERVICE_TYPE_ID = ACTIVITY_TRAIL.SERVICE_ID \
  INNER JOIN APPROVAL_STATUS ON APPROVAL_STATUS. ID = ACTIVITY_TRAIL.STATUS\
  \ INNER JOIN APPROVED_BY ON APPROVED_BY. GROUP_ID = ACTIVITY_TRAIL. GROUP_ID\
  \ INNER JOIN BE_ROLE_APPROVAL_SERVICES ON BE_ROLE_APPROVAL_SERVICES.SERVICE_TYPE_ID = SERVICE_CONFIG_MAP.SERVICE_TYPE_ID\
  \ INNER JOIN BE_ROLE ON BE_ROLE. ID = BE_ROLE_APPROVAL_SERVICES.ROLE_ID\
  \ INNER JOIN APPROVAL_CYCLE_GROUP ON APPROVAL_CYCLE_GROUP. ID = ACTIVITY_TRAIL. GROUP_ID\
  \ LEFT JOIN CUSTOMER ON CUSTOMER.CUSTOMER_ID = ACTIVITY_TRAIL.USER_ID \
  WHERE APPROVAL_STATUS. ID = 0 AND APPROVED_BY.APPROVAL_STATUS = 0 \
  AND APPROVAL_CYCLE_GROUP.BUSINESS_ENTITY_ID = '%1$s' AND APPROVED_BY.USER_ID = %2$s 
getQuery_1=GROUP BY ACTIVITY_TRAIL.TASK_ID, \
  SERVICE_CONFIG_MAP.SERVICE_TYPE_NAME,ACTIVITY_TRAIL.TASK_DATE,ACTIVITY_TRAIL.USER_NAME,APPROVAL_STATUS. NAME,\
  ACTIVITY_TRAIL.REJECT_REASON,ACTIVITY_TRAIL.STATUS,CUSTOMER.PROFILE_PICTURE_LINK 
getQuery_2=ORDER BY TASK_DATE ASC
getQuery_3=ORDER BY TASK_DATE DESC
getQuery_4=) WHERE RN BETWEEN %1$s AND %2$s
checkTaskFilter_1=AND lower (SERVICE_CONFIG_MAP.SERVICE_TYPE_NAME) LIKE (?)
checkTaskFilter_2=OR lower(ACTIVITY_TRAIL.USER_NAME) LIKE (?)
checkTaskFilter_3=AND TO_CHAR(ACTIVITY_TRAIL.TASK_DATE,'YYYY-MM-DD') = ?
checkTaskFilter_4=AND TO_CHAR(ACTIVITY_TRAIL.TASK_DATE,'YYYY-MM-DD') >= ?
checkTaskFilter_5=AND TO_CHAR(ACTIVITY_TRAIL.TASK_DATE,'YYYY-MM-DD') <= ?




