package com.cit.vericash.apis.integrationGetMiniStatement;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ExternalTransactionInformation;
import com.cit.vericash.api.components.transactionhistorymodel.ExternalTransactionInformationDto;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.model.GeneralLookupsData;
import com.cit.vericash.apis.commons.util.CachableGeneralLookups;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class IntegrationGetMiniStatementResponseTransformer implements MuleResponseTransformer {
    private final String monthsize = "2020_Full_Statement_Months_Size";
    @Autowired
    CachableGeneralLookups CachableGeneralLookups;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        ObjectMapper mapper = new ObjectMapper();
        ExternalTransactionInformationDto externalTransactionInformationDto;

        List<ExternalTransactionInformation> bankStatement = businessMessage.getExternalTransactionInformations();

        List<ExternalTransactionInformationDto> bankStatementResult = new ArrayList<>();
        for (ExternalTransactionInformation bankStatmentObj : bankStatement) {
            externalTransactionInformationDto = new ExternalTransactionInformationDto();
            externalTransactionInformationDto.setBalance(bankStatmentObj.getBalance());
            externalTransactionInformationDto.setNarration(bankStatmentObj.getNarration());
            externalTransactionInformationDto.setTranAmt(bankStatmentObj.getTranAmt());
            externalTransactionInformationDto.setTranDate(bankStatmentObj.getTranDate());
            externalTransactionInformationDto.setTranId(bankStatmentObj.getTranId());
            externalTransactionInformationDto.setTranSubtype(bankStatmentObj.getTranSubtype());
            externalTransactionInformationDto.setDatePosted(bankStatmentObj.getDatePosted());
            externalTransactionInformationDto.setTranSNum(bankStatmentObj.getTranSNum());
            externalTransactionInformationDto.setDisputeTransaction(bankStatmentObj.isDisputeTransaction());
            externalTransactionInformationDto.setMobileBanking(bankStatmentObj.isDisputeTransaction());
            externalTransactionInformationDto.setActivityType(bankStatmentObj.getActivityType());
            externalTransactionInformationDto.setDatePostedTimeStamp(bankStatmentObj.getDatePostedTimeStamp());
            externalTransactionInformationDto.setCurrency(bankStatmentObj.getCurrency());
            bankStatementResult.add(externalTransactionInformationDto);
        }

//        bankStatement.forEach(name -> {
//            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
//        });

        result.put("Bank Statement", bankStatementResult);
        GeneralLookupsData generalLookupsData = CachableGeneralLookups.getGeneralLookup(monthsize);

        if (generalLookupsData == null) {
            throw new APIException("Unable to load Months Size", ErrorCode.UNABLE_TO_LOAD_Full_Statement_Months_Size.getErrorCode());
        }
        result.put("Month_Size", Integer.parseInt(generalLookupsData.getLookupvalue()));
        return result;
    }
}