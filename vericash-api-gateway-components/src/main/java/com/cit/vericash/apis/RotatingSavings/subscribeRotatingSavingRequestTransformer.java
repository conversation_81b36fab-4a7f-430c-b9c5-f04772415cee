package com.cit.vericash.apis.RotatingSavings;


import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.subscribeRotatingSavingRequestTransformer")
public class subscribeRotatingSavingRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        return;
    }
}
