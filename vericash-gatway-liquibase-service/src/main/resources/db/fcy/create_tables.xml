<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1" author="Amir">
        <preConditions onFail="MARK_RAN">
            <!-- Check if the table exists -->
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM all_tables WHERE OWNER = '${DB_USERNAME}' AND table_name = 'CURRENCY_CONFIG'
            </sqlCheck>
        </preConditions>

        <!-- Create the table if it does not exist -->
        <createTable tableName="CURRENCY_CONFIG">
            <column name="CURRENCY_ID" type="NUMBER(3)">
                <constraints primaryKey="true"/>
            </column>
            <column name="STATUS" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="BASE" type="BOOLEAN" defaultValueBoolean="false"/>
        </createTable>
    </changeSet>


<!--     ChangeSet to create CURRENCY_RATES table -->
    <changeSet id="2" author="Amir Nabil">
        <preConditions onFail="MARK_RAN">
            <!-- Check if the table exists -->
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM all_tables WHERE OWNER = '${tableOwner}' AND table_name = 'CURRENCY_RATES'
            </sqlCheck>
        </preConditions>

        <createTable tableName="CURRENCY_RATES">
        <column name="CURRENCY_ID" type="NUMBER" defaultValueNumeric="0"/>
        <column name="BASE_CURRENCY_ID" type="NUMBER" defaultValueNumeric="0"/>
        <column name="BUY" type="NUMBER" defaultValueNumeric="0"/>
        <column name="SELL" type="NUMBER" defaultValueNumeric="0"/>
        <column name="LAST_UPDATE" type="TIMESTAMP" defaultValueDate="CURRENT_TIMESTAMP"/>
    </createTable>
        <addForeignKeyConstraint baseColumnNames="CURRENCY_ID"
                                 baseTableName="CURRENCY"
                                 constraintName="fk_currency_rates_currency_id"
                                 referencedColumnNames="ID"
                                 referencedTableName="CURRENCY"/>

        <addForeignKeyConstraint baseColumnNames="BASE_CURRENCY_ID"
                                 baseTableName="CURRENCY"
                                 constraintName="fk_currency_rates_base_currency_id"
                                 referencedColumnNames="ID"
                                 referencedTableName="CURRENCY"/>
    </changeSet>

    <changeSet id="3" author="Amir Nabil">
        <preConditions onFail="MARK_RAN">
            <!-- Check if the table exists -->
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM all_tables WHERE OWNER = '${tableOwner}' AND table_name = 'FCY_CUSTOMER_FAVORITES'
            </sqlCheck>
        </preConditions>


        <createTable tableName="FCY_CUSTOMER_FAVORITES">
            <column name="ID" type="NUMBER">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="CUSTOMER_ID" type="NUMBER(20)">
                <constraints nullable="false"/>
            </column>
            <column name="CURRENCY_ID" type="NUMBER(3)">
                <constraints nullable="false" unique="true"/>
            </column>
        </createTable>

        <addForeignKeyConstraint baseTableName="FCY_CUSTOMER_FAVORITES"
                                 baseColumnNames="CURRENCY_ID"
                                 constraintName="fk_customer_favorites_currency_id"
                                 referencedTableName="CURRENCY"
                                 referencedColumnNames="ID"/>
    </changeSet>


    <changeSet id="checkSequenceExistence" author="Amir Nabil">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM all_sequences WHERE SEQUENCE_OWNER = '${tableOwner}' AND sequence_name = 'FCY_CUSTOMER_FAVORITES_SEQ'
            </sqlCheck>
        </preConditions>

        <!-- Create the sequence if it does not exist -->
        <createSequence sequenceName="FCY_CUSTOMER_FAVORITES_SEQ" incrementBy="1" startValue="1"/>
    </changeSet>


    <changeSet id="checkTriggerExistence" author="your_author">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*) FROM all_triggers WHERE trigger_name = 'TRG_FCY_CUSTOMER_FAVORITES'
            </sqlCheck>
        </preConditions>

        <!-- Add your trigger creation logic here if it does not exist -->
        <sql>
            CREATE TRIGGER FCY_CUSTOMER_FAVORITES_TRIG
                BEFORE INSERT ON FCY_CUSTOMER_FAVORITES
                FOR EACH ROW
            BEGIN
                SELECT
                    FCY_CUSTOMER_FAVORITES_SEQ.NEXTVAL
                INTO
                    :new
                    .ID
                FROM
                    dual
                ;
            END;
        </sql>
    </changeSet>


</databaseChangeLog>