package com.cit.vericash.staticmessagetomessage.transformer;

import com.cit.vericash.api.transformerfactory.RequestTransformer;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicinputparameters.DynamicRequestModelMapperInitializerImpl;
import com.cit.vericash.backend.commons.dynamicmodelmapper.MapWrapper;
import com.cit.vericash.dynamicrequest.transformer.DynamicObjectTypeEnum;
import org.apache.commons.jxpath.JXPathContext;
import org.springframework.beans.factory.annotation.Autowired;
import com.cit.vericash.apis.commons.util.*;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.cit.vericash.backend.commons.dynamicmodelmapper.DynamicModelMapper;
import com.cit.vericash.backend.commons.dynamicmodelmapper.Field;
import com.cit.vericash.backend.commons.dynamicmodelmapper.ObjectSchema;

@Service
public class StaticMessageToMessageTransformerImpl implements RequestTransformer {

    @Autowired
    DynamicRequestModelMapperInitializerImpl dynamicRequestModelMapperInitializer;


    @Autowired
    private CachableVericashApi vericashApiCache;


    @Override
    public Message transform(Message message) throws Exception {
        Message targetMessage = message;
        String apiCode = message.getHeader().getAttributeAsString("apiCode");
        VericashAPI vericashAPI = vericashApiCache.getVericashAPI(Long.valueOf(apiCode));

        DynamicModelMapper modelMapper = dynamicRequestModelMapperInitializer.getModelMapper(apiCode,vericashAPI.getRequestType());
        if (modelMapper == null)
            return message;
        List<ObjectSchema> objectSchemas = modelMapper.getObjectSchemas();

        String dtoClassPath = vericashAPI.getDtoClassPath();
//        targetMessage = new Message();
//        if(dto != null){
        if (dtoClassPath != null) {
            ObjectSchema objectSchema = new ObjectSchema();
            String[] classPathSplited = dtoClassPath.split("[.]", 0);
            String string = classPathSplited[classPathSplited.length - 1];
            string = Character.toLowerCase(string.charAt(0)) + string.substring(1);
            targetMessage.getPayload().put("dtoName", string);
            objectSchema.setObject(string);
            objectSchema.setType("1");
            objectSchemas.add(objectSchema);
        }
        if (objectSchemas != null) {
            initObjects(objectSchemas, targetMessage);
        }
        JXPathContext sourceObject = JXPathContext.newContext(message);
        mapValuesToObjects(modelMapper,sourceObject,targetMessage);
        return targetMessage;
    }

    @Override
    public void mapValuesToObjects(DynamicModelMapper modelMapper, JXPathContext sourceObject, Message targetMessage) {
        List<Field> mapperFields = modelMapper.getFields();
        List<MapWrapper> maps = modelMapper.getMaps();
        JXPathContext targetObject = JXPathContext.newContext(targetMessage);
        String path = "payload/" + targetMessage.getPayload().getAttributeAsString("dtoName");

        Map<String, Object> container = new HashMap<>();
        if (maps != null && !maps.isEmpty()) {
            for (MapWrapper map : maps) {
                String keySource = map.getKey();
                String sourceField = map.getValue();
                Object key = sourceObject.getValue(keySource);
                Object value = sourceObject.getValue(sourceField);
                container.put(key.toString(), value);
                String targetField = path + map.getTarget();
                targetObject.setValue(targetField, container);
            }
        }
        if (mapperFields != null) {
            for (Field field : mapperFields) {
                String sourceField = field.getSource();
                String targetField = field.getTarget();
                if (sourceField.contains("payload/") && !path.equals("")) {
                    targetField = path + field.getTarget();
                }

                try {
                    Object sourceValue = sourceObject.getValue(sourceField);
                    targetObject.setValue(targetField, sourceValue);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }


            }

        }

    }

    private void initObjects(List<ObjectSchema> objectSchemas, Message targetMessage) {
        for (ObjectSchema objectSchema : objectSchemas) {
            String objectPath = objectSchema.getObject();
            String type = objectSchema.getType();
            checkTargetObjAndInit(objectPath, targetMessage, DynamicObjectTypeEnum.getObjectType(type));
        }
    }
    private void checkTargetObjAndInit(String targetPath, Message message, Object type) {

        Map<String, Object> targetMap = message.getPayload();
        String[] splitPath = targetPath.split("/");
        for (String object : splitPath) {
            if (object.isEmpty() || object.equals("payload")) {
                continue;

            }
            if (targetMap.containsKey(object)) {
                targetMap = (Map<String, Object>) targetMap.get(object);

            } else {
                targetMap.put(object, type);

            }
        }
    }
}
