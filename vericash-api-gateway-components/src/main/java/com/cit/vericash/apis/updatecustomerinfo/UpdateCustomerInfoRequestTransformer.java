package com.cit.vericash.apis.updatecustomerinfo;

import com.cit.mpaymentapp.common.message.Gender;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.EnumTransformer;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.updatecustomerinfo.UpdateCustomerInfoRequestTransformer")
public class UpdateCustomerInfoRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    private final String GENDER = "gender";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

        try {
//			cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();

            String customerGnderKey = cacheableFieldParameters.getRequestParameters(GENDER);
            String customerGnder = message.getPayload().getAttributeAsString(customerGnderKey);


            Gender genderEnum = EnumTransformer.convertGender(customerGnder);
            if (genderEnum == null) {
                throw new APIException("customer gender is invalid", ErrorCode.INVALID_CUSTOMER_GENDER.getErrorCode());
            }
            message.getPayload().put(customerGnderKey, genderEnum);


        } catch (Exception e) {
            throw e;
        }

    }
}
