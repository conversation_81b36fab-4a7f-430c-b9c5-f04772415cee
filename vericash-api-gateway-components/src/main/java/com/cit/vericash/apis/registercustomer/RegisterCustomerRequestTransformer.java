package com.cit.vericash.apis.registercustomer;

import com.cit.mpaymentapp.common.message.Gender;
import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.CacheableWalletInfo;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.registercustomer.RegisterCustomerRequestTransformer")
public class RegisterCustomerRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String SENDER_OWNERTYPE = "sender-OwnerType";
    private final String RECIEVER_OWNERTYPE = "reciever-OwnerType";
    private final String WALLET_SHORT_CODE = "wallet-Short-Code";
    private final String CURRENCY = "currency";
    private final String CONTACT_US = "contact-US";
    private final String GENDER = "gender";
    private final String COUNTRY_ISO2 = "country-iso2";
    @Autowired
    private CacheableWalletInfo walletInfoCache;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;


//	public RegisterCustomerRequestTransformer() {
//		walletInfoCache = new CacheableWalletInfoImpl();
//
//	}

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
        String walletShortCodeKey = cacheableFieldParameters.getRequestParameters(WALLET_SHORT_CODE);

        String walletShortCode = message.getHeader().getAttributeAsString(walletShortCodeKey);
        WalletInfo walletInfo = walletInfoCache.getWalletInfo(walletShortCode);
        try {
            String senderOwnerTypeKey = cacheableFieldParameters.getRequestParameters(SENDER_OWNERTYPE);
            String receiverOwnerTypeKey = cacheableFieldParameters.getRequestParameters(RECIEVER_OWNERTYPE);
            String ownerTypeString = message.getPayload().getAttributeAsString(senderOwnerTypeKey);
            String receiverOwnerTypeString = message.getPayload().getAttributeAsString(receiverOwnerTypeKey);
            //	OwnerTypeEnum senderOwnerType = converOwnerType(ownerTypeString);
            //OwnerTypeEnum receiverOwnerType = converOwnerType(receiverOwnerTypeString);
            String genderKey = cacheableFieldParameters.getRequestParameters(GENDER);

            String gender = message.getPayload().getAttributeAsString(genderKey);
            Gender genderEnum = convertGender(gender);

            message.getPayload().put(cacheableFieldParameters.getRequestParameters(CURRENCY), walletInfo.getCurrency());
            message.getPayload().put(cacheableFieldParameters.getRequestParameters(CONTACT_US), walletInfo.getContactUS());
            message.getPayload().put(cacheableFieldParameters.getRequestParameters(COUNTRY_ISO2), walletInfo.getCountryIso2());
            message.getPayload().put(genderKey, genderEnum);
            //	message.getPayload().put(senderOwnerTypeKey, senderOwnerType);
            //message.getPayload().put(receiverOwnerTypeKey, receiverOwnerType);


        } catch (Exception e) {
            throw e;
        }
    }
	
	
	/*public OwnerTypeEnum converOwnerType(String ownerTypeString){
		OwnerTypeEnum ownerType = OwnerTypeEnum.None;


		if (!StringUtils.isBlank(ownerTypeString)) {

			switch (Integer.valueOf(ownerTypeString)) {
			case 0: {
				ownerType = OwnerTypeEnum.None;
				break;
			}
			case 1: {
				ownerType = OwnerTypeEnum.CUSTOMER;
				break;
			}
			case 2:
			case 3:
			case 4:
			case 5:
			case 6: {
				ownerType = OwnerTypeEnum.BUSINESSENTITY;
				break;
			}
			default: {
				ownerType = OwnerTypeEnum.None;
				break;
			}
			}
		}
			
			return ownerType;
	}*/

    public Gender convertGender(String genderString) {
        Gender genderEnum = null;
        if (!StringUtils.isBlank(genderString)) {
            switch (Integer.valueOf(genderString)) {
                case 0: {
                    genderEnum = Gender.Male;
                    break;
                }
                case 1: {
                    genderEnum = Gender.Female;
                    break;
                }

            }
        }
        return genderEnum;
    }


}
