package com.cit.vericash.apis.gettopupoperators;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.gettopupoperators.GetTopUpResponseTransformer")
public class GetTopUpResponseTransformer implements MuleResponseTransformer {
    public Object transform(BusinessMessage businessMessage) throws Exception {
        JSONParser parser = new JSONParser();
        JSONObject json = new JSONObject();
        try {

            if (businessMessage.getSoftFields().containsKey("BillersList")) {
                json = (JSONObject) parser.parse((String) businessMessage.getSoftFields().get("BillersList"));
            }
        } catch (Exception e) {
            throw e;
        }

        return json;
    }
}