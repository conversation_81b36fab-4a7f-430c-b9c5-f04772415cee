package com.cit.vericash.apis.agentministatement;

import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.DateUtil;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Qualifier("com.cit.vericash.apis.agentministatement.AgentGetMiniStatementRequestTransformer")
public class AgentGetMiniStatementRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        try {
//			cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
            //String ownerTypeKey = cacheableFieldParameters.getRequestParameters(OWNERTYPE);
            //	String ownerTypeString = message.getPayload().getAttributeAsString(ownerTypeKey);
            //String transactionInfoKey = cacheableFieldParameters.getRequestParameters("transactionInfo");
            //String from = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters("form"));
            //String to = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters("to"));

            //String ownerTypeString = message.getPayload().getAttributeAsString("senderOwnerType");
            String from = message.getPayload().getAttributeAsString("from");
            String to = message.getPayload().getAttributeAsString("to");
            boolean vFrom = DateUtil.validateDate(from);
            boolean vTo = DateUtil.validateDate(to);
            TransactionInformation transactionInformation = null;
            if (vTo && vFrom) {
                Date fromDate = DateUtil.transferDate(from);
                Date toDate = DateUtil.transferDate(to);

                transactionInformation = new TransactionInformation();
                transactionInformation.setDateFrom(fromDate);
                transactionInformation.setDateTo(toDate);

                message.getPayload().put("transactionInfo", transactionInformation);
            } else {
                transactionInformation = new TransactionInformation();
                message.getPayload().put("transactionInfo", transactionInformation);
            }


        } catch (Exception e) {
            throw e;
        }
    }


}
