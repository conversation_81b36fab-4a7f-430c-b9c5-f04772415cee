Process=SELECT ACTIVITY_TRAIL.USER_ID,ACTIVITY_TRAIL.ID,ACTIVITY_TRAIL.TASK_DATE,SERVICE_CONFIG_MAP.SERVICE_TYPE_NAME \
  FROM ACTIVITY_TRAIL \
  INNER JOIN SERVICE_CONFIG_MAP ON SERVICE_CONFIG_MAP.SERVICE_TYPE_ID = ACTIVITY_TRAIL.SERVICE_ID \
  WHERE ACTIVITY_TRAIL.STATUS = 0 \
  AND TRUNC(ACTIVITY_TRAIL.TASK_DATE + ?)= TRUNC(SYSDATE) \
  GROUP BY ACTIVITY_TRAIL.USER_ID,ACTIVITY_TRAIL. ID,ACTIVITY_TRAIL.TASK_DATE,SERVICE_CONFIG_MAP.SERVICE_TYPE_NAME
Process_2=UPDATE ACTIVITY_TRAIL SET STATUS = 3,ACTION = 'Expired' WHERE ID = ?
