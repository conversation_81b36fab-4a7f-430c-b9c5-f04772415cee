<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="insert_api_error_forPaymentMethodType_asSRS" author="<PERSON>">
        <insert tableName="API_ERROR">
            <column name="ERROR_CODE" value="VAL202320" />
            <column name="ERROR_DESCRIPTION" value="Costumer default receiver payment method is not supported; costumer must set his default receiver payment method as a wallet payment method" />
            <column name="ERROR_DESCRIPTION_FR" value="Costumer default receiver payment method is not supported; costumer must set his default receiver payment method as a wallet payment method" />
            <column name="ERROR_DESCRIPTION_PT" value="Costumer default receiver payment method is not supported; costumer must set his default receiver payment method as a wallet payment method" />
            <column name="ERROR_DESCRIPTION_ES" value="Costumer default receiver payment method is not supported; costumer must set his default receiver payment method as a wallet payment method" />
        </insert>
    </changeSet>

    <changeSet id="insert_servicesPaymentMethod_for_cashin" author="Abdullah Khamis" failOnError="false">
        <insert tableName="SERVICES_PAYMENTMETHOD">
            <column name="ID" valueComputed="(SELECT MAX(ID) + 1 FROM SERVICES_PAYMENTMETHOD)"/>
            <column name="API_CODE" value="333"/>
            <column name="PAYMENMETHODTYPE" value="871"/>
            <column name="LOOKUP_ENUM" value="4"/>
            <column name="GAR_STATUS" value="1"/>
            <column name="USER_TYPE" value="2"/>
            <column name="VAILDATE_PM" value="0"/>
            <column name="REQUIRED_DATA" value="NULL"/>
        </insert>
    </changeSet>
</databaseChangeLog>
