package com.cit.vericash.apis.getministatement;

import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Qualifier("com.cit.vericash.apis.getministatement.GetMiniStatementRequestTransformer")
public class GetMiniStatementRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    private final String FROM_KEY = "from";
    private final String TRX_INFO_KEY = "transactionInfo";
    private final String TO_KEY = "to";
    private final String PAGENUMBER = "page_Number";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        try {
//			cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
            //String transactionInfoKey = cacheableFieldParameters.getRequestParameters("transactionInfo");
            //String from = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters("form"));
            //String to = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters("to"));

            //String ownerTypeString = message.getPayload().getAttributeAsString("senderOwnerType");
            String from = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(FROM_KEY));
            String to = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(TO_KEY));
            Integer pageNumber = message.getPayload().getAttributeAsInteger(cacheableFieldParameters.getRequestParameters(PAGENUMBER));

            boolean vFrom = com.cit.vericash.apis.commons.util.DateUtil.validateDate(from);
            boolean vTo = com.cit.vericash.apis.commons.util.DateUtil.validateDate(to);
            TransactionInformation transactionInformation = new TransactionInformation();
            ;

            if (vTo && vFrom) {
                Date fromDate = com.cit.vericash.apis.commons.util.DateUtil.transferDate(from);
                Date toDate = com.cit.vericash.apis.commons.util.DateUtil.transferDate(to);


                transactionInformation.setDateFrom(fromDate);
                transactionInformation.setDateTo(toDate);
//				transactionInformation.setPageNumber(pageNumber);

                message.getPayload().put(cacheableFieldParameters.getRequestParameters(TRX_INFO_KEY), transactionInformation);
            } else {
                message.getPayload().put(cacheableFieldParameters.getRequestParameters(TRX_INFO_KEY), transactionInformation);

            }


        } catch (Exception e) {
            throw e;
        }
    }

//	public boolean validateDate(String strDate) {
//		/* Check if date is 'null' */
//		if ( strDate == null ||strDate.trim().equals("")) {
//			return false;
//		}
//		/* Date is not 'null' */
//		else {
//			/*
//			 * Set preferred date format, For example MM-dd-yyyy, MM.dd.yyyy,dd.MM.yyyy etc.
//			 */
//			SimpleDateFormat sdfrmt = new SimpleDateFormat("dd/MM/yyyy");
//			sdfrmt.setLenient(false);
//			/*
//			 * Create Date object parse the string into date
//			 */
//			try {
//				Date javaDate = sdfrmt.parse(strDate);
//				System.out.println(strDate + " is valid date format");
//			}
//			/* Date format is invalid */
//			catch (ParseException e) {
//				System.out.println(strDate + " is Invalid Date format");
//				return false;
//			}
//			/* Return true if date format is valid */
//			return true;
//		}
//	}

}
