package com.cit.vericash.apis.initiatecashoutbycustomer;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.initiatecashoutbycustomer.InitiateCashOutByCustomerResponseTransformer")
public class InitiateCashOutByCustomerResponseTransformer implements MuleResponseTransformer {
    private final String trxAmountKey = "Transaction-Amount";
    private final String totalFeeAmountKey = "Total-Fee-Amount";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();

        Map<String, Object> result = new HashMap<String, Object>();

        if (businessMessage != null && businessMessage.getTransactionInfo() != null) {

            //	result.put("transactionExecutionSummaryID", businessMessage.getTransactionInfo().getTransactionId());
            result.put(cacheableFieldParameters.getResponseParameters(trxAmountKey), businessMessage.getTransactionInfo().getTransactionAmount());
            result.put(cacheableFieldParameters.getResponseParameters(totalFeeAmountKey), (businessMessage.getTransactionInfo().getTransactionFeeAmount() == null) ? 0 : businessMessage.getTransactionInfo().getTransactionFeeAmount());
        }


        return result;
    }

}