package com.cit.vericash.apis.appRating;

import com.cit.vericash.api.entity.appRating.*;
import com.cit.vericash.api.repository.appRating.AppRatingChannelRepository;
import com.cit.vericash.api.repository.appRating.AppRatingConfigurationRepository;
import com.cit.vericash.api.repository.appRating.AppRatingResponseRepository;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class AppRatingTest {

    private AppRating appRating;
    private AppRatingResponseRepository mockResponseRepo;
    private AppRatingConfigurationRepository mockConfigRepo;
    private AppRatingChannelRepository mockChannelRepo;

    @Before
    public void setUp() {
        appRating = new AppRating();
        mockResponseRepo = mock(AppRatingResponseRepository.class);
        mockConfigRepo = mock(AppRatingConfigurationRepository.class);
        mockChannelRepo = mock(AppRatingChannelRepository.class);

        ReflectionTestUtils.setField(appRating, "appRatingResponseRepository", mockResponseRepo);
        ReflectionTestUtils.setField(appRating, "appRatingConfigurationRepository", mockConfigRepo);
        ReflectionTestUtils.setField(appRating, "appRatingChannelRepository", mockChannelRepo);
    }

    private Message buildMessage(Payload payloadMap) {
        Message message = new Message();
        DynamicPayload dynamicPayload = new DynamicPayload();
        Header header = new Header();
        header.put("apiCode", 123L);
        dynamicPayload.setHeader(header);
        dynamicPayload.setPayload(payloadMap);
        message.setDynamicPayload(dynamicPayload);
        return message;
    }

    @Test
    public void test_save_success_all_fields() throws Exception {
        AppRatingConfiguration config = new AppRatingConfiguration(); config.setId(1L);
        Channel channel = new Channel(); channel.setId(2L);

        when(mockConfigRepo.findById(1L)).thenReturn(Optional.of(config));
        when(mockChannelRepo.findById(2L)).thenReturn(Optional.of(channel));

        Payload payload = new Payload();
        payload.put("122682988", Map.of("parameterValue", "1"));
        payload.put("101", Map.of("parameterValue", "123"));
        payload.put("122682991", Map.of("parameterValue", "5"));
        payload.put("122682990", Map.of("parameterValue", "Great app!"));
        payload.put("122682989", Map.of("parameterValue", "2"));

        List<Map<String, Object>> questionResponses = List.of(
                Map.of("question_id", 1L, "freeResponse", "text", "answer_options", List.of(1,2))
        );
        payload.put("122682986", Map.of("parameterValue", questionResponses));

        Message message = buildMessage(payload);

        appRating.saveRatingResponse(message);

        ArgumentCaptor<AppRatingResponse> captor = ArgumentCaptor.forClass(AppRatingResponse.class);
        verify(mockResponseRepo).save(captor.capture());
        AppRatingResponse resp = captor.getValue();
        assertEquals(Integer.valueOf(5), resp.getRatingValue());
        assertEquals("Great app!", resp.getFeedbackMessage());
        assertEquals(config, resp.getConfiguration());
        assertEquals(channel, resp.getChannel());
        assertEquals(1, resp.getQuestionResponses().size());
        assertEquals(2, resp.getQuestionResponses().get(0).getAnswers().size());
    }

    @Test
    public void test_save_success_text_only_question() throws Exception {
        AppRatingConfiguration config = new AppRatingConfiguration(); config.setId(1L);
        Channel channel = new Channel(); channel.setId(2L);

        when(mockConfigRepo.findById(1L)).thenReturn(Optional.of(config));
        when(mockChannelRepo.findById(2L)).thenReturn(Optional.of(channel));

        Payload payload = new Payload();
        payload.put("122682988", Map.of("parameterValue", "1"));
        payload.put("101", Map.of("parameterValue", "123"));
        payload.put("122682991", Map.of("parameterValue", "4"));
        payload.put("122682990", Map.of("parameterValue", "Nice!"));
        payload.put("122682989", Map.of("parameterValue", "2"));

        List<Map<String, Object>> questionResponses = List.of(
                Map.of("question_id", 2L, "freeResponse", "only text", "answer_options", List.of())
        );
        payload.put("122682986", Map.of("parameterValue", questionResponses));

        Message message = buildMessage(payload);

        appRating.saveRatingResponse(message);

        ArgumentCaptor<AppRatingResponse> captor = ArgumentCaptor.forClass(AppRatingResponse.class);
        verify(mockResponseRepo).save(captor.capture());
        AppRatingResponse resp = captor.getValue();
        assertEquals(1, resp.getQuestionResponses().size());
        assertEquals(1, resp.getQuestionResponses().get(0).getAnswers().size());
        assertEquals("only text", resp.getQuestionResponses().get(0).getAnswers().get(0).getFreeResponse());
    }

    @Test
    public void test_save_success_no_questions() throws Exception {
        AppRatingConfiguration config = new AppRatingConfiguration(); config.setId(1L);
        Channel channel = new Channel(); channel.setId(2L);

        when(mockConfigRepo.findById(1L)).thenReturn(Optional.of(config));
        when(mockChannelRepo.findById(2L)).thenReturn(Optional.of(channel));

        Payload payload = new Payload();
        payload.put("122682988", Map.of("parameterValue", "1"));
        payload.put("101", Map.of("parameterValue", "123"));
        payload.put("122682991", Map.of("parameterValue", "3"));
        payload.put("122682990", Map.of("parameterValue", "ok"));
        payload.put("122682989", Map.of("parameterValue", "2"));
        // No question responses

        Message message = buildMessage(payload);

        appRating.saveRatingResponse(message);

        ArgumentCaptor<AppRatingResponse> captor = ArgumentCaptor.forClass(AppRatingResponse.class);
        verify(mockResponseRepo).save(captor.capture());
        AppRatingResponse resp = captor.getValue();
        assertTrue(resp.getQuestionResponses().isEmpty());
    }

    @Test
    public void test_save_missing_required_param() {
        Payload payload = new Payload();
        payload.put("122682990", Map.of("parameterValue", "Great app!")); // Only feedback

        Message message = buildMessage(payload);

        APIException ex = assertThrows(APIException.class, () -> appRating.saveRatingResponse(message));
        assertEquals(ErrorCode.REQUIRED_PARAMETER_MISSING.getErrorCode(), ex.getErrorCode());
        verify(mockResponseRepo, never()).save(any());
    }

    @Test
    public void test_save_invalid_question_response_structure() {
        AppRatingConfiguration config = new AppRatingConfiguration(); config.setId(1L);
        Channel channel = new Channel(); channel.setId(2L);

        when(mockConfigRepo.findById(1L)).thenReturn(Optional.of(config));
        when(mockChannelRepo.findById(2L)).thenReturn(Optional.of(channel));

        Payload payload = new Payload();
        payload.put("122682988", Map.of("parameterValue", "1"));
        payload.put("101", Map.of("parameterValue", "123"));
        payload.put("122682991", Map.of("parameterValue", "5"));
        payload.put("122682990", Map.of("parameterValue", "Great app!"));
        payload.put("122682989", Map.of("parameterValue", "2"));
        payload.put("122682986", Map.of("parameterValue", new AppRatingQuestionResponse()));

        Message message = buildMessage(payload);

        APIException ex = assertThrows(APIException.class, () -> appRating.saveRatingResponse(message));
        assertEquals(ErrorCode.QUESTION_RESPONSES_NOT_TYPE_OF_LIST.getErrorCode(), ex.getErrorCode());
    }

    @Test
    public void test_save_config_not_found() {
        when(mockConfigRepo.findById(1L)).thenReturn(Optional.empty());
        when(mockChannelRepo.findById(2L)).thenReturn(Optional.of(new Channel()));

        Payload payload = new Payload();
        payload.put("122682988", Map.of("parameterValue", "1"));
        payload.put("101", Map.of("parameterValue", "123"));
        payload.put("122682991", Map.of("parameterValue", "5"));
        payload.put("122682990", Map.of("parameterValue", "Great app!"));
        payload.put("122682989", Map.of("parameterValue", "2"));

        Message message = buildMessage(payload);

        APIException ex = assertThrows(APIException.class, () -> appRating.saveRatingResponse(message));
        assertEquals(ErrorCode.APP_RATING_CONFIG_NOT_FOUND.getErrorCode(), ex.getErrorCode());
    }

    @Test
    public void test_save_channel_not_found() {
        AppRatingConfiguration config = new AppRatingConfiguration(); config.setId(1L);
        when(mockConfigRepo.findById(1L)).thenReturn(Optional.of(config));
        when(mockChannelRepo.findById(2L)).thenReturn(Optional.empty());

        Payload payload = new Payload();
        payload.put("122682988", Map.of("parameterValue", "1"));
        payload.put("101", Map.of("parameterValue", "123"));
        payload.put("122682991", Map.of("parameterValue", "5"));
        payload.put("122682990", Map.of("parameterValue", "Great app!"));
        payload.put("122682989", Map.of("parameterValue", "2"));

        Message message = buildMessage(payload);

        APIException ex = assertThrows(APIException.class, () -> appRating.saveRatingResponse(message));
        assertEquals(ErrorCode.APP_RATING_CHANNEL_NOT_FOUND.getErrorCode(), ex.getErrorCode());
    }

    @Test
    public void test_save_invalid_numeric_value() {
        AppRatingConfiguration config = new AppRatingConfiguration(); config.setId(1L);
        Channel channel = new Channel(); channel.setId(2L);

        when(mockConfigRepo.findById(1L)).thenReturn(Optional.of(config));
        when(mockChannelRepo.findById(2L)).thenReturn(Optional.of(channel));

        Payload payload = new Payload();
        payload.put("122682988", Map.of("parameterValue", "notANumber"));
        payload.put("101", Map.of("parameterValue", "123"));
        payload.put("122682991", Map.of("parameterValue", "5"));
        payload.put("122682990", Map.of("parameterValue", "Great app!"));
        payload.put("122682989", Map.of("parameterValue", "2"));

        Message message = buildMessage(payload);

        assertThrows(IllegalArgumentException.class, () -> appRating.saveRatingResponse(message));
    }

    @Test
    public void test_save_invalid_payload_structure() {
        AppRatingConfiguration config = new AppRatingConfiguration(); config.setId(1L);
        Channel channel = new Channel(); channel.setId(2L);

        when(mockConfigRepo.findById(1L)).thenReturn(Optional.of(config));
        when(mockChannelRepo.findById(2L)).thenReturn(Optional.of(channel));

        Payload payload = new Payload();
        payload.put("122682988", "notAMap");
        payload.put("101", Map.of("parameterValue", "123"));
        payload.put("122682991", Map.of("parameterValue", "5"));
        payload.put("122682990", Map.of("parameterValue", "Great app!"));
        payload.put("122682989", Map.of("parameterValue", "2"));

        Message message = buildMessage(payload);

        APIException ex = assertThrows(APIException.class, () -> appRating.saveRatingResponse(message));
        assertEquals(ErrorCode.INVALID_PARAMETER_STRUCTURE.getErrorCode(), ex.getErrorCode());
    }
}