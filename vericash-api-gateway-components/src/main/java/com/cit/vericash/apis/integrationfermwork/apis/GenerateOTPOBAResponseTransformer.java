package com.cit.vericash.apis.integrationfermwork.apis;

import com.cit.vericash.apis.commons.api.IntegrationResponseTransformer;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.integrationfermwork.apis.GenerateOTPOBAResponseTransformer")

public class GenerateOTPOBAResponseTransformer implements IntegrationResponseTransformer {
    @Override
    public Object transform(Payload payload) throws Exception {
        Map<String, Object> result = new HashedMap();

        if(payload.get("response")!=null)
        {

            Map integ_response=(HashMap<String ,Object>) payload.get("response");

            result.put("otp", integ_response.get("otp"));
        }
        return result ;

    }

}
