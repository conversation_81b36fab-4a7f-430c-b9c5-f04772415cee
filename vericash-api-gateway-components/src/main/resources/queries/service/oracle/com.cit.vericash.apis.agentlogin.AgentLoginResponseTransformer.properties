AgentLoginResponseTransformer=SELECT h1.BUSINESS_ENTITY_NAME AS BUSINESS_ENTITY_NAME,c.COUNTRY AS COUNTRY,\
  h1.CORPORATE_NAME AS CORPORATE_NAME, h2.BUSINESS_ENTITY_NAME AS parent_Business_Entity, \
  h1.REGISTRATION_NUMBER AS REGISTRATION_NUMBER, \
  IDENTIFICATION_KEY,PROFILE_NAME,MOBILE_PHONE,c.ISO_Code,c.COUNTRY_Code,\
  sme.MAIN_CUSTOMER_ID As Parent_Customer_Id,sme.CORPERATE_ID,sme.BE_USER_TYPE_ID, \
  sme.PARENT_CUSTOMER_ID AS CUSTOMER_ID \
  FROM Business_Users bu LEFT JOIN BUSINESS_ENTITY_HIERARCHY h1 \
  ON h1.BUSINESS_ENTITY_ID = bu.BUSINESS_ENTITY_ID \
  LEFT JOIN COUNTRY c ON c.COUNTRY_ID = bu.COUNTRY \
  LEFT JOIN USER_PROFILES ON USER_PROFILES. ID = bu.USER_PROFILE_ID \
  LEFT JOIN BUSINESS_ENTITY_HIERARCHY h2 ON h2.BUSINESS_ENTITY_ID = h1.BUSINESS_ENTITY_ID \
  LEFT JOIN BE_CUSTOMER sme ON bu.BUSINESS_ENTITY_ID = sme.CORPERATE_ID \
  WHERE Username = ?