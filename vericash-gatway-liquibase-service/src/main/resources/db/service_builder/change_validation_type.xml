<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">



<!--    <changeSet id="Change validation type for apiCode 3005 to be mixed" author="<PERSON>">-->
<!--        <sql>-->
<!--            UPDATE API_VERICASH_APIS av-->
<!--            SET av.VALIDATION_TYPE = 2-->
<!--            WHERE av.code = 3005;-->
<!--        </sql>-->
<!--    </changeSet>-->

<!--    <changeSet id="Change validation type and request type for apiCode 2267 to be mixed" author="<PERSON>">-->
<!--        <sql>-->
<!--            UPDATE API_VERICASH_APIS av-->
<!--            SET av.VALIDATION_TYPE = 2,-->
<!--            av.REQUEST_TYPE = 1-->
<!--            WHERE av.code = 2267;-->
<!--        </sql>-->
<!--    </changeSet>-->

<!--    <changeSet id="add to api error table for customer id and recurrence id" author="Abdullah Khames">-->
<!--        <sql>-->
<!--            INSERT INTO API_ERROR VALUES ('VAL_025', 'customer id cannot be null');-->
<!--            INSERT INTO API_ERROR VALUES ('VAL_027', 'recurrence id cannot be null');-->

<!--        </sql>-->
<!--    </changeSet>-->
    <changeSet id="add to api error table for dynamic group" author="Abdullah Khames">
        <sql>
            INSERT INTO API_ERROR (ERROR_CODE,ERROR_DESCRIPTION) VALUES ('API_05551','dynamic group field is invalid it must be a list of dynamic group input parameters');
       </sql>
    </changeSet>
    <changeSet id="add to api error table for application name" author="Abdullah Khames">
        <sql>
            INSERT INTO API_ERROR (ERROR_CODE,ERROR_DESCRIPTION) VALUES ('VAL0182','Invalid Application name');
        </sql>
    </changeSet>

</databaseChangeLog>