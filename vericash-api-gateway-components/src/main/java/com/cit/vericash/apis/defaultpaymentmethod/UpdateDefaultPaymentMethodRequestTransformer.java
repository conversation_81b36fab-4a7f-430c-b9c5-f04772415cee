package com.cit.vericash.apis.defaultpaymentmethod;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.cit.mpaymentapp.common.paymentmethod.SourcePaymentMethodDTO;
import com.cit.mpaymentapp.model.customer.Customer;
import com.cit.mpaymentapp.model.customer.CustomerGar;
import com.cit.mpaymentapp.model.payment.SourcePaymentMethods;
import com.cit.shared.error.exception.GarException;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.defaultpaymentmethod.UpdateDefaultPaymentMethodRequestTransformer")
@RequiredArgsConstructor
public class UpdateDefaultPaymentMethodRequestTransformer implements VericashAction {

    private final ConnectionUtil connectionUtil;


    @Override
    public Object process(Request request) throws Exception {
        Message message = request.getMessage();
        setPaymentMethodDefaultSenderAndReceiver(message);
        return null;
    }
    private final ObjectMapper mapper = new ObjectMapper();
    private final List<Parameter> parameters = new ArrayList<>();

    public void setPaymentMethodDefaultSenderAndReceiver(Message message) throws GeneralFailureException {
        Map<String, Object> sourcePaymentMethod = (Map<String, Object>) message.getPayload().get("sourcePaymentMethod");
        SourcePaymentMethodDTO paymentMethodDTO = mapper.convertValue(sourcePaymentMethod, SourcePaymentMethodDTO.class);
        Long customerGarId = paymentMethodDTO.getPaymentMethodCode();
        if (paymentMethodDTO.getIsDefaultSenderAndReceiver() != null && paymentMethodDTO.getIsDefaultSenderAndReceiver()) {
            paymentMethodDTO.setIsDefaultSender(true);
            paymentMethodDTO.setIsDefaultReciever(true);
        }
        CustomerGar customerGar = getCustomerGar(customerGarId);
       if (customerGar == null) {
            throw new GarException(GarException.GAR_INVALID_DATA);
        }
        SourcePaymentMethods paymentMethod = getPaymentMethodById(Long.valueOf(sourcePaymentMethod.get("paymentMethodType").toString()));
        updateIsDefaultSenderAndReceiver(customerGar.getCustomer(), customerGar, paymentMethodDTO, paymentMethod);

    }

    public void updateIsDefaultSenderAndReceiver(Customer customer, CustomerGar customerGar,
                                                  SourcePaymentMethodDTO sourcePaymentMethodInRequest,
                                                  SourcePaymentMethods paymentMethodInSystem) throws GarException {

        Boolean isDefaultSenderGar = customerGar.getIsDefaultSender();
        Boolean isDefaultSenderRequest = sourcePaymentMethodInRequest.getIsDefaultSender();
        Boolean isDefaultReceiverGar = customerGar.getIsDefaultReciever();
        Boolean isDefaultReceiverRequest = sourcePaymentMethodInRequest.getIsDefaultReciever();
        // Checks if the user changes the value of the sender that comes from the request
        // by comparing the original value in the customerGar
        if (isDefaultSenderRequest == false && isDefaultReceiverRequest == false) {
            customerGar.setIsDefaultSender(isDefaultSenderGar);
            customerGar.setIsDefaultReciever(isDefaultReceiverGar);
            return;
        }
        if (isDefaultSenderRequest != isDefaultSenderGar || isDefaultReceiverRequest != isDefaultReceiverGar) {
            if (sourcePaymentMethodInRequest.getIsDefaultSender() != null &&
                    sourcePaymentMethodInRequest.getIsDefaultSender() && !paymentMethodInSystem.getAllowDefaultSender()) {
                throw new GarException(GarException.PAYMENT_METHOD_DOES_NOT_ALLOW_DEFAULT_SENDER);
            }
            if (sourcePaymentMethodInRequest.getIsDefaultReciever() != null &&
                    sourcePaymentMethodInRequest.getIsDefaultReciever() && !paymentMethodInSystem.getAllowDefaultReceiver()) {
                throw new GarException(GarException.PAYMENT_METHOD_DOES_NOT_ALLOW_DEFAULT_RECEIVER);
            }
            updateIsDefaultSenderReciever(customer, isDefaultSenderRequest, isDefaultReceiverRequest,customerGar);
            if ((sourcePaymentMethodInRequest.getIsDefaultSender() != null && sourcePaymentMethodInRequest.getIsDefaultSender() == true)) {
                customerGar.setIsDefaultSender(isDefaultSenderRequest);
                String query =ServiceQueryEngine.getQueryStringToExecute("updateCustomerGarDefaultSender"
                        ,this.getClass(),String.valueOf(isDefaultSenderRequest?1:0),String.valueOf(customerGar.getGarId()));
                connectionUtil.executeQuery(query, parameters);
            }
            if ((sourcePaymentMethodInRequest.getIsDefaultReciever() != null && sourcePaymentMethodInRequest.getIsDefaultReciever() == true)) {
                customerGar.setIsDefaultReciever(isDefaultReceiverRequest);
                String query =ServiceQueryEngine.getQueryStringToExecute("updateCustomerGarDefaultReceiver"
                        ,this.getClass(),String.valueOf(isDefaultReceiverRequest?1:0),String.valueOf(customerGar.getGarId()));
                connectionUtil.executeQuery(query, parameters);
            }

        }
    }

    @Transactional
    public void updateIsDefaultSenderReciever (Customer customer, Boolean IS_DEFAULT_SENDER, Boolean
            IS_DEFAULT_RECIEVER, CustomerGar customerGar) throws GarException {
        /**
         * MKEN-1051
         * add deleted PM with the same data
         */
        String query =ServiceQueryEngine.getQueryStringToExecute("customerGarParameters"
                ,this.getClass(),String.valueOf(customer.getCustomerID()),String.valueOf(customerGar.getGarId()));
        StringBuilder queryStr = new StringBuilder("update Customer_Gar ");

        if (IS_DEFAULT_SENDER & !IS_DEFAULT_RECIEVER) {
            queryStr.append("SET IS_DEFAULT_SENDER=0 " + query );
        }
            else if (!IS_DEFAULT_SENDER & IS_DEFAULT_RECIEVER) {
            queryStr.append("SET IS_DEFAULT_RECIEVER=0 " + query );
        }
            else if (IS_DEFAULT_SENDER & IS_DEFAULT_RECIEVER) {
            queryStr.append("SET IS_DEFAULT_RECIEVER=0 ,IS_DEFAULT_SENDER=0 " + query );
        }
        this.connectionUtil.executeQuery(queryStr.toString(),parameters);
    }
    public SourcePaymentMethods getPaymentMethodById(Long ID) throws GarException {
        SourcePaymentMethods sourcePaymentMethod=new SourcePaymentMethods();
        if (ID == null) {
            throw new GarException(GarException.GAR_INVALID_DATA);
        }
        String queryString = ServiceQueryEngine.getQueryStringToExecute("getPaymentMethodById",this.getClass(),String.valueOf(ID));
        List<Record> PaymentMethods = connectionUtil.executeSelect(queryString,parameters);
        if ((PaymentMethods != null) && (!PaymentMethods.isEmpty())) {
            sourcePaymentMethod.setAllowDefaultReceiver(PaymentMethods.get(0).getValueAsBoolan("ALLOW_DEFAULT_RECEIVER"));
            sourcePaymentMethod.setAllowDefaultSender(PaymentMethods.get(0).getValueAsBoolan("ALLOW_DEFAULT_SENDER"));
        }
        return sourcePaymentMethod;
    }
    public CustomerGar getCustomerGar(Long ID) throws GarException {
        CustomerGar gar = new CustomerGar();
        if (ID == null) {
            throw new GarException(GarException.GAR_INVALID_DATA);
        }
        String queryString = ServiceQueryEngine.getQueryStringToExecute("getCustomerGar",
                this.getClass(), String.valueOf(ID));
        List<Record> customerGars = this.connectionUtil.executeSelect(queryString, parameters);
        if ((customerGars != null) && (!customerGars.isEmpty())) {
            Customer customer = new Customer();
            customer.setCustomerID(customerGars.get(0).getValueAsLong("CUSTOMER_ID"));
            gar.setGarId(customerGars.get(0).getValueAsLong("GARID"));
            gar.setCustomer(customer);
            gar.setIsDefaultReciever(customerGars.get(0).getValueAsBoolan("IS_DEFAULT_RECIEVER"));
            gar.setIsDefaultSender(customerGars.get(0).getValueAsBoolan("IS_DEFAULT_SENDER"));

        }
        return gar;
    }
}



