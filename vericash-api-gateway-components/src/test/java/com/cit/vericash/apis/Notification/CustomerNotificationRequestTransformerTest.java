package com.cit.vericash.apis.Notification;

import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CustomerNotificationRequestTransformerTest {

    @Mock
    private NotificationImpl notificationImpl;

    @Mock
    private Request mockRequest;

    @Mock
    private Message mockMessage;

    @Mock
    private Payload mockPayload;

    @InjectMocks
    private CustomerNotificationRequestTransformer transformer;

    @Before
    public void setUp() {
        when(mockRequest.getMessage()).thenReturn(mockMessage);
        when(mockMessage.getPayload()).thenReturn(mockPayload);
    }

    @Test
    public void process_WhenNRecentIsTrue_ReturnsRecentNotifications() throws Exception {
        // Arrange
        when(mockPayload.getAttributeAsBoolean("nRecent")).thenReturn(true);
        List<MobileNotification> expected = new ArrayList<>();
        expected.add(new MobileNotification());
        when(notificationImpl.viewRecentNotification(mockRequest)).thenReturn(expected);

        // Act
        Object result = transformer.process(mockRequest);

        // Assert
        assertSame(expected, result);
    }

    @Test
    public void process_WhenNRecentIsFalse_ReturnsAllNotifications() throws Exception {
        // Arrange
        when(mockPayload.getAttributeAsBoolean("nRecent")).thenReturn(false);
        List<MobileNotification> expected = new ArrayList<>();
        expected.add(new MobileNotification());
        when(notificationImpl.viewAllNotification(mockRequest)).thenReturn(expected);

        // Act
        Object result = transformer.process(mockRequest);

        // Assert
        assertSame(expected, result);
    }

    @Test(expected = Exception.class)
    public void process_WhenViewRecentThrowsException_ThrowsException() throws Exception {
        // Arrange
        when(mockPayload.getAttributeAsBoolean("nRecent")).thenReturn(true);
        when(notificationImpl.viewRecentNotification(mockRequest))
                .thenThrow(new RuntimeException("Recent Error"));

        // Act
        transformer.process(mockRequest);
    }

    @Test(expected = Exception.class)
    public void process_WhenViewAllThrowsException_ThrowsException() throws Exception {
        // Arrange
        when(mockPayload.getAttributeAsBoolean("nRecent")).thenReturn(false);
        when(notificationImpl.viewAllNotification(mockRequest))
                .thenThrow(new RuntimeException("All Error"));

        // Act
        transformer.process(mockRequest);
    }

    @Test
    public void process_ExceptionThrown_MessagePropagated() throws GeneralFailureException {
        // Arrange
        String errorMessage = "Service Error";
        when(mockPayload.getAttributeAsBoolean("nRecent")).thenReturn(true);
        when(notificationImpl.viewRecentNotification(mockRequest))
                .thenThrow(new RuntimeException(errorMessage));

        try {
            transformer.process(mockRequest);
        } catch (Exception e) {
            // Assert
            assertEquals(errorMessage, e.getMessage());
        }
    }
}