package com.cit.vericash.apis.updatecustomerinfo.username;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.pupulateSenderInfoUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.updatecustomerinfo.username.UpdateUserNameResponseTransformer")
public class UpdateUserNameResponseTransformer implements MuleResponseTransformer {
    @Autowired
    pupulateSenderInfoUtil pupulate;

    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, String> result = new HashMap<String, String>();
//		pupulateSenderInfoUtil pupulate=pupulateSenderInfoUtil.getInstance();
//		pupulate.removeUserInfo(EnumTransformer.identificationTypeEnum.USERID,
//				businessMessage.getPrimarySenderInfo().getUserId(), OwnerTypeEnum.CUSTOMER);
        return result;
    }
}