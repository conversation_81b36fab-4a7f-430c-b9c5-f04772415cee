package com.cit.vericash.apis.topupOperators;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.international.airtime.common.OperatorInfo;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.topupOperators.AirTimeTopupOperatorsResponseTransformer")
public class AirTimeTopupOperatorsResponseTransformer implements MuleResponseTransformer {
    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        if (businessMessage.getITopup() != null && businessMessage.getITopup().getOperatorsList().size() > 0) {
            List<OperatorInfo> airTimeOperatorList = businessMessage.getITopup().getOperatorsList();
            result.put("airTimeOperatorList", airTimeOperatorList);
        }
        return result;
    }
}


