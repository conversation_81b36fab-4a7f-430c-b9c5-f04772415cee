package com.cit.vericash.apis.getBillersByCategoryId;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.mongo.beans.BillerPerCategory;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Component
public class GetBillersByCategoryIdResponseTransformer implements MuleResponseTransformer {
    private final String billersKey = "billers";
    private final String interswitchBillers = "interswitchBillersByCategoryId";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        Gson gson = new Gson();
        try {

            if (businessMessage.getSoftFields().get(interswitchBillers) != null) {
                Set<BillerPerCategory> billers = (Set<BillerPerCategory>) businessMessage.getSoftFields().get(interswitchBillers);
                result.put(cacheableFieldParameters.getResponseParameters(billersKey), billers);
            }

        } catch (Exception e) {
            throw e;
        }

        return result;
    }
}



