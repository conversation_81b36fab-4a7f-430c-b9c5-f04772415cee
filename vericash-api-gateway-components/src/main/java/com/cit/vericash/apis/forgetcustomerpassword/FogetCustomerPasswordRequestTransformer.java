package com.cit.vericash.apis.forgetcustomerpassword;

import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.forgetcustomerpassword.FogetCustomerPasswordRequestTransformer")
public class FogetCustomerPasswordRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

    }
}
