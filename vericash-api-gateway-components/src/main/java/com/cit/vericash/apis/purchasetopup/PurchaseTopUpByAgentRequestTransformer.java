package com.cit.vericash.apis.purchasetopup;

import com.cit.mpaymentapp.common.message.BillerInfo;
import com.cit.mpaymentapp.common.message.PayBillInfo;
import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.purchasetopup.PurchaseTopUpByAgentRequestTransformer")
public class PurchaseTopUpByAgentRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNER_TYPE = "sender-OwnerType";
    private final String BILLER_ID = "biller-Id";
    private final String BILLER_CATEGORY_ID = "billerCategoryId";
    private final String BILLER_NAME = "biller-Name";
    private final String BILL_ID = "bill-Id";
    private final String BILL_AMOUNT = "bill-Amount";
    private final String ATTRIBUTES = "attributes";
    private final String TRANSACTION_AMOUNT = "transactionAmount";
    private final String TRANSACTION_FEE_AMOUNT = "transactionFeeAmount";
    private final String TRANSACTION_VAT_AMOUNT = "transactionVatAmount";
    private final String TRANSACTION_TOTAL_AMOUNT = "transactionTotalAmount";
    private final String TRANSACTION_INFO = "transactionInfo";
    private final String BILLS = "bills";
    private final String BILLERS = "billers";

    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        try {
//            cacheableFieldParameters = CacheableFieldParametersImpl.getInstance();
            //   String ownerTypeKey = cacheableFieldParameters.getRequestParameters(OWNER_TYPE);
            String billerCategoryIdKey = cacheableFieldParameters.getRequestParameters(BILLER_CATEGORY_ID);
            String billerIdKey = cacheableFieldParameters.getRequestParameters(BILLER_ID);
            String billIdKey = cacheableFieldParameters.getRequestParameters(BILL_ID);
            String billerNameKey = cacheableFieldParameters.getRequestParameters(BILLER_NAME);
            String billAmountKey = cacheableFieldParameters.getRequestParameters(BILL_AMOUNT);
            String attributesKey = cacheableFieldParameters.getRequestParameters(ATTRIBUTES);
            String transactionAmountKey = cacheableFieldParameters.getRequestParameters(TRANSACTION_AMOUNT);
            String transactionFeeAmountKey = cacheableFieldParameters.getRequestParameters(TRANSACTION_FEE_AMOUNT);
            String transactionVatAmountKey = cacheableFieldParameters.getRequestParameters(TRANSACTION_VAT_AMOUNT);
            String transactionTotalAmountKey = cacheableFieldParameters.getRequestParameters(TRANSACTION_TOTAL_AMOUNT);
            String transactionInfoKey = cacheableFieldParameters.getRequestParameters(TRANSACTION_INFO);
            String billsKey = cacheableFieldParameters.getRequestParameters(BILLS);
            String billersKey = cacheableFieldParameters.getRequestParameters(BILLERS);


            BillerInfo billInfo = new BillerInfo();
            PayBillInfo payBillInfo = new PayBillInfo();
            TransactionInformation transactionInfo = new TransactionInformation();
            //prepare sender info data
          /*  if (message.getPayload().containsKey(ownerTypeKey)) {
                String ownerTypeString = (String) message.getPayload().get(ownerTypeKey);
                if (!StringUtils.isBlank(ownerTypeString)) {
                    Enums.OwnerTypeEnum ownerType;
                    switch (Integer.valueOf(ownerTypeString)) {
                        case 1: {
                            ownerType = Enums.OwnerTypeEnum.CUSTOMER;
                            break;
                        }
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6: {
                            ownerType = Enums.OwnerTypeEnum.BUSINESSENTITY;
                            break;
                        }
                        default: {
                            ownerType = Enums.OwnerTypeEnum.None;
                            break;
                        }
                    }
                    message.getPayload().put(ownerTypeKey,ownerType);
                }
            }*/

            //prepare billers info
            if (message.getPayload().containsKey(billerCategoryIdKey)) {
                billInfo.setBillerCatgoryId((String) message.getPayload().get(billerCategoryIdKey));
            }
            billInfo.setBillerId(message.getPayload().containsKey(billerIdKey) ? (String) message.getPayload().get(billerIdKey) : null);
            billInfo.setBillerName(message.getPayload().containsKey(billerNameKey) ? (String) message.getPayload().get(billerNameKey) : null);
            Map<String, String> billAttribute = message.getPayload().containsKey(attributesKey) ? (Map<String, String>) message.getPayload().get(attributesKey) : null;
            billInfo.setBillAttributes(billAttribute);
            payBillInfo.setBillId(message.getPayload().containsKey(billIdKey) ? (String) message.getPayload().get(billIdKey) : null);
            payBillInfo.setAmount(String.valueOf(message.getPayload().containsKey(billAmountKey) ? message.getPayload().get(billAmountKey) : null));

            //prepare transaction info
            String transactionAmount = message.getPayload().getAttributeAsString(transactionAmountKey);
            transactionInfo.setTransactionAmount(new BigDecimal(transactionAmount));
            String transactionFeeAmount = message.getPayload().getAttributeAsString(transactionFeeAmountKey);
            transactionInfo.setTransactionFeeAmount(new BigDecimal(transactionFeeAmount));
            String transactionVatAmount = message.getPayload().getAttributeAsString(transactionVatAmountKey);
            transactionInfo.setTransactionVatAmount(new BigDecimal(transactionVatAmount));
            String transactionTotalAmount = message.getPayload().getAttributeAsString(transactionTotalAmountKey);
            transactionInfo.setTransactionTotalAmount(new BigDecimal(transactionTotalAmount));

            //set message payload values
            message.getPayload().put(transactionInfoKey, transactionInfo);
            message.getPayload().put(billsKey, payBillInfo);
            message.getPayload().put(billersKey, billInfo);

        } catch (Exception e) {
            throw e;
        }
    }
}
