<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:tns="http://ws.entrustplugin.expertedge.com/"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" targetNamespace="http://ws.entrustplugin.expertedge.com/"
             name="EntrustMultiFactorAuthImplService" xmlns="http://schemas.xmlsoap.org/wsdl/"
>
    <types>
        <xsd:schema>
            <xsd:import namespace="http://ws.entrustplugin.expertedge.com/"
                        schemaLocation="schemaLocation.xsd"/>
        </xsd:schema>
    </types>
    <message name="pingEntrustConn"/>
    <message name="pingEntrustConnResponse">
        <part name="return" type="xsd:string"/>
    </message>
    <message name="performTokenAuth">
        <part name="arg0" type="tns:tokenAuthDTO"/>
    </message>
    <message name="performTokenAuthResponse">
        <part name="return" type="tns:authResponseDTO"/>
    </message>
    <portType name="EntrustMultiFactorAuth">
        <operation name="pingEntrustConn">
            <input
                    wsam:Action="http://ws.entrustplugin.expertedge.com/EntrustMultiFactorAuth/pingEntrustConnRequest"
                    message="tns:pingEntrustConn"/>
            <output
                    wsam:Action="http://ws.entrustplugin.expertedge.com/EntrustMultiFactorAuth/pingEntrustConnResponse"
                    message="tns:pingEntrustConnResponse"/>
        </operation>
        <operation name="performTokenAuth">
            <input
                    wsam:Action="http://ws.entrustplugin.expertedge.com/EntrustMultiFactorAuth/performTokenAuthRequest"
                    message="tns:performTokenAuth"/>
            <output
                    wsam:Action="http://ws.entrustplugin.expertedge.com/EntrustMultiFactorAuth/performTokenAuthResponse"
                    message="tns:performTokenAuthResponse"/>
        </operation>
    </portType>
    <binding name="EntrustMultiFactorAuthImplPortBinding" type="tns:EntrustMultiFactorAuth">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http"
                      style="rpc"/>
        <operation name="pingEntrustConn">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal" namespace="http://ws.entrustplugin.expertedge.com/"/>
            </input>
            <output>
                <soap:body use="literal" namespace="http://ws.entrustplugin.expertedge.com/"/>
            </output>
        </operation>
        <operation name="performTokenAuth">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal" namespace="http://ws.entrustplugin.expertedge.com/"/>
            </input>
            <output>
                <soap:body use="literal" namespace="http://ws.entrustplugin.expertedge.com/"/>
            </output>
        </operation>
    </binding>
    <service name="EntrustMultiFactorAuthImplService">
        <port name="EntrustMultiFactorAuthImplPort" binding="tns:EntrustMultiFactorAuthImplPortBinding">
            <soap:address
                    location="http://*************:8181/entrust_plugin_v5/entrust_auth"/>
        </port>
    </service>
</definitions>