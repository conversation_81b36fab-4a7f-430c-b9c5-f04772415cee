package com.cit.vericash.apis.openBankAccount;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ParametersMap;
import org.junit.Test;



import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;


public class CreateBankAccountResponseTransformerTest {
    private final CreateBankAccountResponseTransformer transformer = new CreateBankAccountResponseTransformer();


    @Test
    public void transform_shouldReturnNull() throws Exception {
        BusinessMessage businessMessage = new BusinessMessage();
        ParametersMap parametersMap = new ParametersMap();
        businessMessage.setParameters(parametersMap);
        Object result = transformer.transform(businessMessage);
        assertNull(result);
    }

    @Test
    public void transform_shouldReturnAccountNumber() throws Exception {
        BusinessMessage businessMessage = new BusinessMessage();
        ParametersMap parametersMap = new ParametersMap();
        parametersMap.put("bankAccountNumber", "1234");
        businessMessage.setParameters(parametersMap);
        Object result = transformer.transform(businessMessage);
        assertEquals("1234", result);
    }
}
