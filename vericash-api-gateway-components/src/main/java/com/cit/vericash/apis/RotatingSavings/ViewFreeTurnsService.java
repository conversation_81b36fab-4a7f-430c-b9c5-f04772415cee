package com.cit.vericash.apis.RotatingSavings;


import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.ViewFreeTurnsService")
public class ViewFreeTurnsService {

    @Autowired
    ConnectionUtil connectionUtil;


    public List<Integer> listOfFreeTurns(Request request) {
        Long rotatingSavingId = request.getMessage().getPayload().getAttributeAsLong("rotatingSavingId");
        return ViewFreeHelper(rotatingSavingId);
    }

    public List<Integer> ViewFreeHelper(Long rotatingSavingId) {
        List<Integer> listOfUsedTurns = new ArrayList<Integer>();
        List<Integer> listOfAllowedTurns = new ArrayList<Integer>();
        int maxMembers = 0;
        List<Parameter> parameters = new ArrayList<>();
        List<String> fieldsTurn = new ArrayList<>();
        List<String> fieldsMem = new ArrayList<>();
        fieldsTurn = setFieldsTurn(fieldsTurn);
        fieldsMem = setFieldsMem(fieldsMem);

        String queryMem = ServiceQueryEngine.getQueryStringToExecute("ViewFreeHelper", this.getClass(), String.valueOf(rotatingSavingId));

        String queryTurn = ServiceQueryEngine.getQueryStringToExecute("ViewFreeHelper2", this.getClass(), String.valueOf(rotatingSavingId));

        List<Record> recordsTurn = connectionUtil.executeSelect(queryTurn, parameters, fieldsTurn);
        List<Record> recordsMem = connectionUtil.executeSelect(queryMem, parameters, fieldsMem);

        for (Record record : recordsTurn) {
            int turn = record.getValueAsInt("TURN_NUMBER");
            listOfUsedTurns.add(turn);
        }
        for (Record record : recordsMem) {
            maxMembers = record.getValueAsInt("NUMBER_MEMBERS");
        }

        for (int i = 1; i <= maxMembers; i++) {
            if (!listOfUsedTurns.contains(i)) {
                listOfAllowedTurns.add(i);
            }
        }
        return listOfAllowedTurns;

    }

    public List<String> setFieldsTurn(List<String> fields) {
        fields.add("TURN_NUMBER");
        return fields;
    }

    public List<String> setFieldsMem(List<String> fields) {
        fields.add("NUMBER_MEMBERS");
        return fields;
    }
}
