package com.cit.vericash.apis.generateOTPDynamic;

import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.util.CacheableWalletInfo;
import com.cit.vericash.apis.commons.util.MsisdnFormatterImpl;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;

@Component
@Qualifier("com.cit.vericash.apis.generateOTPDynamic.GenerateOTPRequestTransformer")
public class GenerateOTPRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    private static final String INVALID_MOBILE_NUMBER = "invalidMobileNumberFormatErrorCode";
    @Autowired
    MsisdnFormatterImpl msisdnFormatter;
    @Autowired
    private PropertyLoaderComponent propertyLoaderComponent;
    @Autowired
    private CacheableWalletInfo walletInfoCache;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        String targetApiCode = message.getPayload().getAttributeAsString("otpApiCode");
        String mobileNumber = message.getPayload().getAttributeAsString("recieverMSISDN");
        String walletShortCode = message.getHeader().getAttributeAsString("walletShortCode");
        WalletInfo walletInfo = walletInfoCache.getWalletInfo(walletShortCode);
        String region = walletInfo.getCountryIso2();
        String newMobileNumberApisString = propertyLoaderComponent.getPropertyAsString("newMobileNumberApis", "application", "application");

        if (newMobileNumberApisString != null) {
            String[] newMobileNumberApisArray = newMobileNumberApisString.split(",");
            ArrayList<String> newMobileNumberApis = new ArrayList(Arrays.asList(newMobileNumberApisArray));
            if (newMobileNumberApis != null && newMobileNumberApis.contains(targetApiCode)) {
                try {
                    msisdnFormatter.formatMSISDN(mobileNumber, region, walletShortCode);
                } catch (Exception e) {
                    throw new APIException(getInvalidMobileNumberFormatErrorCode(walletShortCode));
                }

            }
        }


    }

    private String getInvalidMobileNumberFormatErrorCode(String walletShortCode) {
        return propertyLoaderComponent.getPropertyAsString(walletShortCode + "." + INVALID_MOBILE_NUMBER);
    }
}
