package com.cit.vericash.util;

import com.cit.mpaymentapp.model.transaction.TransactionStatus;

public class TransactionUtils {

    public static TransactionStatus getTransactionStatusName(Object transactionStatus) {
        if (transactionStatus == null) {
            return null;
        }
        int status;
        try {
            status = Integer.parseInt(transactionStatus.toString());
        }catch (Exception e){
            return null;
        }
        switch (status) {
            case 0:
                return TransactionStatus.INITIATED;
            case 1:
                return TransactionStatus.SUCCEEDED;
            case 2:
                return TransactionStatus.FAILED;
            case 3:
                return TransactionStatus.REVERSED;
            case 4:
                return TransactionStatus.WAIT_APPROVAL;
            case 5:
                return TransactionStatus.PENDING_REVERSAL;
            case 6:
                return TransactionStatus.PARTIALLY_REVERSED;
            case 7:
                return TransactionStatus.REDEEMED;
            case 8:
                return TransactionStatus.Pending;
            case 9:
                return TransactionStatus.UNDER_PROCESSING_TO;
            case 10:
                return TransactionStatus.UNDER_PROCESSING_CB;
            default:
                return null;
        }
    }
}
