package com.cit.vericash.util;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ApiValidationException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

@Service
@RequiredArgsConstructor
public class ApplicationNameValidator {
    public static final List<String> API_CODES_WHITE_LIST = new ArrayList<>();
    public static final String SANLAM = "SANLAM";
    public static final String VDI = "vdi";
    private final ConnectionUtil connectionUtil;
    private final RequestParsingUtils requestParsingUtils;
    private final PropertyLoaderComponent propertyLoaderComponent;

//    @PostConstruct
//    private void init(){
//        API_CODES_WHITE_LIST.addAll(Arrays.asList(
//                propertyLoaderComponent.getPropertyAsString("APPLICATION_NAME_WHITE_LIST").split(",")));
//    }
    public void validateApplicationName(Request request){
//        if (request == null ||
//                request.getMessage() == null ||
//                request.getMessage().getHeader() == null ||
////                Objects.equals(request.getMessage().getHeader().getAttributeAsString("apiCode"), LOGIN_API_CODE)) {
//                API_CODES_WHITE_LIST.contains(request.getMessage().getHeader().getAttributeAsString("apiCode"))) {
//            return;
//        }
        String applicationName = null;
        if (request.getMessage().getHeader() != null){
            try{
                applicationName = (String) request.getMessage().getHeader().get("applicationName");
            }catch (Exception ex){
//                throw new APIException(ErrorCode.INVALID_APPLICATION_NAME.getErrorCode());
                System.err.println(ex.getMessage());
            }
            System.out.println(applicationName);
        }
        if (applicationName == null) {
            return;
        }
        if (Objects.equals(applicationName.toUpperCase(), SANLAM)) {
            throw new APIException(ErrorCode.INVALID_APPLICATION_NAME.getErrorCode());
        }
//        List<Record> records = getDeviceApplicationName(request);
//        String deviceApplicationName = null;
//        if (records != null && !records.isEmpty()) {
//            deviceApplicationName = records.get(0).getValueAsString("APPLICATION_NAME");
//            System.out.println(deviceApplicationName);
//        }
//
//        if (deviceApplicationName == null) {
//            // if not found in database should i return ot throw exception ?
//            return;
//        }
//        if (!Objects.equals(deviceApplicationName, applicationName)){
//            throw new APIException(ErrorCode.INVALID_APPLICATION_NAME.getErrorCode());
//        }
    }

    private List<Record> getDeviceApplicationName(Request request) {
        String vdi = requestParsingUtils.getFromDeviceInfo(request, VDI);
        if (vdi == null) {
            throw new ApiValidationException(List.of(propertyLoaderComponent.getPropertyAsString("API_11143", "application", "error")));
        }
        System.out.println(vdi);
        List<Parameter> parameters = new ArrayList<>();
        parameters.add(new Parameter(1, vdi));
        List<String> fields = new ArrayList<>();
        fields.add("APPLICATION_NAME");
        String query = ServiceQueryEngine.getQueryStringToExecute("get.Application.Name", this.getClass());
        System.out.println(query);
        return connectionUtil.executeSelect(query, parameters, fields);
    }


}
