loadSplitBEId=SELECT BUSINESS_ENTITY_ID as SPLITBEID FROM BUSINESS_ENTITY_HIERARCHY WHERE BUSINESS_ENTITY_ID = '%1$s' AND BUSINESS_ENTITY_STATUS = 1 AND PARENT_BUSINESS_ENTITY = (SELECT BUSINESS_ENTITY_ID FROM BUSINESS_ENTITY_HIERARCHY beh WHERE beh.SHORT_CODE = '%2$s')
loadServiceId=SELECT bsc.ID FROM BUSINESS_SERVICE_CONFIG bsc INNER JOIN SERVICE_CONFIG_MAP scm ON scm.SERVICE_TYPE_ID = bsc.BUSINESS_SERVICE_TYPE WHERE scm.SERVICE_CODE = '%1$s'