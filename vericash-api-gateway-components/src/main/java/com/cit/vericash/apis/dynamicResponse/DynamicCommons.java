package com.cit.vericash.apis.dynamicResponse;


import com.cit.vericash.LookupFramework.common.util.response.LookupResponse;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Objects;
import java.util.Set;

@Service
public class DynamicCommons {

    public LinkedHashMap<String, Object> extractResultMap(LookupResponse lookupResponse, Set<String> keys, LinkedHashMap<String, Object> resultMap) {
        ArrayList<LinkedTreeMap<String, Object>> resultSet;
        for (String key : keys) {
            if (lookupResponse.get(key) instanceof LinkedTreeMap) {
                LinkedTreeMap<String, Object> capturedKey = (LinkedTreeMap<String, Object>) lookupResponse.get(key);
                resultSet = (ArrayList<LinkedTreeMap<String, Object>>) capturedKey.get("records");
                resultMap.put(key, Objects.requireNonNullElse(resultSet, capturedKey));
            } else {
                Object object = lookupResponse.get(key);
                resultMap.put(key, object);
            }
        }
        return resultMap;
    }
}
