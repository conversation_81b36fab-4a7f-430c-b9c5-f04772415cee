package com.cit.vericash.dynamicrequest.transformer;

import com.cit.vericash.api.transformerfactory.RequestTransformer;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.backend.commons.dynamicinputparameters.DynamicRequestModelMapperInitializerImpl;
import com.cit.vericash.backend.commons.dynamicinputparameters.DynamicRequestModelMapperInitializerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.backend.commons.dynamicpayload.*;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import org.apache.commons.jxpath.JXPathContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.cit.vericash.backend.commons.dynamicmodelmapper.*;
import com.cit.vericash.backend.commons.dynamicmodelmapper.DynamicModelMapper;
import com.cit.vericash.backend.commons.dynamicmodelmapper.Field;
import com.cit.vericash.backend.commons.dynamicmodelmapper.MapWrapper;
import com.cit.vericash.backend.commons.dynamicmodelmapper.ObjectSchema;
import com.cit.vericash.backend.commons.dynamicinputparameters.DynamicModelMapperInitializer;

@Component
public class HashMapMessageToMessageTransformerImpl implements RequestTransformer {
    private final DynamicRequestModelMapperInitializerImpl dynamicRequestModelMapperInitializer;

    @Autowired
    private CachableVericashApi vericashApiCache;

    public HashMapMessageToMessageTransformerImpl() {

        this.dynamicRequestModelMapperInitializer = new DynamicRequestModelMapperInitializerImpl();
    }

    @Override
    public Message transform(Message message) throws Exception {
        String apiCode = message.getHeader().getAttributeAsString("apiCode");
        List<Map<String, Object>> originalDynamicGroup = (List<Map<String, Object>>) message.getPayload().get("dynamicGroup");
        Map<String, Object> modifiedInputParameters = modifyDynamicGroup(originalDynamicGroup);
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setPayload(new Payload());
        if (message.getAdditionalData() != null) {
            dynamicPayload.getPayload().putAll(message.getAdditionalData());
        }
        dynamicPayload.getPayload().putAll(modifiedInputParameters);
        dynamicPayload.setHeader(message.getHeader());
        message.setDynamicPayload(dynamicPayload);
        return message;
    }

    @Override
    public void mapValuesToObjects(DynamicModelMapper modelMapper, JXPathContext sourceObject, Message targetMessage) {

    }


    private Map<String, Object> modifyDynamicGroup(List<Map<String, Object>> dynamicGroupList) {
        Map<String, Object> inputParametersMap = new LinkedHashMap<>();
        if(dynamicGroupList==null)
            return inputParametersMap;
        for (Map<String, Object> dynamicGroupMap : dynamicGroupList) {
            inputParametersMap.putAll((Map<String, Object>) dynamicGroupMap.get("inputParameters"));
        }
        return inputParametersMap;
    }


}
