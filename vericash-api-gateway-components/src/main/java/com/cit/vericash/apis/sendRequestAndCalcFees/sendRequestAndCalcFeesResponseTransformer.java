package com.cit.vericash.apis.sendRequestAndCalcFees;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
@Component
@Qualifier("sendRequestAndCalcFeesResponseTransformer")
public class sendRequestAndCalcFeesResponseTransformer implements MuleResponseTransformer {
    private final String trxTotalAmountKey = "Transaction_Total_amount";
    private final String trxAmountKey = "Transaction-Amount";
    //private final String totalFeeAmountKey="Total-Fee-Amount";Transaction-Fee-Amount
    private final String totalFeeAmountKey = "Transaction-Fee-Amount";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, Object> result = new HashMap<String, Object>();
        Map<String, Object> ReceiverDetails = new HashMap<String, Object>();

        try {
            TransactionInformation transactionInformation = businessMessage.getTransactionInfo();
            if (transactionInformation != null) {
                result.put(cacheableFieldParameters.getResponseParameters(trxAmountKey), transactionInformation.getTransactionAmount());
                BigDecimal feesRuleOutputModel = businessMessage.getParameters().getAttributeAsBigDecimal("feesRuleOutputModel");
                if (feesRuleOutputModel != null)
                    result.put(cacheableFieldParameters.getResponseParameters(totalFeeAmountKey), feesRuleOutputModel);
                else
                    result.put(cacheableFieldParameters.getResponseParameters(totalFeeAmountKey), "0");

                BigDecimal totalAmount = null;
                if (feesRuleOutputModel != null) {
                    totalAmount = feesRuleOutputModel.add(transactionInformation.getTransactionAmount());
                    result.put(cacheableFieldParameters.getResponseParameters(trxTotalAmountKey), totalAmount);

                } else {

                    result.put(cacheableFieldParameters.getResponseParameters(trxTotalAmountKey), transactionInformation.getTransactionAmount());
                }
            }

            BigDecimal commissionAmount = (BigDecimal) businessMessage.getSoftFields().get("commissionAmount");
            if (commissionAmount != null)
                result.put(cacheableFieldParameters.getResponseParameters("commissionAmount"), commissionAmount);

        } catch (Exception e) {
            throw e;
        }
        ReceiverDetails.put("MobileNumber" , businessMessage.getPrimaryReceiverInfo().getMsisdn());
        ReceiverDetails.put("name" , businessMessage.getPrimaryReceiverInfo().getUserName());
        result.put("ReceiverDetails",businessMessage.getDynamicPayload().getPayload().getAttribute("recipientMobileNumbers"));
        return result;
    }
}
