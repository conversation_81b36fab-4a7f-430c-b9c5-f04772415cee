package com.cit.vericash.LookupFramework.transformers.customTransformer;

import com.cit.vericash.LookupFramework.common.interfaces.Transformer;
import com.cit.vericash.LookupFramework.common.util.LookupDTO;
import com.cit.vericash.LookupFramework.common.util.Record;
import com.cit.vericash.LookupFramework.common.util.enums;
import com.cit.vericash.LookupFramework.common.util.response.LookupResponse;
import com.cit.vericash.LookupFramework.transformers.DTO.paymentMetods.PaymentMethodContainerDTO;
import com.cit.vericash.LookupFramework.transformers.DTO.paymentMetods.PaymentMethodViewResultDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ViewPaymentMethodNewStructure implements Transformer {
    public static final String PaymentMethodCURRENCY = "CURRENCY";
    public static final String PaymentMethodBALANCE = "BALANCE";
    public static final String WalletCURRENCY = "WALLET CURRENCY";
    public static final String EquivalentBalance = "Equivalent Balance";
    public static final String PaymentMethodSHORT_CODE = "SHORT_CODE";
    public static final String PaymentMethodSCHEME_CODE = "SCHEME_CODE";
    public static final String ACCOUNT_STATUS = "ACCOUNT_STATUS";
    public static final String ACCOUNT_TYPE = "ACCOUNT_TYPE";
    public static final String ACCOUNT_NUMBER = "ACCOUNT_NUMBER";
    public static final String decimalFormatPattern = "0.0";
    public static final int MaximumFractionDigits = 340;

    @SneakyThrows
    @Override
    public void transform(LookupDTO lookupDTO, Object response) {
        ObjectMapper mapper = new ObjectMapper();
        Gson gson = new Gson();
        LookupResponse lookupResponse = (LookupResponse) response;
        Object lookupResponseObj = lookupResponse.getAttribute(lookupDTO.getLookupName());
        HashMap<String, Object> map = mapper.convertValue(lookupResponseObj, HashMap.class);
        String responseStr = gson.toJson(map.get("records"));
        List<Record> recordList = null;
        try {
            recordList = mapper.readValue(responseStr, new TypeReference<List<Record>>() {
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<Map<String, Object>> paymentMethodsDTOS = getPaymentMethodsAndItsParameters(recordList);
        PaymentMethodContainerDTO paymentMethodContainerDTO = new PaymentMethodContainerDTO();
        paymentMethodContainerDTO.setDefinition(enums.PaymentMethodDefination.SourcePaymentMethod);
        paymentMethodContainerDTO.setPaymentMethods(paymentMethodsDTOS);
        lookupResponse.put(lookupDTO.getLookupName(), paymentMethodContainerDTO.getPaymentMethods());
    }

    private List<Map<String, Object>> getPaymentMethodsAndItsParameters(List<Record> records) {

        List<PaymentMethodViewResultDTO> ListOfPaymentMethods = getViewPaymentMethods(records);


        Map<String, List<PaymentMethodViewResultDTO>> collect1 = ListOfPaymentMethods.stream()
                .collect(Collectors.groupingBy(PaymentMethodViewResultDTO::getPaymentMethodCode));
        List<Map<String, Object>> finalListOfPaymentMethods = new ArrayList<>();
        collect1.forEach((key, value) -> {
            Map<String, Object> map = new HashMap<>();
            value.forEach(list -> {
                map.put("paymentMethodName", list.getPaymentMethodName());
                map.put("paymentMethodType", list.getPaymentMethodType().toString());
                map.put("paymentMethodCode", list.getPaymentMethodCode() != null ? Long.valueOf(list.getPaymentMethodCode()) : null);
                if (list.getParameterCode() != null) {
                    map.put(list.getParameterCode().toString(), list.getParameterValue());
                    map.put("groupId", list.getGroupId());
                    map.put("groupName", list.getGroupName());
                }
                map.put("acceptOverDraft", list.getAcceptOverDraft());
                map.put("isCredit", list.getIsCredit());
                map.put("isDebt", list.getIsDebt());
                map.put("allowUpdate", list.getAllowUpdate()? 1 : 0);
                map.put("allowDelete", list.getAllowDelete()? 1 : 0);
                map.put("allowFreeze", list.getAllowFreeze()? 1 : 0);
                map.put("allowUnfreeze", list.getAllowUnfreeze()? 1 : 0);
                map.put("openedUponCustomerRequest", list.getOpenedUponCustomerRequest());
                map.put("overDraftLimit", list.getOverDraftLimit());
                map.put("allowBalanceInquiry", list.getAllowBalanceInquiry());
                map.put("currency", list.getCurrency_unit());
                map.put("statusKey", list.getStatusKey());
                map.put("statusValue", list.getStatusValue());
                map.put("isDefaultSender", list.getDefaultSender() == null ? 0 : list.getDefaultSender() ? 1 : 0);
                map.put("isDefaultReciever", list.getDefaultReciever() == null ? 0 : list.getDefaultReciever()? 1 : 0);
                map.put("paymentAlias", list.getPaymentAlias());
                map.put("paymentBalance", list.getBalance().toString());
                map.put("shortCode", list.getShortCode());
                map.put("schemeCode", list.getSchemeCode());
                map.put("accountStatus", list.getAccountStatus());
                map.put("accountType", list.getAccountType());
                map.put("accountNumber", list.getAccountNumber());
                map.put("paymentMethodTypeName", list.getPaymentMethodTypeName());
                map.put("customerName", list.getCustomerName());
                map.put("lastModifiedDate", list.getLastModifiedDate());
            });
            finalListOfPaymentMethods.add(map);
        });
        return finalListOfPaymentMethods;
    }


    private List<PaymentMethodViewResultDTO> getViewPaymentMethods(List<Record> records) {
        List<PaymentMethodViewResultDTO> ListOfPaymentMethods = new ArrayList<PaymentMethodViewResultDTO>();
        boolean allowUpdate = false;
        boolean allowDelete = false;
        boolean allowFreeze = false;
        boolean allowUnfreeze = false;
        if (records.size() > 0) {
            for (Record rec : records) {
                PaymentMethodViewResultDTO paymentMethod = new PaymentMethodViewResultDTO();

                if (rec.getValueAsString("PM.NAME") != null) {
                    paymentMethod.setPaymentMethodName((rec.getValueAsString("PM.NAME")));
                }
                if (rec.get("SPM.ID") != null && rec.get("SPM.ID") != "") {
                    paymentMethod.setPaymentMethodType(rec.getValueAsLong("SPM.ID"));
                }
                if (rec.get("SPM.AUTOMATICALLY_OPENED") != null && rec.get("SPM.AUTOMATICALLY_OPENED") != "") {
                    paymentMethod.setAutomaticallyOpened(getBooleanValue(rec.getValueAsLong("SPM.AUTOMATICALLY_OPENED")));
                }
                if (rec.get("SPM.STORAGELOCATION") != null && rec.get("SPM.STORAGELOCATION") != "") {
                    enums.PaymentMethodStorageLocation paymentMethodStorageLocation = enums.PaymentMethodStorageLocation.getTypeEnum(rec.getValueAsLong("SPM.STORAGELOCATION").intValue());
                    paymentMethod.setStorageLocation(paymentMethodStorageLocation.name());
                }
                if (rec.get("SPM.IS_SINGLE_ACCOUNT") != null && rec.get("SPM.IS_SINGLE_ACCOUNT") != "") {
                    paymentMethod.setIsSingleAccount(getBooleanValue(rec.getValueAsLong("SPM.IS_SINGLE_ACCOUNT")));
                }
                if (rec.get("SPM.ACCEPT_OVER_DRAFT") != null && rec.get("SPM.ACCEPT_OVER_DRAFT") != "") {
                    paymentMethod.setAcceptOverDraft(getBooleanValue(rec.getValueAsLong("SPM.ACCEPT_OVER_DRAFT")));
                }
                if (rec.get("SPM.IS_CREDIT") != null && rec.get("SPM.IS_CREDIT") != "") {
                    paymentMethod.setIsCredit(getBooleanValue(rec.getValueAsLong("SPM.IS_CREDIT")));
                }
                if (rec.get("SPM.IS_DEBT") != null && rec.get("SPM.IS_DEBT") != "") {
                    paymentMethod.setIsDebt(getBooleanValue(rec.getValueAsLong("SPM.IS_DEBT")));
                }
                if (rec.get("SPM.OPENED_UPON_CUS_REQ") != null && rec.get("SPM.OPENED_UPON_CUS_REQ") != "") {
                    paymentMethod.setOpenedUponCustomerRequest(getBooleanValue(rec.getValueAsLong("SPM.OPENED_UPON_CUS_REQ")));
                }
                if (rec.getValueAsString("SPM.OVER_DRAFT_LIMIT") != null) {
                    paymentMethod.setOverDraftLimit(rec.getValueAsString("SPM.OVER_DRAFT_LIMIT"));
                }
                if (rec.get("SPM.ALLOW_BALANCE_INQUIRY") != null && rec.get("SPM.ALLOW_BALANCE_INQUIRY") != "") {
                    paymentMethod.setAllowBalanceInquiry(getBooleanValue(rec.getValueAsLong("SPM.ALLOW_BALANCE_INQUIRY")));
                }
                if (rec.getValueAsString("PM.CURRENCY_OR_UNIT") != null) {
                    paymentMethod.setCurrency_unit(rec.getValueAsString("PM.CURRENCY_OR_UNIT"));
                }
                if (rec.get("SPM.HAS_EQUIVALENT_BALANCE") != null && rec.get("SPM.HAS_EQUIVALENT_BALANCE") != "") {
                    paymentMethod.setHasEquivalentBalance(getBooleanValue(rec.getValueAsLong("SPM.HAS_EQUIVALENT_BALANCE")));
                }
                if (rec.get("IPD.CODE") != null && rec.get("IPD.CODE") != "") {
                    paymentMethod.setParameterCode(rec.getValueAsLong("IPD.CODE"));
                }
                if (rec.get("CG.LAST_MODIFIED_DATE") != null && rec.get("CG.LAST_MODIFIED_DATE") != "") {
                    paymentMethod.setLastModifiedDate(rec.getValueAsString("CG.LAST_MODIFIED_DATE"));
                }
                if (rec.get("CG.GARID") != null && rec.get("CG.GARID") != "") {
                    paymentMethod.setPaymentMethodCode(String.valueOf((rec.getValueAsLong("CG.GARID"))));
                }
                if (rec.getValueAsString("CGP.PARAMETER_VALUE") != null) {
                    paymentMethod.setParameterValue(rec.getValueAsString("CGP.PARAMETER_VALUE"));
                }
                if (rec.get("SVA.BALANCE") != null && rec.get("SVA.BALANCE") != "") {
                    DecimalFormat df = new DecimalFormat(decimalFormatPattern);
                    df.setMaximumFractionDigits(MaximumFractionDigits);
                    double balance = rec.getValueAsLong("SVA.BALANCE");
                    BigDecimal balanceBigDecimal = new BigDecimal(df.format(balance));
                    paymentMethod.setBalance(balanceBigDecimal);
                } else {
                    paymentMethod.setBalance(BigDecimal.valueOf(0.0));
                }

                if (rec.getValueAsString("CG.SHORT_CODE") != null) {
                    paymentMethod.setShortCode(rec.getValueAsString("CG.SHORT_CODE"));
                }
                if (rec.getValueAsString("CG.SCHEME_CODE") != null) {
                    paymentMethod.setSchemeCode(rec.getValueAsString("CG.SCHEME_CODE"));
                }
                if (rec.get("CG.IS_DEFAULT") != null && rec.get("CG.IS_DEFAULT") != "") {
                    paymentMethod.setIsDefault(getBooleanValue(rec.getValueAsLong("CG.IS_DEFAULT")));
                }
                if (rec.get("SVA.ACCOUNT_STATUS") != null && rec.get("SVA.ACCOUNT_STATUS") != "") {
                    paymentMethod.setAccountStatus(enums.AccountStatus.getAccountStatus(rec.getValueAsLong("SVA.ACCOUNT_STATUS").intValue()));
                }
                if (rec.get("SVA.ACCOUNT_TYPE") != null && rec.get("SVA.ACCOUNT_TYPE") != "") {
                    paymentMethod.setAccountType(rec.getValueAsLong("SVA.ACCOUNT_TYPE"));
                }
                if (rec.get("SPM.ALLOW_UPDATE") != null && rec.get("SPM.ALLOW_UPDATE") != "") {
                    allowUpdate = rec.getValueAsLong("SPM.ALLOW_UPDATE") == 1;
                }
                if (rec.get("SPM.ALLOW_DELETE") != null && rec.get("SPM.ALLOW_DELETE") != "") {
                    allowDelete = rec.getValueAsLong("SPM.ALLOW_DELETE") == 1;
                }
                if (rec.get("SPM.ALLOW_FREEZE") != null && rec.get("SPM.ALLOW_FREEZE") != "") {
                    allowFreeze = rec.getValueAsLong("SPM.ALLOW_FREEZE") == 1;
                }
                if (rec.get("SPM.ALLOW_UNFREEZE") != null && rec.get("SPM.ALLOW_UNFREEZE") != "") {
                    allowUnfreeze = rec.getValueAsLong("SPM.ALLOW_UNFREEZE") == 1;
                }

                if (rec.getValueAsString("CG.ACCOUNT_NUMBER") != null) {
                    paymentMethod.setAccountNumber(rec.getValueAsString("CG.ACCOUNT_NUMBER"));
                }
                if (rec.get("IPGW.ID") != null && rec.get("IPGW.ID") != "") {
                    paymentMethod.setGroupId(rec.getValueAsLong("IPGW.ID"));
                }
                if (rec.getValueAsString("IPGD.NAME") != null) {
                    paymentMethod.setGroupName(rec.getValueAsString("IPGD.NAME"));
                }
                if (rec.get("PMP.DECIMAL_PLACES") != null && rec.get("PMP.DECIMAL_PLACES") != "") {
                    paymentMethod.setDecimalPlaces(rec.getValueAsLong("PMP.DECIMAL_PLACES"));
                }

                if (rec.get("CG.IS_DEFAULT_SENDER") != null && rec.get("CG.IS_DEFAULT_SENDER") != "") {
                    paymentMethod.setDefaultSender(rec.getValueAsLong("CG.IS_DEFAULT_SENDER")== 1);
                }
                if (rec.get("CG.IS_DEFAULT_RECIEVER") != null && rec.get("CG.IS_DEFAULT_RECIEVER") != "") {
                    paymentMethod.setDefaultReciever(rec.getValueAsLong("CG.IS_DEFAULT_RECIEVER") == 1);
                }
                if (rec.get("CG.LAST_MODIFIED_DATE") != null && rec.get("CG.LAST_MODIFIED_DATE") != "") {
                    paymentMethod.setLastModifiedDate(rec.getValueAsString("CG.LAST_MODIFIED_DATE"));
                }

                if (rec.get("C.FIRST_NAME") != null && rec.get("C.FIRST_NAME") != "") {
                    paymentMethod.setCustomerName(rec.getValueAsString("C.FIRST_NAME"));
                }

                if (rec.get("C.MIDDLE_NAME") != null && rec.get("C.MIDDLE_NAME") != "") {
                    paymentMethod.setCustomerName(paymentMethod.getCustomerName().concat(" " + rec.getValueAsString("C.MIDDLE_NAME")));
                }
                if (rec.get("C.LAST_NAME") != null && rec.get("C.LAST_NAME") != "") {
                    paymentMethod.setCustomerName(paymentMethod.getCustomerName().concat(" " + rec.getValueAsString("C.LAST_NAME")));
                }


                if (rec.getValueAsString("CG.PAYMENT_ALIAS") != null) {
                    paymentMethod.setPaymentAlias(rec.getValueAsString("CG.PAYMENT_ALIAS"));
                }
                if(rec.getValueAsString("PMT.NAME") != null){
                    paymentMethod.setPaymentMethodTypeName(rec.getValueAsString("PMT.NAME"));
                }
                if (rec.getValueAsLong("CG.STATUS") != null && rec.get("CG.STATUS") != "") {
                    paymentMethod.setStatusKey(rec.getValueAsLong("CG.STATUS").intValue());

                    if (rec.getValueAsLong("CG.STATUS").intValue() == enums.PaymentMethodStatus.ACTIVE.ordinal()) {
                        paymentMethod.setStatusValue(enums.PaymentMethodStatus.ACTIVE);
                        paymentMethod.setAllowFreeze(allowFreeze);
                        paymentMethod.setAllowUnfreeze(false);
                        paymentMethod.setAllowUpdate(allowUpdate);
                        paymentMethod.setAllowDelete(allowDelete);
                    } else if (rec.getValueAsLong("CG.STATUS").intValue() == enums.PaymentMethodStatus.INACTIVE.ordinal()) {
                        paymentMethod.setStatusValue(enums.PaymentMethodStatus.INACTIVE);
                        paymentMethod.setAllowFreeze(false);
                        paymentMethod.setAllowUnfreeze(allowUnfreeze);
                        paymentMethod.setAllowUpdate(false);
                        paymentMethod.setAllowDelete(allowDelete);
                    } else if (rec.getValueAsLong("CG.STATUS").intValue() == enums.PaymentMethodStatus.BLOCKED.ordinal()) {
                        paymentMethod.setStatusValue(enums.PaymentMethodStatus.BLOCKED);
                        paymentMethod.setAllowFreeze(false);
                        paymentMethod.setAllowUnfreeze(false);
                        paymentMethod.setAllowUpdate(false);
                        paymentMethod.setAllowDelete(false);
                    } else if (rec.getValueAsLong("CG.STATUS").intValue() == enums.PaymentMethodStatus.CLOSED.ordinal()) {
                        paymentMethod.setStatusValue(enums.PaymentMethodStatus.CLOSED);
                        paymentMethod.setAllowFreeze(false);
                        paymentMethod.setAllowUnfreeze(false);
                        paymentMethod.setAllowUpdate(false);
                        paymentMethod.setAllowDelete(false);
                    }

                }
                ListOfPaymentMethods.add(paymentMethod);
            }

        }

        System.out.println("inside get list of getViewPaymentMethodsQuickActions method");
        return ListOfPaymentMethods;
    }


    private boolean getBooleanValue(Object val) {
        if (val == null) {
            return false;
        }

        if (val instanceof Double) {
            return ((Double) val).doubleValue() == 1.0;
        }
        if (val instanceof BigDecimal) {
            return Long.valueOf(((BigDecimal) val).longValue()) == 1L;
        }
        return false;
    }

}