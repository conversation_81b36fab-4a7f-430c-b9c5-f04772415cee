package com.cit.vericash.apis.becustomerManagement;

import com.cit.vericash.api.commons.dto.BECustomerDto;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.dto.response.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.becustomerManagement.SearchBECustomerActionRequestTransformer")
public class SearchBECustomerActionRequestTransformer implements VericashAction {
    @Autowired
    SearchBECustomersAction searchBECustomersAction;

    @Override
    public Object process(Request request) throws Exception {
        Response response = null;

        try {
            List<BECustomerDto> bECustomerDto = new ArrayList<BECustomerDto>();
            bECustomerDto = searchBECustomersAction.vBECustomersService.getBeCustomers(request, false);
            return bECustomerDto;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
