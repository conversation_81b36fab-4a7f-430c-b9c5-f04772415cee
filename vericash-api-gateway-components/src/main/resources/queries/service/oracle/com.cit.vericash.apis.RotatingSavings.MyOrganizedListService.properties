getMyOrganizedList=SELECT rs.ID,rs.STATUS, rs.TOTAL_AMOUNT ,rs.CURRENCY,rs.SUBSCRIPTION_START_DATE,rs.SUBSCRIPTION_END_DATE, \
  rs.CONTRIBUTION_AMOUNT, rs.CONTRIBUTION_FREQUENCY,rs.NUMBER_MEMBERS,rsf.NAME AS RSF_NAME,rs.END_DATE \
  FROM ROTATING_SAVINGS rs \
  INNER JOIN ROTATING_SAVINGS_FREQUENCY rsf ON rs.CONTRIBUTION_FREQUENCY = rsf.ID \
  WHERE rs.ORGANIZER = %1$s AND ROWNUM <= %2$s ORDER BY rs.CREATION_DATE DESC
rotatingSavingsQuery_elseif=SELECT rs.ID,rs.STATUS, rs.TOTAL_AMOUNT ,rs.CURRENCY,\
  rs.SUBSCRIPTION_START_DATE,rs.SUBSCRIPTION_END_DATE, rs.CONTRIBUTION_AMOUNT,\
  \ rs.CONTRIBUTION_FREQUENCY,rs.NUMBER_MEMBERS,rsf.NAME AS RSF_NAME,rs.END_DATE \
  FROM ROTATING_SAVINGS rs \
  INNER JOIN ROTATING_SAVINGS_FREQUENCY rsf ON rs.CONTRIBUTION_FREQUENCY = rsf.ID \
  WHERE rs.ORGANIZER = %1$s ORDER BY rs.CREATION_DATE DESC
buildRotatingSavings=SELECT COUNT(*) AS NUM FROM ROTATING_SAVINGS_MEMBERS rsm INNER JOIN ROTATING_SAVINGS rs ON rs.ID =rsm.ROTATING_SAVINGS_ID  \
  WHERE rsm.CUSTOMER_ID = %1$s AND rsm.ROTATING_SAVINGS_ID = %2$s
numberOfMembersQuery=SELECT COUNT(rsm.ID) AS num FROM ROTATING_SAVINGS_MEMBERS rsm  INNER JOIN ROTATING_SAVINGS_MEM_TURNS rsmt ON rsmt.MEMBER_ID =rsm.ID \
  INNER JOIN ROTATING_SAVINGS rs ON rsm.ROTATING_SAVINGS_ID =rs.ID \
  \ where   (rsmt.STATUS =0 OR  rsmt.STATUS =1 OR rsmt.STATUS =3 ) AND rsm.ROTATING_SAVINGS_ID = %1$s

