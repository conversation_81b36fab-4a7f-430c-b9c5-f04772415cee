package com.cit.vericash.apis.addbeneficiary;

import com.cit.mpaymentapp.common.paymentmethod.DynamicParametersDTO;
import com.cit.mpaymentapp.model.Enums;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.commons.dao.PaymentMethodServiceDAO;
import com.cit.vericash.api.entity.customer.Customer;
import com.cit.vericash.api.repository.customer.CustomerRepository;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

@Component
@Qualifier("com.cit.vericash.apis.addbeneficiary.ViewBeneficiary")
public class ViewBeneficiary implements VericashAction {
    private static final String ALIAS ="ALIAS" ;
    private static final String BENEFICIARY_USER_ID ="BENEFICIARY_USER_ID" ;
    private static final String FILE = "bankAccountCodes";
    private static final String FOLDER = "application";
    private static final String PROPERTY = "codes";
    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    ConnectionUtil connectionUtil;
    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    @Autowired
    PaymentMethodServiceDAO paymentMethodServiceDAO;
    List<Map<String, Object>> userBeneficiaries;
    Map<String, List<Map<String, Object>>> result;
    Map<Integer, String> filterValues;
    String codes;

    @PostConstruct
    void init(){
        filterValues=new HashMap<>();
        filterValues.put(0,BENEFICIARY_USER_ID);
        filterValues.put(2,ALIAS);
        codes=propertyLoaderComponent.getPropertyAsString(PROPERTY,FOLDER,FILE);
    }

    @Override
    public Object process(Request request) throws Exception {
        userBeneficiaries = new ArrayList<>();
        result = new HashMap<>();
        String senderIdentifyValue = request.getMessage().getPayload().getAttributeAsString("senderIdentifyValue");
        Integer filter = request.getMessage().getPayload().getAttributeAsInteger("filter");

        Long userId = getUserId(senderIdentifyValue);
        try {
            String sqlQuery;

            if(filterValues.containsKey(filter))
                  sqlQuery = ServiceQueryEngine.getQueryStringToExecute("ViewBeneficiary", this.getClass(), String.valueOf(userId),filterValues.get(filter));
            else
                sqlQuery = ServiceQueryEngine.getQueryStringToExecute("ViewBeneficiaryPaymentMethod", this.getClass(), String.valueOf(userId),codes);

            List<Parameter> parameters = new ArrayList<>();
            List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters);

            if (!records.isEmpty()) {
                for (Record record : records) {
                    Map<String, Object> beneficiaryView = new HashMap<>();
                    Long paymentMethodType = Long.valueOf(record.getValueAsString("PAYMENT_METHOD_TYPE"));
                    beneficiaryView.put("beneficiaryId", record.getValueAsLong("ID"));
                    beneficiaryView.put("beneficiaryName", record.getValueAsString("NAME"));
                    beneficiaryView.put("paymentMethodType", record.getValueAsString("PAYMENT_METHOD_TYPE"));
                    beneficiaryView.put("paymentMethodName", record.getValueAsString("PAYMENT_METHOD_NAME"));
                    beneficiaryView.put("currency_code", record.getValueAsString("CURRENCY_CODE"));
                    beneficiaryView.put("CURRENCY_ID", record.getValueAsLong("CURRENCY_ID"));

                    if (filterValues.containsKey(filter)) {
                        beneficiaryView.put("alias", record.getValueAsString(ALIAS));
                        beneficiaryView.put("beneficiaryUserId", record.getValueAsBigDecimal(BENEFICIARY_USER_ID));
                        beneficiaryView.put("countryCode", record.getValueAsString("COUNTRY_CODE"));
                        beneficiaryView.put("mobileNumber", record.getValueAsString("MOBILE_NUMBER")
                                .replace('+'+record.getValueAsString("COUNTRY_CODE"),""));

                    }
                    String inputParameters = record.getValueAsString("INPUT_PARAMETERS");
                    DynamicParametersDTO[] inputParameterDTOS = new Gson().fromJson(inputParameters, DynamicParametersDTO[].class);
                    List<DynamicParametersDTO> inputParameterList = Arrays.asList(inputParameterDTOS);
                    inputParameterList.forEach(inputParameterDTO -> beneficiaryView.put(inputParameterDTO.getParameterCode().toString(), inputParameterDTO.getParameterValue()));
                    Long groupId = !inputParameterList.isEmpty() ? paymentMethodServiceDAO.getGruopIdByPaymentMethodType(paymentMethodType) : null;
                    String groupName = !inputParameterList.isEmpty() ? paymentMethodServiceDAO.getGruopNameByPaymentMethodType(paymentMethodType) : null;
                    Long type = paymentMethodServiceDAO.getPaymentMethodType(paymentMethodType);
                    beneficiaryView.put("groupId", groupId);
                    beneficiaryView.put("groupName", groupName);
                    beneficiaryView.put("receiverType", returnReceiverType(type));
                    userBeneficiaries.add(beneficiaryView);

                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
        result.put("userBeneficiaries", userBeneficiaries);
        return result;
    }

    @Cacheable(cacheManager = "userIds", key = "#userId")
    public Long getUserId(String userId) throws Exception {
        Optional<Customer> byUserId = customerRepository.findByUserId(userId);
        return byUserId.orElseThrow(() -> new GeneralFailureException(GeneralFailureException.GENERAL_ERROR)).getCustomerId();
    }

    private Long returnReceiverType(Long type) {
        if (type.equals((long) Enums.PaymentMethodType.PREPAID_CARD.ordinal()))
            return 1L;
        else if (type.equals((long) Enums.PaymentMethodType.STORED_VALUE_ACCOUNT.ordinal()))
            return 2L;
        else return 0L;
    }
}
