ReleaseDayService=SELECT * FROM ( SELECT ROTATING_SAVINGS. ID, ROTATING_SAVINGS.CURRENT_TURN, \
  SUM ( ROTATING_SAVINGS_PAYMENT_HIST.CONTRIBUTION_AMOUNT ) AS TOTAL_AMOUNT, \
  ROTATING_SAVINGS_MEM_TURNS.TURN_DAY, CUSTOMER.WALLET_SHORT_CODE, \
  ROTATING_SAVINGS_MEM_TURNS.RECEIVING_ACCOUNT, CUSTOMER_GAR.PAYMENT_METHOD_TYPE, \
  CUSTOMER.WALLET_ID, ROW_NUMBER () OVER ( ORDER BY ROTATING_SAVINGS. ID, ROTATING_SAVINGS.CURRENT_TURN, \
  ROTATING_SAVINGS_PAYMENT_HIST.CONTRIBUTION_AMOUNT, ROTATING_SAVINGS_MEM_TURNS.RECEIVING_ACCOUNT, \
  ROTATING_SAVINGS_MEM_TURNS.TURN_DAY, CUSTOMER.WALLET_SHORT_CODE, CUSTOMER_GAR.PAYMENT_METHOD_TYPE, \
  CUSTOMER.WALLET_ID ) rn \
  FROM ROTATING_SAVINGS_PAYMENT_HIST \
  INNER JOIN ROTATING_SAVINGS_MEMBERS ON ROTATING_SAVINGS_PAYMENT_HIST.MEMBER_ID = ROTATING_SAVINGS_MEMBERS. ID \
  INNER JOIN ROTATING_SAVINGS_MEM_TURNS ON ROTATING_SAVINGS_PAYMENT_HIST.MEMBER_ID = ROTATING_SAVINGS_MEM_TURNS.MEMBER_ID \
  INNER JOIN ROTATING_SAVINGS ON ROTATING_SAVINGS_MEM_TURNS.TURN_NUMBER = ROTATING_SAVINGS.CURRENT_TURN \
  INNER JOIN CUSTOMER ON ROTATING_SAVINGS_MEMBERS.CUSTOMER_ID = CUSTOMER.CUSTOMER_ID \
  INNER JOIN CUSTOMER_GAR ON ROTATING_SAVINGS_MEM_TURNS.RECEIVING_ACCOUNT = CUSTOMER_GAR.GARID \
  WHERE ROTATING_SAVINGS_PAYMENT_HIST.PAYMENT_STATUS IN (1, 2, 4) \
  AND ROTATING_SAVINGS_MEM_TURNS.RECEIVED_PAYMENT = 0 \
  AND ROTATING_SAVINGS.STATUS = 3 \
  GROUP BY ROTATING_SAVINGS. ID, ROTATING_SAVINGS.CURRENT_TURN, \
  ROTATING_SAVINGS_PAYMENT_HIST.CONTRIBUTION_AMOUNT, \
  ROTATING_SAVINGS_MEM_TURNS.RECEIVING_ACCOUNT, ROTATING_SAVINGS_MEM_TURNS.TURN_DAY, \
  CUSTOMER.WALLET_SHORT_CODE, CUSTOMER_GAR.PAYMENT_METHOD_TYPE, CUSTOMER.WALLET_ID ) \
  WHERE RN BETWEEN %1$s AND %2$s
getContributionHoldDays=SELECT CONTRIBUTION_HOLD_DAYS FROM ROTATING_SAVINGS_CONFIGURATION \
  WHERE WALLET_ID= %1$s
getSourcePaymentMethod=SELECT CUSTOMER_GAR.GARID, CUSTOMER_GAR.PAYMENT_METHOD_TYPE FROM CUSTOMER_GAR \
  INNER JOIN PAYMENT_METHOD ON CUSTOMER_GAR.PAYMENT_METHOD_TYPE = PAYMENT_METHOD.ID \
  WHERE PAYMENT_METHOD.TYPE = 7 AND PAYMENT_METHOD.BUSINESS_ENTITY_ID = %1$s \
  AND CUSTOMER_GAR.CUSTOMER_ID = %2$s
getBusinessServiceId=SELECT ID FROM BUSINESS_SERVICE_CONFIG WHERE BUSINESS_SERVICE_TYPE = 100043


