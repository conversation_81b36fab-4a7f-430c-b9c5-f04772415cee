package com.cit.vericash.apis.activatecustomer;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.activatecustomer.ActivateCustomerResponseTransformer")
public class ActivateCustomerResponseTransformer implements MuleResponseTransformer {

    public Object transform(BusinessMessage businessMessage) throws Exception {
        // TODO Auto-generated method stub

        Map<String, String> result = new HashMap<String, String>();


        return result;
    }

}
