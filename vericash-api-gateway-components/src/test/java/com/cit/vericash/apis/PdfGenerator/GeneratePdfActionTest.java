package com.cit.vericash.apis.PdfGenerator;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.printpdf.AliasResolver;
import com.cit.vericash.printpdf.FieldConfig;
import com.cit.vericash.util.ConfigFileResolver;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;

import java.io.File;
import java.nio.file.Files;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GeneratePdfActionTest {

    @Mock
    Environment environment;
    @Mock
    ConnectionUtil connectionUtil;
    @Mock
    ConfigFileResolver configFileResolver;
    @Mock
    PropertyLoaderComponent propertyLoaderComponent;
    @Mock
    AliasResolver aliasResolver;

    @InjectMocks
    GeneratePdfAction generatePdfAction;

    private static final String rootFolder = System.getProperty("java.io.tmpdir");
    private static final String folderName = "shareReceipt";
    private static final String keyName = "printBills";
    private static final String defaultKeyName = "printDefault";
    private static final String msisdn = "0100001111";
    private static final String templateHTML = "<html>{REPLACE_CONTENT}</html>";

    @BeforeEach
    void setup() throws Exception {
        java.lang.reflect.Field templateHTMLField = GeneratePdfAction.class.getDeclaredField("templateHTML");
        templateHTMLField.setAccessible(true);
        templateHTMLField.set(generatePdfAction, templateHTML);

        java.lang.reflect.Field shareReceiptField = GeneratePdfAction.class.getDeclaredField("SHARE_RECEIPT_FOLDER_NAME");
        shareReceiptField.setAccessible(true);
        shareReceiptField.set(null, folderName);

        File folder = new File(rootFolder, folderName);
        folder.mkdirs();
        File configFile = new File(folder, keyName + ".json");
        Files.writeString(configFile.toPath(),
                "{\"fields\":[{\"alias\":\"amount\",\"html\":\"<span>{amount}</span>\"}]}");

        File defaultConfigFile = new File(folder, defaultKeyName + ".json");
        Files.writeString(defaultConfigFile.toPath(),
                "{\"fields\":[{\"alias\":\"amount\",\"html\":\"<span>{amount}</span>\"}]}");
    }

    @AfterEach
    void cleanup() throws Exception {
        File configFile = new File(rootFolder, folderName + "/" + keyName + ".json");
        if (configFile.exists()) configFile.delete();

        File defaultConfigFile = new File(rootFolder, folderName + "/" + defaultKeyName + ".json");
        if (defaultConfigFile.exists()) defaultConfigFile.delete();
    }

    @Test
    void testProcess_Successful() throws Exception {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("TransactionId", "12345");
        payload.put("msisdn", msisdn);
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        Record serviceCodeRecord = new Record();
        serviceCodeRecord.put("PARENT_SERVICE_CODE", "SCODE");
        serviceCodeRecord.put("SERVICE_TYPE_NAME", "STYPE");

        Record transactionRecord = new Record();

        when(connectionUtil.executeSelect(anyString(), anyList()))
                .thenReturn(List.of(serviceCodeRecord))
                .thenReturn(List.of(transactionRecord));

        Properties properties = new Properties();
        properties.put("SCODE", "SCODE.json," + keyName);
        when(propertyLoaderComponent.loadAllPropertiesFromFile(any(), any())).thenReturn(properties);

        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);
        when(aliasResolver.resolveAlias(any(), any(), any(), any())).thenReturn("100");

        Object result = generatePdfAction.process(request);

        assertNotNull(result);
        Map<?,?> map = (Map<?,?>) result;
        assertTrue(map.containsKey("html"));
        assertFalse(((String) map.get("html")).isEmpty());
    }

    @Test
    void testProcess_PropertyDefaultKey() throws Exception {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("TransactionId", "12346");
        payload.put("msisdn", msisdn);
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        Record serviceCodeRecord = new Record();
        serviceCodeRecord.put("PARENT_SERVICE_CODE", "UNKNOWN");
        serviceCodeRecord.put("SERVICE_TYPE_NAME", "STYPE");

        Record transactionRecord = new Record();

        when(connectionUtil.executeSelect(anyString(), anyList()))
                .thenReturn(List.of(serviceCodeRecord))
                .thenReturn(List.of(transactionRecord));

        Properties properties = new Properties();
        properties.put(defaultKeyName, defaultKeyName + ".json," + defaultKeyName);
        when(propertyLoaderComponent.loadAllPropertiesFromFile(any(), any())).thenReturn(properties);

        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);
        when(aliasResolver.resolveAlias(any(), any(), any(), any())).thenReturn("100");

        Object result = generatePdfAction.process(request);

        assertNotNull(result);
        Map<?,?> map = (Map<?,?>) result;
        assertTrue(map.containsKey("html"));
    }

    @Test
    void testProcess_ThrowsApiExceptionOnGenerateHtmlFailure() throws Exception {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("TransactionId", "12347");
        payload.put("msisdn", msisdn);
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        Record serviceCodeRecord = new Record();
        serviceCodeRecord.put("PARENT_SERVICE_CODE", "SCODE");
        serviceCodeRecord.put("SERVICE_TYPE_NAME", "STYPE");

        Record transactionRecord = new Record();

        when(connectionUtil.executeSelect(anyString(), anyList()))
                .thenReturn(List.of(serviceCodeRecord))
                .thenReturn(List.of(transactionRecord));

        Properties properties = new Properties();
        properties.put("SCODE", "SCODE.json," + keyName);
        when(propertyLoaderComponent.loadAllPropertiesFromFile(any(), any())).thenReturn(properties);

        when(configFileResolver.getVericashRootFolder()).thenReturn("/invalid/path");

        APIException ex = assertThrows(APIException.class, () -> generatePdfAction.process(request));
        assertEquals(ErrorCode.SOMETHING_WENT_WRONG_IN_GENERATING_PDF.getErrorCode(), ex.getErrorCode());
    }

    @Test
    void testProcess_PropertyWithoutComma_BackwardCompatibility() throws Exception {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("TransactionId", "12348");
        payload.put("msisdn", msisdn);
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        Record serviceCodeRecord = new Record();
        serviceCodeRecord.put("PARENT_SERVICE_CODE", "SCODE");
        serviceCodeRecord.put("SERVICE_TYPE_NAME", "STYPE");

        Record transactionRecord = new Record();

        // Use lenient() to avoid unnecessary stubbing exception
        lenient().when(connectionUtil.executeSelect(anyString(), anyList()))
                .thenReturn(List.of(serviceCodeRecord))
                .thenReturn(List.of(transactionRecord));

        Properties properties = new Properties();
        properties.put("SCODE", "SCODE.json," + keyName);
        when(propertyLoaderComponent.loadAllPropertiesFromFile(any(), any())).thenReturn(properties);

        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);
        lenient().when(aliasResolver.resolveAlias(any(), any(), any(), any())).thenReturn("100");

        Object result = generatePdfAction.process(request);

        assertNotNull(result);
        Map<?,?> map = (Map<?,?>) result;
        assertTrue(map.containsKey("html"));
    }
    @Test
    void testExecuteQuery_ReturnsRecord() {
        Record rec = new Record();
        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(List.of(rec));
        Record out = generatePdfAction.executeQuery("printBills", "txnId");
        assertSame(rec, out);
    }

    @Test
    void testExecuteQuery_ThrowsApiException() {
        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(Collections.emptyList());
        APIException ex = assertThrows(APIException.class, () ->
                generatePdfAction.executeQuery("printBills", "txnId")
        );
        assertEquals(ErrorCode.NONE_FOUND_TRANSACTION_ID.getErrorCode(), ex.getErrorCode());
    }

    @Test
    void testGetField_Success() {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("TransactionId", "abc");
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        String result = invokePrivateGetField(generatePdfAction, request, "TransactionId");
        assertEquals("abc", result);
    }

    @Test
    void testGetField_ExceptionReturnsEmpty() {
        Request request = mock(Request.class);
        when(request.getMessage()).thenThrow(new RuntimeException("fail"));
        String result = invokePrivateGetField(generatePdfAction, request, "TransactionId");
        assertEquals("", result);
    }

    @Test
    void testGetServiceCode_ReturnsData() throws Exception {
        Record rec = new Record();
        rec.put("PARENT_SERVICE_CODE", "SCODE");
        rec.put("SERVICE_TYPE_NAME", "STYPE");
        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(List.of(rec));
        Map<String, String> result = invokePrivateGetServiceCode(generatePdfAction, "12345");
        assertEquals("SCODE", result.get("serviceCode"));
        assertEquals("STYPE", result.get("serviceType"));
    }

    @Test
    void testGetServiceCode_ThrowsApiException() {
        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(Collections.emptyList());
        APIException ex = assertThrows(APIException.class,
                () -> invokePrivateGetServiceCode(generatePdfAction, "12345")
        );
        assertEquals(ErrorCode.BUSINESS_SERVICE_NOT_FOUND.getErrorCode(), ex.getErrorCode());
    }

    @Test
    void testGenerateHTMLBase64() throws Exception {
        Map<String, String> serviceCodeName = new HashMap<>();
        serviceCodeName.put("serviceCode", "SCODE");
        serviceCodeName.put("serviceType", "STYPE");

        Record record = new Record();

        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);
        when(aliasResolver.resolveAlias(any(), any(), any(), any())).thenReturn("100");

        java.lang.reflect.Method generateHTMLBase64 = GeneratePdfAction.class.getDeclaredMethod(
                "generateHTMLBase64", Record.class, Map.class, String.class, String.class);
        generateHTMLBase64.setAccessible(true);

        Object result = generateHTMLBase64.invoke(
                generatePdfAction, record, serviceCodeName, keyName, msisdn);

        assertNotNull(result);
        assertTrue(((Map<?, ?>) result).containsKey("html"));
    }

    private static String invokePrivateGetField(GeneratePdfAction action, Request request, String field) {
        try {
            java.lang.reflect.Method m = GeneratePdfAction.class.getDeclaredMethod("getField", Request.class, String.class);
            m.setAccessible(true);
            return (String) m.invoke(action, request, field);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static Map<String, String> invokePrivateGetServiceCode(GeneratePdfAction action, String txnId) {
        try {
            java.lang.reflect.Method m = GeneratePdfAction.class.getDeclaredMethod("getServiceCode", String.class);
            m.setAccessible(true);
            return (Map<String, String>) m.invoke(action, txnId);
        } catch (Exception e) {
            Throwable cause = e.getCause() != null ? e.getCause() : e;
            if (cause instanceof APIException) throw (APIException) cause;
            throw new RuntimeException(e);
        }
    }
    @Test
    void testProcess_PropertyEmpty_UsesDefault() throws Exception {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("TransactionId", "12349");
        payload.put("msisdn", msisdn);
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        Record serviceCodeRecord = new Record();
        serviceCodeRecord.put("PARENT_SERVICE_CODE", "UNKNOWN_CODE");
        serviceCodeRecord.put("SERVICE_TYPE_NAME", "STYPE");

        Record transactionRecord = new Record();

        when(connectionUtil.executeSelect(anyString(), anyList()))
                .thenReturn(List.of(serviceCodeRecord))
                .thenReturn(List.of(transactionRecord));

        Properties properties = new Properties();
        properties.put("UNKNOWN_CODE", ""); // Empty property
        when(propertyLoaderComponent.loadAllPropertiesFromFile(any(), any())).thenReturn(properties);

        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);
        when(aliasResolver.resolveAlias(any(), any(), any(), any())).thenReturn("100");

        Object result = generatePdfAction.process(request);

        assertNotNull(result);
        Map<?,?> map = (Map<?,?>) result;
        assertTrue(map.containsKey("html"));
    }

    @Test
    void testGetServiceCode_UsesServiceCodeWhenParentIsNull() {
        Record rec = new Record();
        rec.put("SERVICE_CODE", "DIRECT_CODE");
        rec.put("PARENT_SERVICE_CODE", null); // Null parent service code
        rec.put("SERVICE_TYPE_NAME", "STYPE");

        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(List.of(rec));

        Map<String, String> result = invokePrivateGetServiceCode(generatePdfAction, "12345");
        assertEquals("DIRECT_CODE", result.get("serviceCode"));
        assertEquals("STYPE", result.get("serviceType"));
    }

    @Test
    void testWriteTemplateJsonFile_EmptyHtmlPart() throws Exception {
        String base64Html = Base64.getEncoder().encodeToString(
                "<div class='flex-container'><div class='left-text'>Status</div><div class='right-text'>{STATUS}</div></div>".getBytes()
        );
        Map<String, String> attributeMap = new HashMap<>();
        attributeMap.put("Status", "STATUS");

        File tempFile = File.createTempFile("test", ".json");
        tempFile.deleteOnExit();

        java.lang.reflect.Method method = GeneratePdfAction.class.getDeclaredMethod(
                "writeTemplateJsonFile", String.class, Map.class, java.nio.file.Path.class
        );
        method.setAccessible(true);

        method.invoke(generatePdfAction, base64Html, attributeMap, tempFile.toPath());

        assertTrue(tempFile.exists());
        assertTrue(tempFile.length() > 0);
    }

    @Test
    void testExtractNameAttribute_NoNameFound() throws Exception {
        java.lang.reflect.Method method = GeneratePdfAction.class.getDeclaredMethod(
                "extractNameAttribute", String.class
        );
        method.setAccessible(true);

        String htmlWithoutName = "<div class='right-text'>{STATUS}</div>";
        String result = (String) method.invoke(generatePdfAction, htmlWithoutName);

        assertEquals("", result);
    }

    @Test
    void testExtractAliasFromCurlyBraces_NoAliasFound() throws Exception {
        java.lang.reflect.Method method = GeneratePdfAction.class.getDeclaredMethod(
                "extractAliasFromCurlyBraces", String.class
        );
        method.setAccessible(true);

        String htmlWithoutBraces = "<div class='left-text'>Status</div>";
        String result = (String) method.invoke(generatePdfAction, htmlWithoutBraces);

        assertEquals("", result);
    }

    @Test
    void testGetTemplateHTMLBase64_ApiCodeExists() throws Exception {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("apiCode", keyName);
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        Properties properties = new Properties();
        properties.put(keyName, "exists");
        when(propertyLoaderComponent.loadAllPropertiesFromFile(any(), any())).thenReturn(properties);
        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);

        Object result = generatePdfAction.getTemplateHTMLBase64(request);

        assertNotNull(result);
        assertTrue(result instanceof Map);
    }

    @Test
    void testGetTemplateHTMLBase64_ApiCodeNotExists() throws Exception {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("apiCode", "nonExistentCode");
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        Properties properties = new Properties();
        when(propertyLoaderComponent.loadAllPropertiesFromFile(any(), any())).thenReturn(properties);
        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);

        Object result = generatePdfAction.getTemplateHTMLBase64(request);

        assertNotNull(result);
        assertTrue(result instanceof Map);
    }

    @Test
    void testUpdateOrSetTemplate_NewTemplate() throws Exception {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("apiCode", "newTemplate");
        payload.put("html", Base64.getEncoder().encodeToString("<div>test</div>".getBytes()));
        payload.put("attribute", new HashMap<String, String>());
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        Properties properties = new Properties();
        when(propertyLoaderComponent.loadAllPropertiesFromFile(any(), any())).thenReturn(properties);
        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);

        Object result = generatePdfAction.updateOrSetTemplate(request);

        assertEquals("Template created successfully", result);
        verify(propertyLoaderComponent).savePropertiesToFile(any(), any(), any());
    }

    @Test
    void testUpdateOrSetTemplate_ExistingTemplate() throws Exception {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("apiCode", keyName);
        payload.put("html", Base64.getEncoder().encodeToString("<div>test</div>".getBytes()));
        payload.put("attribute", new HashMap<String, String>());
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        Properties properties = new Properties();
        properties.put(keyName, "exists");
        when(propertyLoaderComponent.loadAllPropertiesFromFile(any(), any())).thenReturn(properties);
        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);

        Object result = generatePdfAction.updateOrSetTemplate(request);

        assertEquals("Template updated successfully", result);
    }

    @Test
    void testGetDefaultTemplateHTMLBase64() throws Exception {
        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);

        Object result = generatePdfAction.getDefaultTemplateHTMLBase64();

        assertNotNull(result);
        assertTrue(result instanceof Map);
    }

    @Test
    void testUpdateDefaultTemplate() throws Exception {
        Request request = mock(Request.class);
        Message message = mock(Message.class);
        Payload payload = new Payload();
        payload.put("html", Base64.getEncoder().encodeToString("<div>test</div>".getBytes()));
        payload.put("attribute", new HashMap<String, String>());
        when(request.getMessage()).thenReturn(message);
        when(message.getPayload()).thenReturn(payload);

        when(configFileResolver.getVericashRootFolder()).thenReturn(rootFolder);

        Object result = generatePdfAction.updateDefaultTemplate(request);

        assertEquals("Default Template updated successfully", result);
    }
}