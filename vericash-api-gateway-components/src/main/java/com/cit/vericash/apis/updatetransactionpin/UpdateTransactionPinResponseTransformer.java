package com.cit.vericash.apis.updatetransactionpin;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.updatetransactionpin.UpdateTransactionPinResponseTransformer")
public class UpdateTransactionPinResponseTransformer implements MuleResponseTransformer {

    public Object transform(BusinessMessage arg0) throws Exception {
        Map<String, String> result = new HashMap<String, String>();

        return result;
    }

}
