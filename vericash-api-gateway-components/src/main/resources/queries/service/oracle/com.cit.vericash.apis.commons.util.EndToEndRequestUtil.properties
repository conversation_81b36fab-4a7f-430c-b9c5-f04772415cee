saveEndToEndRequest=insert into END_TO_END_TIME_REQUEST\
  (REQUEST_ID,LAST_MODIFIED_DATE,FAILEDREASON,SENDER_MSISDN,\
  ID,SERVICE_CODE,CREATION_DATE,AMOUNT,STATUS,LEG,REQUEST_PART1\
  ,REQUEST_PART2,RESPONSE_PART1,RESPONSE_PART2,CHANNEL,TRACEID\
  ,RETRIEVAL_ID,TRANSACTION_REFERENCE_ID,SESSION_ID, \
  CUSTOMER_AGENT_ID, SENDER_WALLET_SHORT_CODE,AUTHENTICATION_TYPE) \
  values(END_TO_END_SEQ.nextval, TO_TIMESTAMP(?, 'yyyy-MM-dd HH24:MI:SS:FF'),?,?,?,?,\
  TO_TIMESTAMP(?, 'yyyy-MM-dd HH24:MI:SS:FF'),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
updateEndToEndRequest=update END_TO_END_TIME_REQUEST \
  set LAST_MODIFIED_DATE=TO_TIMESTAMP(?,'yyyy-MM-dd HH24:MI:SS:FF'), \
  ID=?, STATUS=?, SENDER_MSISDN=?, FAILEDREASON=?, SERVICE_CODE=?, \
  RESPONSE_PART1=?, RESPONSE_PART2=? where REQUEST_ID=?
