package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ParametersMap;
import com.cit.mpaymentapp.common.message.StatusType;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.service.commons.codeMapping.PaymentMethodDto;
import com.cit.service.commons.codeMapping.ServiceCodeMappingComponent;
import com.cit.vericash.api.components.LoadPaymentMethodComponent;
import com.cit.vericash.api.components.PaymentMethodServicePreprationComponent;
import com.cit.vericash.api.components.SMEConfigLoaderComponent;
import com.cit.vericash.api.components.impl.LoadTransformWalletServiceComponent;
import com.cit.vericash.api.components.impl.PrepareTransformCommonsComponent;
import com.cit.vericash.api.entity.GroupTransferSetup;
import com.cit.vericash.api.repository.GroupTransferHistoryRepository;
import com.cit.vericash.apis.commons.api.MuleMessageSender;
import com.cit.vericash.apis.commons.exceptions.MuleException;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.security.authentication.AuthorizedValidationStep;
import com.cit.vericash.config.VericashBackendConfig;
import com.google.gson.Gson;
import org.apache.commons.jxpath.JXPathContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.CalculateBulkFeesCommonService")
public class CalculateBulkFeesCommonService {

    final static String parentCode = "100050";
    @Autowired
    ConnectionUtil connectionUtil;
    @Autowired
    LoadTransformWalletServiceComponent loadTransformWalletServiceComponent;
    @Autowired
    PrepareTransformCommonsComponent prepareTransformCommonsComponent;
    @Autowired
    SMEConfigLoaderComponent smeConfigLoader;
    @Autowired
    LoadPaymentMethodComponent loadPaymentMethodComponent;
    @Autowired
    AuthorizedValidationStep authorizedValidationStep;
    @Autowired
    VericashBackendConfig config;
    @Autowired
    ServiceCodeMappingComponent serviceCodeMappingComponent;
    @Autowired
    PaymentMethodServicePreprationComponent paymentMethodServicePreprationComponent;
    @Autowired
    GroupTransferHistoryRepository groupTransferHistoryRepository;
    @Autowired
    private MuleMessageSender muleMessageSender;
    @Autowired
    private CachableVericashApi vericashApiCache;

    public Object calculateFee(Request request, List<Record> recipientsRecords) throws Exception {
        String apiCode = "";
        apiCode = request.getMessage().getHeader().getAttributeAsString("apiCode");
        VericashAPI validateSourceAccountStep = new VericashAPI();
        VericashAPI calculateBulkFeesServiceStep = new VericashAPI();
        BusinessMessage businessMessage = new BusinessMessage();
        //validate source account step
        if (apiCode.equals("2296") || apiCode.equals("2315") || apiCode.equals("2316")) {
            validateSourceAccountStep = vericashApiCache.getVericashAPI(2303l);
            businessMessage = getRecipientsAndCalculateFee(request, validateSourceAccountStep, recipientsRecords);
        }
        //calculateBulkFeesServiceStep step
        else if (apiCode.equals("3002")) {
            calculateBulkFeesServiceStep = vericashApiCache.getVericashAPI(2312l);
            businessMessage = getRecipientsAndCalculateFee(request, calculateBulkFeesServiceStep, recipientsRecords);
        }

        return businessMessage;
    }

    public BusinessMessage getRecipientsAndCalculateFee(Request request, VericashAPI vericashAPI, List<Record> recipientRecords) throws Exception {
        String apiCode = request.getMessage().getHeader().getAttributeAsString("apiCode");
        GroupTransferSetup groupTransferSetup = (GroupTransferSetup) request.getMessage().getPayload().get("groupTransferSetup");

        //initialize variables
        Message message = request.getMessage();
        BusinessMessage businessMessage = new BusinessMessage();
        BigDecimal totalRecipientsAmount = BigDecimal.valueOf(0);
        String groupName = "";

        //load group transfer setup id from request
        Integer groupTransferSetupId = request.getMessage().getPayload().getAttributeAsInteger("groupSetupId");

        //load group user id from request
        String userId = request.getMessage().getHeader().getAttributeAsString("userId");

        Long recipientCount = Long.valueOf(recipientRecords.size());

        if (recipientRecords == null) {
            throw new MuleException("VAL0009", "No recipients found for group transfer setup id" + groupTransferSetupId);
        }

        if (recipientRecords.get(0).getValueAsString("GROUP_NAME") != null) {
            groupName = recipientRecords.get(0).getValueAsString("GROUP_NAME");
        }
        PaymentMethodDto sourcePaymentMethod = new PaymentMethodDto();

        if (apiCode.equals("2296") || apiCode.equals("3002")) {

            //load source payment method from request
            LinkedHashMap<String, LinkedHashMap<String, Integer>> sourcePaymentMethodMap = (LinkedHashMap<String, LinkedHashMap<String, Integer>>) message.getPayload().getAttribute("sourcePaymentMethod");

            String sourcePaymentMethodType = String.valueOf(sourcePaymentMethodMap.get("paymentMethodType"));
            Long sourcePaymentMethodCode = Long.valueOf(String.valueOf(sourcePaymentMethodMap.get("paymentMethodCode")));
            String currency = String.valueOf(String.valueOf(sourcePaymentMethodMap.get("currency")));

            sourcePaymentMethod.setPaymentMethodType(sourcePaymentMethodType);
            sourcePaymentMethod.setPaymentMethodCode(sourcePaymentMethodCode);
            sourcePaymentMethod.setCurrency(currency);
        } else {
            sourcePaymentMethod.setPaymentMethodType(groupTransferSetup.getPaymentMethodType());
            sourcePaymentMethod.setPaymentMethodCode(groupTransferSetup.getPaymentMethodCode());
        }
        message.getPayload().setAttribute("sourcePaymentMethod", sourcePaymentMethod);

        BigDecimal accountId = null;

        if (sourcePaymentMethod != null) {
            //Source Account Id Query
            String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getRecipientsAndCalculateFee" , this.getClass() ,String.valueOf(sourcePaymentMethod.getPaymentMethodCode()));
/*
            String accountIdQuery = "SELECT ACCOUNT_ID  FROM CUSTOMER_GAR  WHERE GARID = " + sourcePaymentMethod.getPaymentMethodCode();
*/
            List<Parameter> accountIdParameters = new ArrayList<>();
            List<String> accountIdFields = new ArrayList<>();
            accountIdFields = setAccountIdFields(accountIdFields);
            List<Record> accountIdRecords = connectionUtil.executeSelect(sqlQuery, accountIdParameters, accountIdFields);

            if (accountIdRecords != null) {
                accountId = accountIdRecords.get(0).getValueAsBigDecimal("ACCOUNT_ID");
            }
        }
        for (Record record : recipientRecords) {
            totalRecipientsAmount = totalRecipientsAmount.add(record.getValueAsBigDecimal("AMOUNT"));
            businessMessage = getBusinessMessage(record, message, vericashAPI.getCode().toString(), accountId, null, groupName, recipientCount
                    , totalRecipientsAmount);
        }

        //set business message needed parameters for first step
        businessMessage.getParameters().put("totalRecipientsAmount", totalRecipientsAmount);
        businessMessage.getParameters().put("recipientsList", new Gson().toJson(recipientRecords));
        businessMessage.getParameters().put("groupTransferId", new Gson().toJson(groupTransferSetupId));

        //outbound to validate source account step
        businessMessage = (BusinessMessage) muleMessageSender.sendMessageWithJson(businessMessage, vericashAPI);

        //set total fees and total vat retrieved from mule
        BigDecimal totalFees = businessMessage.getParameters().getAttributeAsBigDecimal("totalFees");
        BigDecimal totalVat = businessMessage.getParameters().getAttributeAsBigDecimal("totalVat");

        businessMessage.getParameters().put("totalFees", totalFees);
        businessMessage.getParameters().put("totalVat", totalVat);


        //get status type
        StatusType statusType = businessMessage.getStatus();

        //check if account has sufficient balance or any error from mule
        if (statusType.getErrorFlag() || statusType.getStatusCode() != null || statusType.getStatusMsg() != null || statusType.getStatusSource() != null) {
            if (statusType.getErrorFlag() && statusType.getStatusCode() != null) {
                throw new MuleException(statusType.getStatusCode(), statusType.getStatusMsg());
            }
        }
        return businessMessage;
    }

    //load business message needed data for mule steps
    private BusinessMessage getBusinessMessage(Record record, Message message, String apiCode, BigDecimal accountId, Long groupTransferHistoryId, String groupName, Long recipientListSize, BigDecimal totalRecipientsAmount) throws Exception {
        Payload payload = message.getPayload();
        Long userId = message.getHeader().getAttributeAsLong("userId");

        //Set Group Transfer Recipient Attribute
        if (record != null) {
            Long id = record.getValueAsLong("TRANSFER_ID");
            payload.setAttribute("groupTransferRecipientId", id);
        }

        if (record != null) {
            BigDecimal amount = record.getValueAsBigDecimal("AMOUNT");
            payload.setAttribute("amount", amount);
        }
        if (record != null) {
            BigDecimal customerFeeProfileId = record.getValueAsBigDecimal("CUSTOMER_FEE_PROFILE_ID");
            payload.setAttribute("customerFeeProfileId", customerFeeProfileId);
        }

        payload.setAttribute("accountId", accountId);
        payload.setAttribute("groupTransferHistoryId", groupTransferHistoryId);
        payload.setAttribute("groupName", groupName);
        payload.setAttribute("numOfReceivers", recipientListSize);
        payload.setAttribute("totalRecipientsAmount", totalRecipientsAmount);

        //Set Payment Method Attributes
        if (record != null) {
            Long paymentMethodCode = record.getValueAsLong("GAR_ID");
            BigDecimal paymentMethodTypeBig = record.getValueAsBigDecimal("PAYMENT_METHOD_TYPE");

            String paymentMethodType = paymentMethodTypeBig.toString();
            PaymentMethodDto destinationPaymentMethod = new PaymentMethodDto();
            destinationPaymentMethod.setPaymentMethodCode(paymentMethodCode);
            destinationPaymentMethod.setPaymentMethodType(paymentMethodType);
            payload.setAttribute("destinationPaymentMethod", destinationPaymentMethod);
        }

        //Set Api Code Attribute
        message.getHeader().setAttribute("apiCode", apiCode);

        message.setPayload(payload);

        //Set Business Message Attributes
        BusinessMessage businessMessage = transformBusinessMessage(message);
        VericashAPI vericashAPI = vericashApiCache.getVericashAPI(Long.valueOf(apiCode));

        Long businessId = getBusinessId(vericashAPI);
        businessMessage.getServiceInfo().setId("" + businessId);

        //Set Wallet Attributes
        String walletShortCode = message.getHeader().getAttributeAsString("walletShortCode");
        Long walletId = message.getHeader().getAttributeAsLong("walletId");
        businessMessage.getHeader().setWalletId(walletId);
        businessMessage.getHeader().setWalletShortCode(walletShortCode);
        businessMessage.getHeader().setCustomerId(userId);


        //Set Pay Load to Business Message Parameters
        ParametersMap parameters = new ParametersMap();
        payload.remove("PMTransferType");
        payload.remove("groupTransferSetup");
        parameters.putAll(payload);
        System.out.println(parameters);

        businessMessage.setParameters(parameters);
        return businessMessage;
    }

    private Long getBusinessId(VericashAPI vericashAPI) {
/*
        String query = "SELECT ID FROM BUSINESS_SERVICE_CONFIG WHERE NAME= " + "'" + vericashAPI.getName() + "'";
*/
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getBusinessId" , this.getClass() ,String.valueOf(vericashAPI.getName()));
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields.add("ID");
        List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters, fields);
        Long id = records.get(0).getValueAsLong("ID");
        return id;
    }

    private List<String> setAccountIdFields(List<String> fields) {
        fields.add("ACCOUNT_ID");
        return fields;
    }


    private final BusinessMessage transformBusinessMessage(Message message) throws Exception {
        BusinessMessage businessMessage = null;

        try {
            Long apiCode = message.getHeader().getAttributeAsLong("apiCode");
            VericashAPI vericashAPI = vericashApiCache.getVericashAPI(apiCode);

            businessMessage = new BusinessMessage();
            JXPathContext targetObject = JXPathContext.newContext(businessMessage);

            smeConfigLoader.prepareSMEBusinessMessage(message, targetObject);
            loadPaymentMethodComponent.loadPaymentMethodUsingOptions(message, targetObject);
            paymentMethodServicePreprationComponent.LoadPaymentMethodsTypes(message, vericashAPI);
            loadTransformWalletServiceComponent.loadTransformWalletService(vericashAPI, null, message, targetObject, businessMessage);
            prepareTransformCommonsComponent.prepareTransformationToBusinessMessage(message, targetObject);
        } catch (Exception e) {
            throw e;
        }
        return businessMessage;
    }

}