package com.cit.vericash.apis.AdsService.FilterResponsabilityChain;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.cit.vericash.api.entity.Ad;
import com.cit.vericash.apis.AdsService.segmentation.strategies.GroupedSegmentationStrategy;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.AdditionalData;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.EchoData;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.mastercard.api.core.exception.ApiException;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class SegmentationTypeFilterTest {

    @Test
    @DisplayName("Test getters and setters")
    void testGettersAndSetters() {
        // Arrange
        GroupedSegmentationStrategy segmentStrategy = new GroupedSegmentationStrategy();

        // Act
        SegmentationTypeFilter actualSegmentationTypeFilter = new SegmentationTypeFilter(segmentStrategy,
                new LinkedHashMap<>());
        actualSegmentationTypeFilter.setNext(new AudienceTypeFilter(new LinkedHashMap<>()));
    }

    @Test
    @DisplayName("Test handleRequest(Request, Stream)")
    void testHandleRequest() throws ApiException {
        // Arrange
        GroupedSegmentationStrategy segmentStrategy = new GroupedSegmentationStrategy();

        SegmentationTypeFilter segmentationTypeFilter = new SegmentationTypeFilter(segmentStrategy, new LinkedHashMap<>());
        GroupedSegmentationStrategy segmentStrategy2 = new GroupedSegmentationStrategy();
        segmentationTypeFilter.setNext(new SegmentationTypeFilter(segmentStrategy2, new LinkedHashMap<>()));

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(new Payload());

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        ArrayList<Ad> adList = new ArrayList<>();
        Stream<Ad> adStream = adList.stream();

        // Act
        Stream<Ad> actualHandleRequestResult = segmentationTypeFilter.handleRequest(request, adStream);

        // Assert
        assertTrue(actualHandleRequestResult.limit(5).collect(Collectors.toList()).isEmpty());
    }

    @Test
    @DisplayName("Test handleRequest(Request, Stream)")
    void testHandleRequest2() throws ApiException {
        // Arrange
        GroupedSegmentationStrategy segmentStrategy = new GroupedSegmentationStrategy();

        SegmentationTypeFilter segmentationTypeFilter = new SegmentationTypeFilter(segmentStrategy, new LinkedHashMap<>());
        segmentationTypeFilter.setNext(new PublishDateFilter(new LinkedHashMap<>()));

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(new Payload());

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        ArrayList<Ad> adList = new ArrayList<>();
        Stream<Ad> adStream = adList.stream();

        // Act
        Stream<Ad> actualHandleRequestResult = segmentationTypeFilter.handleRequest(request, adStream);

        // Assert
        assertTrue(actualHandleRequestResult.limit(5).collect(Collectors.toList()).isEmpty());
    }

    @Test
    @DisplayName("Test handleRequest(Request, Stream)")
    void testHandleRequest3() throws ApiException {
        // Arrange
        GroupedSegmentationStrategy segmentStrategy = new GroupedSegmentationStrategy();
        SegmentationTypeFilter segmentationTypeFilter = new SegmentationTypeFilter(segmentStrategy, new LinkedHashMap<>());

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(new Payload());

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        ArrayList<Ad> adList = new ArrayList<>();
        Stream<Ad> adStream = adList.stream();

        // Act
        Stream<Ad> actualHandleRequestResult = segmentationTypeFilter.handleRequest(request, adStream);

        // Assert
        assertTrue(actualHandleRequestResult.limit(5).collect(Collectors.toList()).isEmpty());
    }

    @Test
    @DisplayName("Test handleRequest(Request, Stream); then throw ApiException")
    void testHandleRequest_thenThrowApiException() throws ApiException {
        // Arrange
        SegmentationTypeFilter segmentationTypeFilter = new SegmentationTypeFilter(null, new LinkedHashMap<>());

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(new Payload());

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        ArrayList<Ad> adList = new ArrayList<>();
        Stream<Ad> adStream = adList.stream();

        // Act and Assert
        assertThrows(ApiException.class, () -> segmentationTypeFilter.handleRequest(request, adStream));
    }
}
