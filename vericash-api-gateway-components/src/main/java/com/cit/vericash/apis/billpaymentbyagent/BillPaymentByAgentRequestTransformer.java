package com.cit.vericash.apis.billpaymentbyagent;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.billpaymentbyagent.BillPaymentByAgentRequestTransformer")
public class BillPaymentByAgentRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    private final String bill_Key = "bill_key";
    private final String bill_Value = "bill_value";
    private final String billAttributesKey = "bill_attributes";
    private final String amountKey = "amount";
    private final String billAmountKey = "bill_amount";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

//	public BillPaymentByAgentRequestTransformer() {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
//	}

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        try {
            validationInRequest(message);
            //	populateSenderOwnerType(message);
            setParmeterInMessage(message);

        } catch (Exception e) {
            throw e;
        }
    }

/*	private void populateSenderOwnerType(Message message) throws Exception {
		try {
			
			String ownerTypeKey = cacheableFieldParameters.getRequestParameters(OWNERTYPE);
			String ownerTypeString = message.getPayload().getAttributeAsString(ownerTypeKey);
			
			if (!StringUtils.isBlank(ownerTypeString)) {
				OwnerTypeEnum ownerType = OwnerTypeEnum.None;

				switch (Integer.valueOf(ownerTypeString)) {
				case 0: {
					ownerType = OwnerTypeEnum.None;
					break;
				}
				case 1: {
					ownerType = OwnerTypeEnum.CUSTOMER;
					break;
				}
				case 2:
				case 3:
				case 4:
				case 5:
				case 6: {
					ownerType = OwnerTypeEnum.BUSINESSENTITY;
					break;
				}
				default: {
					ownerType = OwnerTypeEnum.None;
					break;
				}
				}

				message.getPayload().put("senderOwnerType", ownerType);
			}
		} catch (Exception e) {
			throw e;
		}
	}*/


    /**
     * This method used to get information for Agent
     *
     * @param message
     * @return void
     * @throws Exception
     */
    private void setParmeterInMessage(Message message) throws Exception {

        String billKey = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(bill_Key));
        String billValue = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(bill_Value));
        Map<String, String> billAttributes = new HashMap<String, String>();
        billAttributes.put(billKey, billValue);
        message.getPayload().put(cacheableFieldParameters.getRequestParameters(billAttributesKey), billAttributes);

    }


    private void validationInRequest(Message message) throws Exception {
        String transactionAmount = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(amountKey));
        String billAmount = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(billAmountKey));
        if (!transactionAmount.equals(billAmount)) {
            throw new APIException("Transaction amount  must  equal  bill amount ", ErrorCode.TRANSACTION_AMOUNT_SHOULD_EQUAL_BILL_AMOUNT.getErrorCode());
        }


    }

}