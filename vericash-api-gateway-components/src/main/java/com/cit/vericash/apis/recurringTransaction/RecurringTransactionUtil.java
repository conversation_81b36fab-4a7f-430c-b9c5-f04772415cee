package com.cit.vericash.apis.recurringTransaction;

import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import oracle.sql.TIMESTAMP;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.recurringTransaction.RecurringTransactionUtil")
public class RecurringTransactionUtil {
    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;


    public TransactionRecurringDTO addTransactionRecurringDTO(Record record) throws SQLException, JsonProcessingException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ObjectMapper objectMapper = new ObjectMapper();
        TransactionRecurringDTO recurringMonthTransactionDTO = new TransactionRecurringDTO();
        recurringMonthTransactionDTO.setId(record.getValueAsLong("ID"));
        recurringMonthTransactionDTO.setTitle(record.getValueAsString("NAME"));
        recurringMonthTransactionDTO.setType(record.getValueAsString("TYPE"));
        Object endTimeChecker = record.get("END_TIME");
        Object repeatCountChecker = record.get("REPEAT_COUNT");
        if (endTimeChecker == null && repeatCountChecker == null) {
            recurringMonthTransactionDTO.setExpiry(ExpiryType.None.name());
            recurringMonthTransactionDTO.setExpiry(null);
        } else {
            if (endTimeChecker != null && repeatCountChecker == null) {
                recurringMonthTransactionDTO.setExpiry(ExpiryType.UntilSpecificDate.name());
                TIMESTAMP timeStamp = (TIMESTAMP) record.get("END_TIME");
                Date date = new Date();
                date.setTime(timeStamp.timestampValue().getTime());
                String strDate = formatter.format(date);
                recurringMonthTransactionDTO.setExpiryValue(strDate);
            } else {
                recurringMonthTransactionDTO.setExpiry(ExpiryType.NumberOfRecurrence.name());
                recurringMonthTransactionDTO.setExpiryValue(record.get("REPEAT_COUNT"));
            }
        }
        TIMESTAMP timeStamp = (TIMESTAMP) record.get("START_TIME");
        Date date = new Date();
        date.setTime(timeStamp.timestampValue().getTime());
        String strDate = formatter.format(date);
        recurringMonthTransactionDTO.setStartDate(strDate);
        recurringMonthTransactionDTO.setFrequency(record.getValueAsString("FREQUENCY"));
        recurringMonthTransactionDTO.setStatus(record.getValueAsString("STATUS"));
        String json = record.getValueAsString("DETAILS");
        if (json == null || json.equals("")) {
            recurringMonthTransactionDTO.setDetails(null);
        } else {
            Map<Object, Object> mapJson = objectMapper.readValue(json, HashMap.class);

/*            Gson gson = new Gson();

            Map<String,String> mapJson = gson.fromJson(json, (Type) Object.class);*/
            recurringMonthTransactionDTO.setDetails(mapJson);
        }

        String allowedNumber = record.getValueAsString("STATUS");
        System.out.println("Number " + allowedNumber);
        if (allowedNumber.equals("Active")) {
            String propertyload = "0.scheduled.recurring.allowed.actions";
            String allowedAction = propertyLoaderComponent.getPropertyAsString(propertyload, "application", "application");
            String[] allowedActions = allowedAction.split(",");
            recurringMonthTransactionDTO.setAllowedActions(allowedActions);
        } else {
            recurringMonthTransactionDTO.setAllowedActions(null);
        }
        return recurringMonthTransactionDTO;

    }
}