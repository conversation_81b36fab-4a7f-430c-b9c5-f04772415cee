package com.cit.vericash.apis.EnquireTransactionInfo;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.TransactionDetails;
import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.mpaymentapp.model.transaction.AccountOwnerType;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.EnquireTransactionInfo.EnquireTransactionInfoResponseTransformer")
public class EnquireTransactionInfoResponseTransformer implements MuleResponseTransformer {
    private final String transactionAmount = "Transaction-Amount";
    private final String transactionFees = "Transaction-Fee-Amount";
    private final String Msisdn = "sender-msisdn";
    private final String ownerType = "owner-type";
    private final String serviceId = "service-type-id";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public EnquireTransactionInfoResponseTransformer() {
    }

    public Object transform(BusinessMessage businessMessage) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
        BigDecimal TxnAmount = new BigDecimal(0);
        BigDecimal TxnFee = new BigDecimal(0);
        TransactionDetails txnDetails = null;
        if (businessMessage.getTransactionDetails() != null) {
            txnDetails = businessMessage.getTransactionDetails();
        }
        if(txnDetails==null){
            throw new QuickActionException("mandatory value is missing", ErrorCode.MISSING_MANDATORY_VALUE);
        }
        TransactionInformation txnInfo = txnDetails.getTransactionInformation();
        if (txnInfo.getTransactionAmount() != null) {
            TxnAmount = txnInfo.getTransactionAmount();
        }
        if (txnInfo.getTransactionFeeAmount() != null) {
            TxnFee = txnInfo.getTransactionFeeAmount();
        }
        String senderMsisdn = txnDetails.getSenderMsisdn();
        AccountOwnerType senderOwnerType = txnDetails.getSenderOwnerType();
        String serviceTypeId = txnDetails.getServiceId();

        Map<String, String> result = new HashMap<String, String>();

        result.put(cacheableFieldParameters.getResponseParameters(transactionAmount), TxnAmount.toString());
        result.put(cacheableFieldParameters.getResponseParameters(transactionFees), TxnFee.toString());
        result.put(cacheableFieldParameters.getResponseParameters(Msisdn), senderMsisdn);
        result.put(cacheableFieldParameters.getResponseParameters(ownerType), senderOwnerType.getAccountOwnerType());
        result.put(cacheableFieldParameters.getResponseParameters(serviceId), serviceTypeId);


        return result;
    }

}
