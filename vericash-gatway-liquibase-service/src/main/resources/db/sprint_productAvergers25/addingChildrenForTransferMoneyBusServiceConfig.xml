<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet author="Sheri<PERSON> <PERSON>" id="Sherif_BUSINESS_SERVICE_CONFIG">
        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From CIB To Virtual Card"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Source Virtual To Source Virtual"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="0"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Source Virtual To Source Prepaid"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Source Virtual To Debit"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Source Virtual To CIB"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Source Virtual To Wallet"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="0"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>
		<insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Source prepaid to Source virtual card"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Source prepaid to CIBr"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="0"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Source prepaid to Debit"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From CIB To Prepaid"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From CIB To CIB"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="0"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From CIB to Debit Card"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From CIB To Wallet"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>
		<insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Debit to Source Virtual"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Debit Source Prepaid"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="0"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Debit to CIB"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Debit to Debit card"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money- Wallet to Source Virtual Card"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="0"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - Wallet to CIB"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>
		<insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer - Wallet to Source Debit"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="***************"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Send Money by Alias - From Wallet To CIB by Customer"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="*********"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From Debit to Wallet"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>

        <insert tableName="BUSINESS_SERVICE_CONFIG" schemaName="PRO_MI_NO_C">
            <column name="ID" valueNumeric="*********"/>
            <column name="BANKING_AGENT" value="0"/>
            <column name="BUSINESSSERVICECATEGORY" value="0"/>
            <column name="ISDEFAULTSERVICE" value="1"/>
            <column name="IS_EXPOSABLE" value="0"/>
            <column name="IS_MULTI_WALLET" value="0"/>
            <column name="NAME" value="Transfer Money - From CIB To Dest Prepaid by Customer"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" value="0"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="IS_ENABLED" value="1"/>
        </insert>
		






    </changeSet>

</databaseChangeLog>
