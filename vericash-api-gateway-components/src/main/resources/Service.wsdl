<?xml version='1.0' encoding='UTF-8'?><!-- Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2-hudson-740-. --><!-- Generated by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2-hudson-740-. -->
<definitions xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
             xmlns:wsp="http://www.w3.org/ns/ws-policy"
             xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://ws.waei.uba.com/"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.xmlsoap.org/wsdl/"
             targetNamespace="http://ws.waei.uba.com/" name="Service">
    <wsp:Policy wsu:Id="ServicePortBinding_MTOM_Policy-ServicePortBinding_MTOM_Policy">
        <ns1:OptimizedMimeSerialization
                xmlns:ns1="http://schemas.xmlsoap.org/ws/2004/09/policy/optimizedmimeserialization"
                wsp:Optional="true"/>
    </wsp:Policy>
    <types>
        <xsd:schema>
            <xsd:import namespace="http://ws.waei.uba.com/" schemaLocation="Service.xsd"/>
        </xsd:schema>
    </types>
    <message name="createNewCorporateEntrustUser">
        <part name="parameters" element="tns:createNewCorporateEntrustUser"/>
    </message>
    <message name="createNewCorporateEntrustUserResponse">
        <part name="parameters" element="tns:createNewCorporateEntrustUserResponse"/>
    </message>
    <message name="createNewOrUpdateUserQA">
        <part name="parameters" element="tns:createNewOrUpdateUserQA"/>
    </message>
    <message name="createNewOrUpdateUserQAResponse">
        <part name="parameters" element="tns:createNewOrUpdateUserQAResponse"/>
    </message>
    <message name="createNewEntrustUser">
        <part name="parameters" element="tns:createNewEntrustUser"/>
    </message>
    <message name="createNewEntrustUserResponse">
        <part name="parameters" element="tns:createNewEntrustUserResponse"/>
    </message>
    <message name="synchronizeToken">
        <part name="parameters" element="tns:synchronizeToken"/>
    </message>
    <message name="synchronizeTokenResponse">
        <part name="parameters" element="tns:synchronizeTokenResponse"/>
    </message>
    <message name="getOneSecurityQuestion">
        <part name="parameters" element="tns:getOneSecurityQuestion"/>
    </message>
    <message name="getOneSecurityQuestionResponse">
        <part name="parameters" element="tns:getOneSecurityQuestionResponse"/>
    </message>
    <message name="authenticateSecurityQuestion">
        <part name="parameters" element="tns:authenticateSecurityQuestion"/>
    </message>
    <message name="authenticateSecurityQuestionResponse">
        <part name="parameters" element="tns:authenticateSecurityQuestionResponse"/>
    </message>
    <message name="updateOrCreateuserContactInfo">
        <part name="parameters" element="tns:updateOrCreateuserContactInfo"/>
    </message>
    <message name="updateOrCreateuserContactInfoResponse">
        <part name="parameters" element="tns:updateOrCreateuserContactInfoResponse"/>
    </message>
    <message name="deleteUser">
        <part name="parameters" element="tns:deleteUser"/>
    </message>
    <message name="deleteUserResponse">
        <part name="parameters" element="tns:deleteUserResponse"/>
    </message>
    <message name="authenticateToken">
        <part name="parameters" element="tns:authenticateToken"/>
    </message>
    <message name="authenticateTokenResponse">
        <part name="parameters" element="tns:authenticateTokenResponse"/>
    </message>
    <message name="adaptiveAuthentication">
        <part name="parameters" element="tns:adaptiveAuthentication"/>
    </message>
    <message name="adaptiveAuthenticationResponse">
        <part name="parameters" element="tns:adaptiveAuthenticationResponse"/>
    </message>
    <message name="createEntrustUser">
        <part name="parameters" element="tns:createEntrustUser"/>
    </message>
    <message name="createEntrustUserResponse">
        <part name="parameters" element="tns:createEntrustUserResponse"/>
    </message>
    <message name="authenticateAdaptiveSecurityQuestion">
        <part name="parameters" element="tns:authenticateAdaptiveSecurityQuestion"/>
    </message>
    <message name="authenticateAdaptiveSecurityQuestionResponse">
        <part name="parameters" element="tns:authenticateAdaptiveSecurityQuestionResponse"/>
    </message>
    <message name="authenticateOTP">
        <part name="parameters" element="tns:authenticateOTP"/>
    </message>
    <message name="authenticateOTPResponse">
        <part name="parameters" element="tns:authenticateOTPResponse"/>
    </message>
    <message name="generateOTP">
        <part name="parameters" element="tns:generateOTP"/>
    </message>
    <message name="generateOTPResponse">
        <part name="parameters" element="tns:generateOTPResponse"/>
    </message>
    <message name="createAuthenticationSecreet">
        <part name="parameters" element="tns:createAuthenticationSecreet"/>
    </message>
    <message name="createAuthenticationSecreetResponse">
        <part name="parameters" element="tns:createAuthenticationSecreetResponse"/>
    </message>
    <message name="fetchAuthenticationSecreet">
        <part name="parameters" element="tns:fetchAuthenticationSecreet"/>
    </message>
    <message name="fetchAuthenticationSecreetResponse">
        <part name="parameters" element="tns:fetchAuthenticationSecreetResponse"/>
    </message>
    <message name="viewOTP">
        <part name="parameters" element="tns:viewOTP"/>
    </message>
    <message name="viewOTPResponse">
        <part name="parameters" element="tns:viewOTPResponse"/>
    </message>
    <portType name="Service">
        <operation name="createNewCorporateEntrustUser">
            <input wsam:Action="http://ws.waei.uba.com/Service/createNewCorporateEntrustUserRequest"
                   message="tns:createNewCorporateEntrustUser"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/createNewCorporateEntrustUserResponse"
                    message="tns:createNewCorporateEntrustUserResponse"/>
        </operation>
        <operation name="createNewOrUpdateUserQA">
            <input wsam:Action="http://ws.waei.uba.com/Service/createNewOrUpdateUserQARequest"
                   message="tns:createNewOrUpdateUserQA"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/createNewOrUpdateUserQAResponse"
                    message="tns:createNewOrUpdateUserQAResponse"/>
        </operation>
        <operation name="createNewEntrustUser">
            <input wsam:Action="http://ws.waei.uba.com/Service/createNewEntrustUserRequest"
                   message="tns:createNewEntrustUser"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/createNewEntrustUserResponse"
                    message="tns:createNewEntrustUserResponse"/>
        </operation>
        <operation name="synchronizeToken">
            <input wsam:Action="http://ws.waei.uba.com/Service/synchronizeTokenRequest" message="tns:synchronizeToken"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/synchronizeTokenResponse"
                    message="tns:synchronizeTokenResponse"/>
        </operation>
        <operation name="getOneSecurityQuestion">
            <input wsam:Action="http://ws.waei.uba.com/Service/getOneSecurityQuestionRequest"
                   message="tns:getOneSecurityQuestion"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/getOneSecurityQuestionResponse"
                    message="tns:getOneSecurityQuestionResponse"/>
        </operation>
        <operation name="authenticateSecurityQuestion">
            <input wsam:Action="http://ws.waei.uba.com/Service/authenticateSecurityQuestionRequest"
                   message="tns:authenticateSecurityQuestion"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/authenticateSecurityQuestionResponse"
                    message="tns:authenticateSecurityQuestionResponse"/>
        </operation>
        <operation name="updateOrCreateuserContactInfo">
            <input wsam:Action="http://ws.waei.uba.com/Service/updateOrCreateuserContactInfoRequest"
                   message="tns:updateOrCreateuserContactInfo"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/updateOrCreateuserContactInfoResponse"
                    message="tns:updateOrCreateuserContactInfoResponse"/>
        </operation>
        <operation name="deleteUser">
            <input wsam:Action="http://ws.waei.uba.com/Service/deleteUserRequest" message="tns:deleteUser"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/deleteUserResponse" message="tns:deleteUserResponse"/>
        </operation>
        <operation name="authenticateToken">
            <input wsam:Action="http://ws.waei.uba.com/Service/authenticateTokenRequest"
                   message="tns:authenticateToken"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/authenticateTokenResponse"
                    message="tns:authenticateTokenResponse"/>
        </operation>
        <operation name="adaptiveAuthentication">
            <input wsam:Action="http://ws.waei.uba.com/Service/adaptiveAuthenticationRequest"
                   message="tns:adaptiveAuthentication"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/adaptiveAuthenticationResponse"
                    message="tns:adaptiveAuthenticationResponse"/>
        </operation>
        <operation name="createEntrustUser">
            <input wsam:Action="http://ws.waei.uba.com/Service/createEntrustUserRequest"
                   message="tns:createEntrustUser"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/createEntrustUserResponse"
                    message="tns:createEntrustUserResponse"/>
        </operation>
        <operation name="authenticateAdaptiveSecurityQuestion">
            <input wsam:Action="http://ws.waei.uba.com/Service/authenticateAdaptiveSecurityQuestionRequest"
                   message="tns:authenticateAdaptiveSecurityQuestion"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/authenticateAdaptiveSecurityQuestionResponse"
                    message="tns:authenticateAdaptiveSecurityQuestionResponse"/>
        </operation>
        <operation name="authenticateOTP">
            <input wsam:Action="http://ws.waei.uba.com/Service/authenticateOTPRequest" message="tns:authenticateOTP"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/authenticateOTPResponse"
                    message="tns:authenticateOTPResponse"/>
        </operation>
        <operation name="generateOTP">
            <input wsam:Action="http://ws.waei.uba.com/Service/generateOTPRequest" message="tns:generateOTP"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/generateOTPResponse" message="tns:generateOTPResponse"/>
        </operation>
        <operation name="createAuthenticationSecreet">
            <input wsam:Action="http://ws.waei.uba.com/Service/createAuthenticationSecreetRequest"
                   message="tns:createAuthenticationSecreet"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/createAuthenticationSecreetResponse"
                    message="tns:createAuthenticationSecreetResponse"/>
        </operation>
        <operation name="fetchAuthenticationSecreet">
            <input wsam:Action="http://ws.waei.uba.com/Service/fetchAuthenticationSecreetRequest"
                   message="tns:fetchAuthenticationSecreet"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/fetchAuthenticationSecreetResponse"
                    message="tns:fetchAuthenticationSecreetResponse"/>
        </operation>
        <operation name="viewOTP">
            <input wsam:Action="http://ws.waei.uba.com/Service/viewOTPRequest" message="tns:viewOTP"/>
            <output wsam:Action="http://ws.waei.uba.com/Service/viewOTPResponse" message="tns:viewOTPResponse"/>
        </operation>
    </portType>
    <binding name="ServicePortBinding" type="tns:Service">
        <wsp:PolicyReference URI="#ServicePortBinding_MTOM_Policy-ServicePortBinding_MTOM_Policy"/>
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
        <operation name="createNewCorporateEntrustUser">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="createNewOrUpdateUserQA">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="createNewEntrustUser">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="synchronizeToken">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="getOneSecurityQuestion">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="authenticateSecurityQuestion">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="updateOrCreateuserContactInfo">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="deleteUser">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="authenticateToken">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="adaptiveAuthentication">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="createEntrustUser">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="authenticateAdaptiveSecurityQuestion">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="authenticateOTP">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="generateOTP">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="createAuthenticationSecreet">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="fetchAuthenticationSecreet">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="viewOTP">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>
    <service name="Service">
        <port name="ServicePort" binding="tns:ServicePortBinding">
            <soap:address location="http://*************:8181/UBAEntrustBridge/Service"/>
        </port>
    </service>
</definitions>