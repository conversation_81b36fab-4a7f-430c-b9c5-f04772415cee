package com.cit.vericash.apis.deleteBECustomer;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.dto.response.Response;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.deleteBECustomer.DeleteBECustomerRequestTransformer")
public class DeleteBECustomerRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    public Object process(Request request) throws Exception {
        Response response = null;

        try {

        } catch (Exception e) {

        }
        return null;
    }
}
