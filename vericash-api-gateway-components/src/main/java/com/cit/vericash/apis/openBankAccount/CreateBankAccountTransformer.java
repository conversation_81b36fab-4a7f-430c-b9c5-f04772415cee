package com.cit.vericash.apis.openBankAccount;


import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Qualifier("com.cit.vericash.apis.openBankAccount.createBankAccountTransformer")
public class CreateBankAccountTransformer extends MessageToBusinessMessageTransformerImpl {
	@Autowired
	ConnectionUtil connectionUtil;

	private final ObjectMapper objectMapper = new ObjectMapper();

	@Override
	protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
		validateRequest(message);

	}
	private void validateRequest(Message message) throws QuickActionException {
		String paymentMethodTypeId = message.getPayload().getAttributeAsString("sourcePaymentMethodType");

		validateOpenUponCustomerRequestFlag(paymentMethodTypeId);
		validateUploadedDocuments(message, paymentMethodTypeId);
	}

	List<Record> fetchPaymentMethodConfig(String paymentMethodTypeId, String queryName) {
		String sqlQuery = ServiceQueryEngine.getQueryStringToExecute(
				queryName,
				this.getClass(),
				paymentMethodTypeId
		);
		return connectionUtil.executeSelect(sqlQuery, new ArrayList<>());
	}

	public int parseRequiredDocuments(String jsonObj) {
		try {
			// Handle null or empty input case
			if (jsonObj == null || jsonObj.trim().isEmpty()) {
				return 0;
			}

			JsonNode documentsNode = objectMapper.readTree(jsonObj);

			// Ensure it's a valid JSON array
			if (documentsNode == null || !documentsNode.isArray()) {
				return 0;
			}

			int mandatoryCount = 0;
			for (JsonNode document : documentsNode) {
				if (document.has("mandatory") && document.get("mandatory").asInt() == 1) {
					mandatoryCount++;
				}
			}

			return mandatoryCount;
		} catch (Exception e) {
			return 0; // Return 0 for any parsing errors
		}
	}

	private void validateUploadedDocuments(Message message, String paymentMethodTypeId) throws QuickActionException {
		List<Record> records = fetchPaymentMethodConfig(paymentMethodTypeId, "get.services.payment.method.config");
		if (!records.isEmpty()) {
			String requiredData = records.get(0).getValueAsString("REQUIRED_DATA");
			int requiredDocumentCount = parseRequiredDocuments(requiredData);

			Object uploadedFiles = message.getPayload().get("fileDownloadURL");

			if (uploadedFiles == null) {
				if (requiredDocumentCount == 0) {
					return;
				}
			}  else if (uploadedFiles instanceof List<?>) {
				List<?> files = (List<?>) uploadedFiles;
				if (files.size() >= requiredDocumentCount) {
					return;
				}
			}
			throw new QuickActionException("Missing documents in the request!",
					ErrorCode.MISSING_DOCUMENTS_ERROR.getErrorCode());
		}

		throw new QuickActionException("Not allowed to use this payment method!", ErrorCode.OPEN_ACCOUNT_PM_NOT_CONFIGURED.getErrorCode());



	}

	private void validateOpenUponCustomerRequestFlag(String paymentMethodTypeId) throws QuickActionException {
		List<Record> records = fetchPaymentMethodConfig(paymentMethodTypeId, "get.source.payment.method.config");

		if (!records.isEmpty()) {
			Integer openedUponCustomerRequest = records.get(0).getValueAsInt("OPENED_UPON_CUS_REQ");
			if (openedUponCustomerRequest != null && openedUponCustomerRequest.equals(1)) {
				return;
			}
		}
		throw new QuickActionException("Not allowed to use this payment method to open an account!", ErrorCode.OPEN_ACCOUNT_PM_NOT_CONFIGURED.getErrorCode());
	}

}
