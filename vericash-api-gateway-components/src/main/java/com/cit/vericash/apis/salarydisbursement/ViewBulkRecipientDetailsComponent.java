package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.model.group.transfers.RecipientTransferStatus;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.commons.dto.GroupTransferRecipientExecDTO;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.ViewBulkRecipientDetailsComponent")
public class ViewBulkRecipientDetailsComponent implements VericashAction {
    @Autowired
    ConnectionUtil connectionUtil;
    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    ArrayList<GroupTransferRecipientExecDTO> failedRecipients;
    ArrayList<GroupTransferRecipientExecDTO> succeededRecipients;
    ArrayList<GroupTransferRecipientExecDTO> inProgressRecipients;
    HashMap<String, ArrayList<GroupTransferRecipientExecDTO>> result;
    Integer status;

    @Override
    public Object process(Request request) throws Exception {
        failedRecipients = new ArrayList<GroupTransferRecipientExecDTO>();
        succeededRecipients = new ArrayList<GroupTransferRecipientExecDTO>();
        inProgressRecipients = new ArrayList<GroupTransferRecipientExecDTO>();
        result = new HashMap<String, ArrayList<GroupTransferRecipientExecDTO>>();
        List<Parameter> parameters = new ArrayList<Parameter>();
        List<String> fields = new ArrayList<String>();
        Integer groupTransferHistoryId = request.getMessage().getPayload().getAttributeAsInteger("groupTransferHistoryId");
        fields = setFields(fields);

        if (groupTransferHistoryId != null) {
            StringBuilder query = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("ViewBulkRecipientDetailsComponent_process",this.getClass(),String.valueOf(groupTransferHistoryId)));
            List<Record> records = connectionUtil.executeSelect(query.toString(), parameters, fields);
            Record record = null;
            GroupTransferRecipientExecDTO groupTransferRecipientDTO = null;
            for (int i = 0; i < records.size(); i++) {
                record = records.get(i);
                ArrayList<GroupTransferRecipientExecDTO> groupTransferRecipientExecDTOS = buildGroupTransfer(record);
                if (groupTransferRecipientExecDTOS != null) {
                    if (status == 0) {
                        result.put("failedRecipients", groupTransferRecipientExecDTOS);
                    } else if (status == 1) {
                        result.put("succeededRecipients", groupTransferRecipientExecDTOS);
                    } else if (status == 2) {
                        result.put("inProgressRecipients", groupTransferRecipientExecDTOS);
                    }
                }
            }
            return result;
        }
        String errorMessage = propertyLoaderComponent.getPropertyAsString("error.group.transfer.missing.id", "application", "application");
        throw new GeneralFailureException(errorMessage);
    }

    private ArrayList<GroupTransferRecipientExecDTO> buildGroupTransfer(Record record) {
        GroupTransferRecipientExecDTO groupTransferRecipientDTO = new GroupTransferRecipientExecDTO();

        if (record.getValueAsDate("EXECUTION_DATE_TIME") != null)
            groupTransferRecipientDTO.setExecutionDateTime(record.getValueAsDate("EXECUTION_DATE_TIME"));
        if (record.getValueAsString("BENEFICIARY_NAME") != null)
            groupTransferRecipientDTO.setBeneficiaryName(record.getValueAsString("BENEFICIARY_NAME"));
        if (record.getValueAsLong("RECIPIENT_ID") != null)
            groupTransferRecipientDTO.setRecipientId(record.getValueAsLong("RECIPIENT_ID"));
        if (record.getValueAsLong("GROUP_TRANSFER_HISTORY_ID") != null)
            groupTransferRecipientDTO.setGroupTransferHistoryID(record.getValueAsLong("GROUP_TRANSFER_HISTORY_ID"));
        if (record.getValueAsString("RECEIVER_TYPE") != null)
            groupTransferRecipientDTO.setReceiverType(record.getValueAsString("RECEIVER_TYPE"));
        if (record.getValueAsBigDecimal("AMOUNT") != null)
            groupTransferRecipientDTO.setAmount(record.getValueAsBigDecimal("AMOUNT").doubleValue());
        if (record.getValueAsString("INSTITUTION_NAME") != null)
            groupTransferRecipientDTO.setInstitutionName(record.getValueAsString("INSTITUTION_NAME"));
        if (record.getValueAsString("BRANCH_NAME") != null)
            groupTransferRecipientDTO.setBranchName(record.getValueAsString("BRANCH_NAME"));
        if (record.getValueAsString("FAILURE_REASON") != null)
            groupTransferRecipientDTO.setFailureReason(record.getValueAsString("FAILURE_REASON"));

        if (record.getValueAsString("NAME") != null && record.getValueAsLong("ID") != null) {
            status = Math.toIntExact(record.getValueAsLong("ID"));
            RecipientTransferStatus recipientTransferStatus = new RecipientTransferStatus();
            recipientTransferStatus.setName(record.getValueAsString("NAME"));
            recipientTransferStatus.setId(record.getValueAsLong("ID"));
            groupTransferRecipientDTO.setTransferResult(recipientTransferStatus);
            if (status == 0) {
                failedRecipients.add(groupTransferRecipientDTO);
                return failedRecipients;
            } else if (status == 1) {
                succeededRecipients.add(groupTransferRecipientDTO);
                return succeededRecipients;
            } else if (status == 2) {
                inProgressRecipients.add(groupTransferRecipientDTO);
                return inProgressRecipients;
            }
        }
        return null;
    }

    private List<String> setFields(List<String> fields) {
        fields.add("RECIPIENT_ID");
        fields.add("AMOUNT");
        fields.add("GROUP_TRANSFER_HISTORY_ID");
        fields.add("EXECUTION_DATE_TIME");
        fields.add("BENEFICIARY_NAME");
        fields.add("RECEIVER_TYPE");
        fields.add("INSTITUTION_NAME");
        fields.add("BRANCH_NAME");
        fields.add("FAILURE_REASON");
        fields.add("NAME");
        fields.add("ID");
        return fields;
    }
}

