package com.cit.vericash.apis.freezepaymentmethodNewAPI;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.freezepaymentmethodNewAPI.NewFreezePaymentMethodRequestTransformer")
public class NewFreezePaymentMethodRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    private String sourcePaymentMethodKey = "source-Payment-Method";

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        //ObjectMapper mapperSource = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        // SourcePaymentMethod sourcePaymentMethod;
        //  Object SourcePaymentMethodObj = message.getPayload().get(cacheableFieldParameters.getRequestParameters(sourcePaymentMethodKey));
        // String sourceJson = ow.writeValueAsString(SourcePaymentMethodObj);
        // sourcePaymentMethod = mapperSource.readValue(sourceJson, SourcePaymentMethod.class);
    }


}
