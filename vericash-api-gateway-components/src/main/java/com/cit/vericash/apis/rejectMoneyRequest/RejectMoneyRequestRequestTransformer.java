package com.cit.vericash.apis.rejectMoneyRequest;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.rejectMoneyRequest.RejectMoneyRequestRequestTransformer")
public class RejectMoneyRequestRequestTransformer extends MessageToBusinessMessageTransformerImpl {

}
