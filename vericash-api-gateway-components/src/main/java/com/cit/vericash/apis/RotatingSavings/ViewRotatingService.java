package com.cit.vericash.apis.RotatingSavings;

import com.cit.mpaymentapp.common.rotatingService.ViewRotatingDTO;
import com.cit.mpaymentapp.common.rotatingService.ViewRotatingUtil;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.ViewRotatingService")
public class ViewRotatingService {
    public ViewRotatingDTO viewRotatingDTO(Request request) {
        ViewRotatingDTO viewRotatingDTO = new ViewRotatingDTO();
        String startDate = request.getMessage().getPayload().getAttributeAsString("startDate");
        int numberOfMembers = request.getMessage().getPayload().getAttributeAsInteger("numberOfMembers");
        int contributionAmount = request.getMessage().getPayload().getAttributeAsInteger("contributionAmount");
        String contributionFrequency = request.getMessage().getPayload().getAttributeAsString("contributionFrequency");
        viewRotatingDTO = ViewRotatingUtil.viewRotatingDTO(numberOfMembers, contributionAmount, startDate, contributionFrequency);

        return viewRotatingDTO;

    }
}