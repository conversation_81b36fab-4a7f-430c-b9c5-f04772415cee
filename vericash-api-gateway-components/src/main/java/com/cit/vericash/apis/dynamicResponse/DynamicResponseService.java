package com.cit.vericash.apis.dynamicResponse;

import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.LookupFramework.common.util.request.Filters;
import com.cit.vericash.LookupFramework.common.util.response.LookupResponse;
import com.cit.vericash.LookupFramework.manager.LookupFrameWorkManager;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import com.google.common.base.CaseFormat;

import java.util.*;

@Component
@Qualifier("com.cit.vericash.apis.dynamicResponse.DynamicResponseService")
public class DynamicResponseService {
    @Autowired
    LookupFrameWorkManager lookupFrameWorkManager;
    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    @Autowired
    DynamicCommons dynamicCommons;

    public LinkedHashMap<String, Object> getLookup(Request request) throws GeneralFailureException {
        Payload lookupData = new Payload();
        Payload payload = request.getMessage().getPayload();;
        LookupResponse lookupResponse;
        Map<String, String> lookupTypes = new HashMap<String, String>();
        String apiCode = request.getMessage().getHeader().getAttributeAsString("apiCode");
        String dynamicKeys = propertyLoaderComponent.getPropertyAsString(apiCode, "application", "dynamicResponse");
        if (dynamicKeys != null) {
            String[] dynamicArray = dynamicKeys.split(",");
            dynamicArray=trim(dynamicArray);
            ArrayList<String> dynamicArrayList = new ArrayList<>(Arrays.asList(dynamicArray));
            List dynamicList = Arrays.asList(dynamicArray);
            lookupData.setAttribute("lookups", dynamicList);
            lookupData.setAttribute("filters",payload);
            lookupResponse = lookupFrameWorkManager.getLookups(lookupData);
            if (lookupResponse.size() == 0) {
                throw new GeneralFailureException("There is a problem with your lookup please check your query");
            }
            lookupTypes = LookupExtractionTypes(lookupTypes, dynamicList);
            Set<String> keys = lookupResponse.keySet();
            LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
            resultMap = dynamicCommons.extractResultMap(lookupResponse, keys, resultMap);
            transformResult(resultMap, dynamicArrayList, lookupTypes);
            return resultMap;
        } else {
            throw new GeneralFailureException("Please add lookup key to your API code in dynamicResponse.properties file");
        }
    }

    private String[] trim(String[] dynamicArray) {
        for(int i = 0; i< dynamicArray.length; i++){
            dynamicArray[i]= dynamicArray[i].trim();
        }
        return dynamicArray;
    }

    private Map<String, String> LookupExtractionTypes(Map<String, String> lookupTypes, List dynamicList) throws GeneralFailureException {
        for (int i = 0; i < dynamicList.size(); i++) {
            String lookupKey = (String) dynamicList.get(i);
            String lookupType = propertyLoaderComponent.getPropertyAsString(lookupKey, "application", "dynamicResponseLookupTypes");
            if (lookupType == null || lookupType.equals("")) {
                throw new GeneralFailureException("Please set lookup type in dynamicResponseLookupTypes.properties file");
            }
            lookupTypes.put(lookupKey, lookupType);
        }
        return lookupTypes;
    }

    public LinkedHashMap<String, Object> transformResult(LinkedHashMap<String, Object> resultMap, ArrayList<String> dynamicList, Map<String, String> lookupTypes) {
        for (String key : dynamicList) {
            DynamicResponseLookupTypes dynamicResponseLookupType = DynamicResponseLookupTypes.valueOf(lookupTypes.get(key).toUpperCase());
            ArrayList<LinkedTreeMap<String, Object>> records = (ArrayList<LinkedTreeMap<String, Object>>) resultMap.get(key);

            //SINGLE VALUE
            if (dynamicResponseLookupType.equals(DynamicResponseLookupTypes.SINGLE_VALUE)) {
                Set<String> keys = records.get(0).keySet();
                Object[] keySet = keys.toArray();
                Object value = records.get(0).get(keySet[0].toString());
                resultMap.put(key, value);
            }
            //OBJECT
            if (dynamicResponseLookupType.equals(DynamicResponseLookupTypes.OBJECT)) {
                Object objectValue=null;
                //no records
                if(records.size()!=0){
                     objectValue = records.get(0);
                }
                resultMap.put(key, objectValue);
            }

        }
        // default is DynamicResponseLookupTypes.LIST
        return resultMap;
    }


    public ArrayList<LinkedTreeMap<String, Object>> transform(ArrayList<LinkedTreeMap<String, Object>> records) {
        for (int i = 0; i < records.size(); i++) {
            LinkedTreeMap<String, Object> map = records.get(i);
            List<String> keyList = new ArrayList<String>(map.keySet());
            for (int j = 0; j < keyList.size(); j++) {
                String key = keyList.get(j);
                Object value = map.get(key);
                map.remove(key);
                key = key.replaceAll("\\.", "_");
                key = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, key);
                map.put(key, value);
            }
            records.set(i, map);
        }
        return records;
    }


}
