//package com.cit.vericash.util;
//
//import com.cit.service.commons.codeMapping.ProjectConfigLoader;
//import com.cit.service.commons.codeMapping.ProjectConfigLoaderImpl;
//import com.cit.vericash.apis.commons.exceptions.ApiValidationException;
//import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
//import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
//import com.cit.vericash.apis.commons.util.connection.Record;
//import com.cit.vericash.apis.dto.request.Request;
//import com.cit.vericash.backend.commons.dynamicpayload.AdditionalData;
//import com.cit.vericash.backend.commons.dynamicpayload.Header;
//import com.cit.vericash.backend.commons.dynamicpayload.Message;
//import com.cit.vericash.backend.commons.dynamicpayload.Payload;
//import org.junit.After;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Spy;
//import org.mockito.runners.MockitoJUnitRunner;
//import org.springframework.context.annotation.Description;
//
//TODO: fix me

//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
//import static org.junit.Assert.*;
//import static org.mockito.Mockito.*;
//
//@RunWith(MockitoJUnitRunner.class)
//public class ApplicationNameValidatorTest {
//    @Mock
//    ConnectionUtil connectionUtil;
//    @Spy
//    ProjectConfigLoaderImpl projectConfigLoader;
//    @InjectMocks
//    PropertyLoaderComponent propertyLoaderComponent = spy(PropertyLoaderComponent.class);
//    @InjectMocks
//    ApplicationNameValidator applicationNameValidator;
//    private final Header header = new Header();
//
//    private final Payload payload = new Payload();
//    private final AdditionalData additionalData = new AdditionalData();
//
//    private final Message message = new Message();
//    private final Request request = new Request();
//    List<Record> records = new ArrayList<>();
//    @Before
//    public void setUp() throws Exception {
//
//        Record record =  new Record();
//        record.put("APPLICATION_NAME", "VERICASH");
//
//        records.add(record);
//        message.setHeader(header);
//        message.setAdditionalData(additionalData);
//        message.setPayload(payload);
//
//        request.setMessage(message);
//    }
//
//    @After
//    public void tearDown() throws Exception {
//    }
//
////    @Test
////    @Description("test that should throw exception for not having device info")
////    public void validateApplicationName() {
////        when(connectionUtil.executeSelect(any(), anyList(), anyList())).thenReturn(records);
////        header.put("applicationName", "sadasd");
////        try {
////            applicationNameValidator.validateApplicationName(request);
////        }catch (ApiValidationException ex){
////            assertEquals("deviceInfo cannot be null", ex.getStatusMsgList().get(0));
////        }
////    }
//}