package com.cit.vericash.apis.transfermoney;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ParametersMap;
import com.cit.mpaymentapp.common.paymentmethod.DestinationPaymentMethodDTO;
import com.cit.mpaymentapp.common.paymentmethod.SourcePaymentMethodDTO;
import com.cit.mpaymentapp.model.Enums;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.receipt.PaymentMethodAccountIdentifier;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class TransferMoneyResponseTransformer implements MuleResponseTransformer {

    @Autowired
    PaymentMethodAccountIdentifier paymentMethodAccountIdentifier;

    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, Object> result = new HashedMap();
        ObjectMapper mapper = new ObjectMapper();
        // String balance = businessMessage.getSourcePaymentMethod().getPaymentBalance();
        // result.put("paymentBalance", balance);
        if (!Objects.isNull(businessMessage.getDestinationPaymentMethod())) {
            String receiverIdentifier = paymentMethodAccountIdentifier.getPaymentMethodAccountNumber
                    (businessMessage.getDestinationPaymentMethod().getPaymentMethodType(),
                            businessMessage.getDestinationPaymentMethod().getDynamicGroup().getInputParametersMap(),
                            null, businessMessage.getDestinationPaymentMethod().getPaymentMethodCode()
                    );
            ParametersMap newParametersMap = businessMessage.getParameters();
            newParametersMap.put("Receiver_Identifier", receiverIdentifier);
            businessMessage.setParameters(newParametersMap);
        }

        if (businessMessage.getDestinationPaymentMethod() != null &&
                businessMessage.getDestinationPaymentMethod().getOwnerRegistrationType().equals(Enums.OwnerRegistrationType.REGISTERED)) {
            DestinationPaymentMethodDTO destinationPaymentMethod = businessMessage.getDestinationPaymentMethod();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            destinationPaymentMethod.setDynamicGroup(null);

            result.put("destinationPaymentMethod", destinationPaymentMethod);

        }
        SourcePaymentMethodDTO sourcePaymentMethod = businessMessage.getSourcePaymentMethod();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        sourcePaymentMethod.setDynamicGroup(null);
        //sourcePaymentMethod.setIntegrationType(Enums.IntegrationType.EXTERNAL);
        result.put("sourcePaymentMethod", sourcePaymentMethod);
        return result;
    }
}
