package com.cit.vericash.apis.getBillPaymentCategories;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.mongo.beans.BillerCategory;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class GetBillPaymentCategoriesResponseTransformer implements MuleResponseTransformer {
    private final String categoriesKey = "categories";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    private String interswitchCategories = "interswitchCategories";

    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        Gson gson = new Gson();
        try {

            if (businessMessage.getSoftFields().get(interswitchCategories) != null) {
                List<BillerCategory> categories = (List<BillerCategory>) businessMessage.getSoftFields().get(interswitchCategories);
                result.put(cacheableFieldParameters.getResponseParameters(categoriesKey), categories);

            }

        } catch (Exception e) {
            throw e;
        }

        return result;
    }
}



