package com.cit.vericash.apis.RotatingSavings;

import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.ReleaseDayResponseTransformer")
public class ReleaseDayResponseTransformer implements VericashAction {


    @Override
    public Object process(Request request) throws Exception {
        return null;
    }
}