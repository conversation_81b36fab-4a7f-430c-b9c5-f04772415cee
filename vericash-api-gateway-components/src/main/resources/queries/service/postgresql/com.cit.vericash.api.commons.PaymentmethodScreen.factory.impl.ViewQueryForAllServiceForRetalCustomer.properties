ViewQueryForAllServiceForRetalCustomer_query=SELECT PM.NAME AS \"paymentMethodName\", SPM.ID \"paymentMethodType\",\
  \ PMT.NAME \"paymentMethodTypeName\",CG.GARID, SPMS.API_CODE, SPMS.USER_TYPE , PM.PAYMETN_METHOD_ORDER  \
  FROM CUSTOMER C \
  JOIN CUSTOMER_GAR CG ON C.CUSTOMER_ID = CG.CUSTOMER_ID  \
  JOIN PAYMENT_METHOD PM ON PM.ID = CG.PAYMENT_METHOD_TYPE \
  JOIN SOURCE_PAYMENT_METHOD SPM ON SPM.ID = PM.ID \
  LEFT JOIN PAYMENT_METHOD_TYPE PMT ON PMT.ID = PM.\"TYPE\" \
  left join SERVICES_PAYMENTMETHOD  SPMS on  SPM.ID =SPMS.PAYMENMETHODTYPE and SPMS.GAR_STATUS IS NOT null \
  and CG.STATUS in ( SPMS.GAR_STATUS ) \
  LEFT JOIN BUSINESS_ENTITY_HIERARCHY BEH ON BEH.BUSINESS_ENTITY_ID = PM.BUSINESS_ENTITY_ID \
  WHERE  C.USER_ID = ?  AND   SPMS.LOOKUP_ENUM = ?  AND  SPMS.USER_TYPE = ? 
ViewQueryForAllServiceForRetalCustomer_ifcondition1=AND   SPMS.API_CODE = ?
ViewQueryForAllServiceForRetalCustomer_ifcondition2=AND   SPMS.API_CODE in ( 
ViewQueryForAllServiceForRetalCustomer_forLoop=? , 
ViewQueryForAllServiceForRetalCustomer=? ) 

