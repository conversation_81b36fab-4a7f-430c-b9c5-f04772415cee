//package com.cit.vericash.apis.commons.util;
//
//import static org.mockito.Matchers.anyLong;
//import static org.mockito.Matchers.anyString;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.when;
//
//import java.io.File;
//import java.util.ArrayList;
//import java.util.List;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.BDDMockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import com.cit.vericash.apis.commons.model.VericashAPI;
//
//import com.cit.vericash.apis.dto.request.Header;
//import com.cit.vericash.backend.commons.model.message.Message;
//import com.cit.vericash.backend.commons.model.message.Payload;
//import com.cit.vericash.apis.dto.request.Record;
//import com.cit.vericash.apis.dto.request.Request;
//import com.cit.vericash.apis.dto.request.ResultSet;
//import com.cit.vericash.apis.dto.response.Response;
//import com.cit.vericash.apis.dto.response.ResponseStatus;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({JsonUtil.class,FileUtils.class})
//public class ApiDocumentationServiceTest {
//	@Test
//	public void test_ApiDocumentation_createDocumentationFile() throws Exception {
//
//		CachableVericashApi vericashApiCache = mock(CachableVericashApi.class);
//		ProjectConfigLoader projectConfigLoader = mock(ProjectConfigLoader.class);
//		 PowerMockito.mockStatic(JsonUtil.class);
//		 PowerMockito.mockStatic(FileUtils.class);
//
//
//
//
//
//
//		VericashAPI vericashAPI = initVericashAPI();
//		Request request = initRequest();
//		Response response = initResponse();
//
//		String userHomeDir = "E:\\Vericash-apis\\vericash-apis-config\\vericash-apis-config";
//		String projectNameDir = "uba";
//		String serviceUrl = "http://localhost:9191/vericash-apis-gateway/";
//		String jsonString = "{\r\n" +
//				"  \"mac\": \"123123123\",\r\n" +
//				"  \"message\": {\r\n" +
//				"    \"header\": {\r\n" +
//				"    	\"serviceCode\":\"1005\",\r\n" +
//				"      \"username\": \"dWJh\",\r\n" +
//				"      \"password\": \"MTIzNDU2Nzg5\",\r\n" +
//				"      \"userId\":\"1234\",\r\n" +
//				"       \"project\": \"product\",\r\n" +
//				"    	  \"serviceName\": \"user-service\"\r\n" +
//				"    },\r\n" +
//				"    \"payload\": {\r\n" +
//				"    	\"apiCode\":\"1234\"\r\n" +
//				"    	\r\n" +
//				"      \r\n" +
//				"    },\r\n" +
//				"    \"additionalData\": {\r\n" +
//				"      \r\n" +
//				"    }\r\n" +
//				"  }\r\n" +
//				"}\r\n" +
//				"\r\n" +
//				"";
//
//
//		ApiDocumentationService apiDocumentationService = null;
////				new ApiDocumentationService(vericashApiCache,projectConfigLoader);
//		when(vericashApiCache.getVericashAPI(anyLong())).thenReturn(vericashAPI);
//		when(projectConfigLoader.getUserHome()).thenReturn(userHomeDir);
//		when(projectConfigLoader.getProjectName()).thenReturn(projectNameDir);
//		Object object = null ;
//		  BDDMockito.given(JsonUtil.getJsonForPrinting(object)).willReturn(jsonString);
//		  BDDMockito.given(FileUtils.getFileContents(anyString())).willReturn(anyString());
//		  File file = new File("E:\\Vericash-apis\\vericash-apis-config\\vericash-apis-config\\api-template.html.html") ;
//		  BDDMockito.given(FileUtils.writeToFile(file, anyString())).willReturn(true);
//
//
//
//		apiDocumentationService.document(request, null, serviceUrl);
//
//	}
//
//	private Request initRequest() {
//		Request request = new Request();
//		Message message = new Message();
//
//	    String signature = "123123123";
//		message.setHeader(new Header());
//		message.setPayload(new Payload());
//
//		message.getHeader().put("code", 1234L);
//		message.getHeader().put("walletShortCode", 1234L);
//		message.getPayload().put("senderMsisdn", "2348077770060");
//
//		request.setMessage(message);
//		request.setSignature(signature);
//
//		return request;
//	}
//
//	private  Response initResponse() {
//		Response response = new Response();
//		response.getResponse().setApiCode("1234");
//		response.getResponse().setResponseStatus(ResponseStatus.Succeeded);
//
//		ResultSet resultSet = new ResultSet();
//		List<Record> records = new ArrayList<Record>();
//		Record record = new Record();
//		record.setField("id", "1234");
//		records.add(record);
//		resultSet.setRecords(records);;
//		response.getResponse().setResult(resultSet);
//
//		return response;
//	}
//
//	private VericashAPI initVericashAPI() {
//		VericashAPI vericashAPI = new VericashAPI();
//		vericashAPI.setName("Test");
//		return vericashAPI;
//	}
//
//}
