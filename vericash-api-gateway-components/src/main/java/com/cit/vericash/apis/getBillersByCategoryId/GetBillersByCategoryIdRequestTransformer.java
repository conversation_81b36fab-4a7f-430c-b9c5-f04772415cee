package com.cit.vericash.apis.getBillersByCategoryId;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.stereotype.Component;

@Component
public class GetBillersByCategoryIdRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

    }

}
