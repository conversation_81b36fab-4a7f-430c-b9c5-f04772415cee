package com.cit.vericash.apis.recurringTransaction;

import com.cit.mpaymentapp.common.notification.AdditionalData;
import com.cit.mpaymentapp.common.notification.Header;
import com.cit.mpaymentapp.common.notification.Payload;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.jetty.JettyHttpClient;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;

@Component
@Qualifier("com.cit.vericash.apis.recurringTransaction.recurringTransactionImpl")
public class RecurringTransactionImpl {


    @Autowired
    RecurringTransactionUtil recurringTransactionUtil;

    @Autowired
    ConnectionUtil connectionUtil;

    public List<TransactionRecurringDTO> listOfTransaction(Request request) throws Exception {
        List<TransactionRecurringDTO> transactionRecurringDTOs = new ArrayList<>();
        System.out.println("asdsad");
        Long customerId = ((Number) request.getMessage().getPayload().getAttribute("customerId")).longValue();
        String fromDate=null;
        String toDate=null;
        ArrayList<LinkedHashMap<String, Object>> dynamicGroup = (ArrayList<LinkedHashMap<String, Object>>) request.getMessage().getPayload().getAttribute("dynamicGroup");
        for (LinkedHashMap<String, Object> mapCaptured : dynamicGroup) {
            LinkedHashMap<String, Object> inputParams = (LinkedHashMap<String, Object>) mapCaptured.get("inputParameters");
            LinkedHashMap<String, Object> startParams = (LinkedHashMap<String, Object>) inputParams.get("357");
            LinkedHashMap<String, Object> endParams = (LinkedHashMap<String, Object>) inputParams.get("358");
            fromDate= (String) startParams.get("parameterValue");
            toDate= (String) endParams.get("parameterValue");
        }
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        String query;
        fields = setFields(fields);
        if (Objects.equals(fromDate, "") && Objects.equals(toDate, "") || fromDate == null && toDate == null) {

            query = ServiceQueryEngine.getQueryStringToExecute("listOfTransaction_if",this.getClass(),String.valueOf(customerId));
        }
        else if(Objects.equals(toDate,"" )|| toDate == null) {
            query=ServiceQueryEngine.getQueryStringToExecute("listOfTransaction_elseif",this.getClass(),String.valueOf(customerId),fromDate);
        }
        else {
            query = ServiceQueryEngine.getQueryStringToExecute("listOfTransaction_else",this.getClass(),String.valueOf(customerId),fromDate,toDate);
        }
        List<Record> records = connectionUtil.executeSelect(query, parameters, fields);
        for (Record record : records) {
            TransactionRecurringDTO transactionRecurringDTO = recurringTransactionUtil.addTransactionRecurringDTO(record);
            System.out.println(transactionRecurringDTO);
            System.out.println("-----------------");
            transactionRecurringDTOs.add(transactionRecurringDTO);

        }
        return transactionRecurringDTOs;
    }

    public void deleteRecurring(Request request) throws GeneralFailureException {
        String headers = "{\"Content-Type\":\"application/json\",\"Authorization\":\"Basic dXNlck5hbWU6cGFzc3dvcmQ=\"}";
        String url = "http://192.168.4.35:8084/cancel-recurring";
        String jsonMessage = getSchedulerMessage((Message) request.getMessage());
        try {
            JettyHttpClient.post(url, jsonMessage, headers);
        } catch (Exception e) {
            throw new GeneralFailureException("Recurring service is unreachable");
        }
    }

    public void updateRecurring(Request request) throws GeneralFailureException {
        String headers = "{\"Content-Type\":\"application/json\",\"Authorization\":\"Basic dXNlck5hbWU6cGFzc3dvcmQ=\"}";
        String url = "http://192.168.4.35:8084/update-recurring";
        String jsonMessage = getSchedulerMessage((Message) request.getMessage());
        try {
            JettyHttpClient.post(url, jsonMessage, headers);
        } catch (Exception e) {
            throw new GeneralFailureException("Recurring service is unreachable");
        }
    }
    public boolean hasScheduledStatus(long occurrenceId){
        StringBuilder hasScheduledStatusQuery = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("hasScheduledStatus",this.getClass(),String.valueOf(occurrenceId)));
        List<Record> records = connectionUtil.executeSelect(hasScheduledStatusQuery.toString(), new ArrayList<>());
        return records != null && !records.isEmpty();
    }

    public boolean hasNexOccurrence(long occurrenceId) {
        StringBuilder hasNexOccurrenceQuery = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("hasNexOccurrenceQuery",this.getClass(),String.valueOf(occurrenceId)));
        List<Record> records = connectionUtil.executeSelect(hasNexOccurrenceQuery.toString(), new ArrayList<>());
        return records.get(0).getValueAsBoolan("HAS_NEXT_OCCURRENCE");
    }

    private String getSchedulerMessage(Message message) {
        Gson gson = new Gson();
        com.cit.mpaymentapp.common.notification.Message messagePortal = new com.cit.mpaymentapp.common.notification.Message();
        AdditionalData additionalData = new AdditionalData();
        additionalData.putAll(message.getAdditionalData());
        messagePortal.setAdditionalData(additionalData);
        Header header = new Header();
        header.putAll(message.getHeader());
        messagePortal.setHeader(header);
        Payload payload = new Payload();
        payload.putAll(message.getPayload());
        messagePortal.setPayload(payload);
        return gson.toJson(messagePortal);
    }

    public void cancelRecurring(Request request) throws GeneralFailureException {
        Long occurrenceId = request.getMessage().getPayload().getAttributeAsLong("occurrenceId");
        if (hasScheduledStatus(occurrenceId)) {
            StringBuilder cancelOccurrenceQuery = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("cancelRecurring",this.getClass(),String.valueOf(occurrenceId)));
            String headers = "{\"Content-Type\":\"application/json\",\"Authorization\":\"Basic dXNlck5hbWU6cGFzc3dvcmQ=\"}";
            String url = "http://222.222.100.68:1407/create-occurrence";
            String jsonMessage = getSchedulerMessage((Message) request.getMessage());
            if (hasNexOccurrence(occurrenceId)) {
                try {
                    JettyHttpClient.post(url, jsonMessage, headers);
                } catch (Exception e) {
                    throw new GeneralFailureException("Scheduler service is unreachable");
                }
            }

            connectionUtil.executeUpdate(cancelOccurrenceQuery.toString(), new ArrayList<>());

        } else {
            throw new GeneralFailureException("Incorrect occurrenceId ");
        }
    }

    public List<String> setFields(List<String> fields) {
        fields.add("ID");
        fields.add("NAME");
        fields.add("TYPE");
        fields.add("END_TIME");
        fields.add("REPEAT_COUNT");
        fields.add("START_TIME");
        fields.add("FREQUENCY");
        fields.add("STATUS");
        fields.add("LAST_OCCURRENCE_STATUS");
        fields.add("DETAILS");
        return fields;
    }
}