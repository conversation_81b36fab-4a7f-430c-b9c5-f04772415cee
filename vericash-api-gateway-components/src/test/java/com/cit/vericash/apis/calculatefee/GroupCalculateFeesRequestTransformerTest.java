package com.cit.vericash.apis.calculatefee;

import com.cit.mpaymentapp.model.Enums;
import com.cit.service.commons.codeMapping.Param;
import com.cit.service.commons.codeMapping.ServiceCodeMappingComponent;
import com.cit.vericash.api.repository.ApiVericashApiRepository;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicinputparameters.mapping.DynamicPayloadTransformer;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.cit.vericash.apis.calculatefee.GroupCalculateFeesRequestTransformer.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GroupCalculateFeesRequestTransformerTest {

    @Mock
    private DynamicPayloadTransformer dynamicPayloadTransformer;

    @Mock
    private ApiVericashApiRepository apiVericashApiRepository;

    @Mock
    private ServiceCodeMappingComponent serviceCodeMappingComponent;

    @Mock
    private FeeProfileLoaderComponent feeProfileLoaderComponent;

    @Mock
    private PropertyLoaderComponent propertyLoaderComponent;

    @InjectMocks
    private GroupCalculateFeesRequestTransformer transformer;

    private Request request;
    private Message message;
    private DynamicPayload dynamicPayload;
    private Payload payload;

    @Before
    public void setup() {
        request = new Request();
        message = new Message();
        dynamicPayload = new DynamicPayload();
        payload = new Payload();

        request.setMessage(message);
        message.setDynamicPayload(dynamicPayload);
        dynamicPayload.setPayload(payload);
    }

    @Test
    public void testProcess() throws Exception {
        // Arrange
        message.getPayload().setAttribute(SENDER_OWNER_TYPE, Enums.OwnerTypeEnum.CUSTOMER);
        message.getPayload().setAttribute(SERVICE_TYPE, "1773661");
        payload.setAttribute(SOURCE_PAYMENT_METHOD, Collections.singletonMap(PAYMENT_METHOD_TYPE, "842"));
        dynamicPayload.getPayload().setAttribute(CUSTOMER_ID, 123L);
        when(dynamicPayloadTransformer.transform(any(DynamicPayload.class))).thenReturn(payload);
        when(apiVericashApiRepository.findServiceCodeForApiCode(eq("1773661"))).thenReturn(Optional.of("77366"));
        when(serviceCodeMappingComponent.loadChildCodesForServiceCode(eq("77366"), any(Param.class), eq(Collections.emptyList()))).thenReturn(Collections.singletonMap("842", "2773660"));
        when(feeProfileLoaderComponent.loadCustomerFeeProfile(123L)).thenReturn(1L);

        // Act
        Object result = transformer.process(request);

        // Assert
        assertNotNull(result);
        assertEquals(request, result);
    }

    @Test
    public void testLoadChildServiceCodesForApiCode() throws Exception {
        // Arrange
        when(apiVericashApiRepository.findServiceCodeForApiCode(any(String.class))).thenReturn(Optional.of("77366"));
        when(serviceCodeMappingComponent.loadChildCodesForServiceCode(any(String.class), any(Param.class), any(List.class))).thenReturn(Collections.singletonMap("842", "2773660"));

        // Act
        Map<String, String> result = transformer.loadChildServiceCodesForApiCode("apiCode", "senderPmType");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("2773660", result.get("842"));
    }

    @Test
    public void testLoadSenderFeeProfileId_Customer() throws Exception {
        // Arrange
        when(feeProfileLoaderComponent.loadCustomerFeeProfile(any(Long.class))).thenReturn(1L);

        // Act
        Long result = transformer.loadSenderFeeProfileId(Enums.OwnerTypeEnum.CUSTOMER, 1L);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.longValue());
    }

    @Test
    public void testLoadSenderFeeProfileId_BusinessEntity() throws Exception {
        // Arrange
        when(feeProfileLoaderComponent.loadAgentFeeProfile(any(Long.class))).thenReturn(1L);

        // Act
        Long result = transformer.loadSenderFeeProfileId(Enums.OwnerTypeEnum.BUSINESSENTITY, 1L);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.longValue());
    }
}