package com.cit.vericash.apis.getcashoutrequests;

import com.cit.mpaymentapp.model.pvoucher.VoucherStatus;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.EnumTransformer;
import com.cit.vericash.apis.commons.util.TransactionAmountAndCurrency;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.getcashoutrequests.GetCashOutRequestTransformer")
public class GetCashOutRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    private final String VOUCHERSTATUS = "voucher-Status";
    private final String walletShortCodeKey = "wallet-Short-Code";
    private final String walletCurrencyKey = "wallet_currency";
    @Autowired
    TransactionAmountAndCurrency transactionAmountAndCurrency;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        try {
//			cacheableFieldParameters = CacheableFieldParametersImpl.getInstance();

            String voucherStatusKey = cacheableFieldParameters.getRequestParameters(VOUCHERSTATUS);
            String voucherStatusString = message.getPayload().getAttributeAsString(voucherStatusKey);

//            TransactionAmountAndCurrency transactionAmountAndCurrency = TransactionAmountAndCurrency.getInstance();

            String walletShortCode = message.getHeader().getAttributeAsString(cacheableFieldParameters.getRequestParameters(walletShortCodeKey));
            String walletCurrency = transactionAmountAndCurrency.getWalletCurrency(walletShortCode);

            message.getPayload().put(cacheableFieldParameters.getRequestParameters(walletCurrencyKey), walletCurrency);


            VoucherStatus voucherStatus = EnumTransformer.transferVoucherStatus(voucherStatusString);

            message.getPayload().put(voucherStatusKey, voucherStatus);


        } catch (Exception e) {
            throw e;
        }
    }
}
