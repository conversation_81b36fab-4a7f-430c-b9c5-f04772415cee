<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet failOnError="false" author="Abubakr" id="CREATE TABLE ALIAS_MANAGEMENT">
        <sql>
			CREATE TABLE ALIAS_MANAGEMENT (
											  ID NUMBER(19, 0) GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
											  ALIAS_DOMAIN VARCHAR2(255),
											  IS_MANUALLY_GENERATED NUMBER(1),
											  NUMBER_OF_SUGGESTIONS NUMBER(10),
											  GENERATE_SUGGESTIONS NUMBER(1)
			);
		</sql>
    </changeSet>

	<changeSet failOnError="false" author="Abubakr" id="Create new get.alias.lookup">
		<sql>
			INSERT INTO LOOKUP_FRAMEWORK_CONFIG (ID, CACHING_KEY_PATTERN, CACHING_TYPE, IS_CACHED_LOOKUP, LOOKUP_NAME, LOOKUP_TYPE, RESPONSE_TRANSFORMER_MAPPING, CUSTOME_RESPONSE_TRANSFORMER, CUSTOM_TRANSFORMER_CONFIG_FILE, ENDPOINT_NAME, SERVICE_NAME) VALUES((select max(ID) +1 from LOOKUP_FRAMEWORK_CONFIG), '0', 0, 0, 'get.alias.lookup', 0, NULL, 'getAliasLookup', NULL, NULL, NULL);
		</sql>
		<sql>
			INSERT INTO PORTAL_LOOKUPS_CONFIG (ID, LOOKUP_NAME, LOOKUP_KEY, LOOKUP_QUERY, CACHEABLE) VALUES((select max(ID) +1 from PORTAL_LOOKUPS_CONFIG), 'Get Alias Lookup', 'get.alias.lookup', 'SELECT IS_MANUALLY_GENERATED ,ALIAS_DOMAIN  FROM ALIAS_MANAGEMENT;', 0);
		</sql>
	</changeSet>


</databaseChangeLog>