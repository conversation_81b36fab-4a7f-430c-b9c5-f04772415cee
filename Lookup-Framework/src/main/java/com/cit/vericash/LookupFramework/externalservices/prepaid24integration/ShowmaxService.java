package com.cit.vericash.LookupFramework.externalservices.prepaid24integration;

import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.springframework.stereotype.Service;

import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("showmaxService")
public class ShowmaxService extends ExternalServiceTemplate {

    public ShowmaxService() {
        this.integrationServiceName = "PREPAID24-Query";
        this.lookupKey = "showmax";
    }

    @Override
    public Payload getData() throws Exception {
        Payload payload = getShowMaxDetailsList();
        resetAndSaveInMongo(payload);
        return payload;
    }

    private DynamicPayload generateRequest(String function, String code) {
        DynamicPayload dynamicPayload = new DynamicPayload();
        Header header = new Header();
        String walletShortCode = getProperty("walletShortCode");
        header.put("walletShortCode", walletShortCode);
        dynamicPayload.setHeader(header);

        Payload payload = new Payload();
        Long timestamp = System.currentTimeMillis();

        Map<String, String> authorization = new HashMap<>();
        authorization.put("Username", getProperty("userName"));
        authorization.put("Password", getProperty("password"));
        payload.put("Authorization", authorization);

        Map<String, String> request = new HashMap<>();
        request.put("CLNT_Reference", timestamp.toString());
        request.put("Function", (function != null && !function.isEmpty()) ? function : "Product Query");
        request.put("Process", "Immediate");
        payload.put("Request", request);

        Map<String, String> product = new HashMap<>();
        product.put("Type", "Digital");
        product.put("Provider", "All");
        product.put("Code", (code != null && !code.isEmpty()) ? code : "All");
        payload.put("Product", product);

        dynamicPayload.setPayload(payload);
        return dynamicPayload;
    }

    public Payload getShowMaxDetailsList() throws Exception {
        List<Map<String, Object>> showmaxList = getShowMaxList();
        showmaxList = showmaxList
                .stream()
                .filter(showmax -> ((String) showmax.get("Name")).toLowerCase().contains("showmax"))
                .map(showmax -> {
                    Map<String, Object> showmaxDetails = getShowMaxDetails((String) showmax.get("ID"));
                    showmax.put("details", showmaxDetails);
                    return showmax;
                })
                .collect(Collectors.toList());
        Payload payload = new Payload();
        payload.put("response", showmaxList);
        return payload;
    }

    public List<Map<String, Object>> getShowMaxList() throws Exception {
        DynamicPayload dynamicPayload = generateRequest(null, null);
        Payload integPayload = this.integrate(dynamicPayload);
        return (List<Map<String, Object>>) ((Map) integPayload.get("response")).get("Description");
    }


    public Map<String, Object> getShowMaxDetails(String showmaxCode) {
        DynamicPayload dynamicPayload = generateRequest("Product Detail", showmaxCode);
        Map<String, Object> result = new HashMap<>();
        Payload payload = null;
        try {
            payload = this.integrate(dynamicPayload);
            List<Map<String, Object>> resultList = (List<Map<String, Object>>) ((Map) payload.get("response")).get("Description");
            result = !resultList.isEmpty() ? resultList.get(0) : new HashMap<>();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> transform(Payload integPayload) {
        List<Map<String, Object>> showmaxList = (List<Map<String, Object>>) integPayload.get("response");
        showmaxList.stream().forEach(showmax -> {
            showmax.remove("Status");
            Map<String, Object> showmaxDetails = (Map<String, Object>) showmax.getOrDefault("details", new HashMap<>());
            showmaxDetails.remove("ID");
            showmaxDetails.remove("Name");
            showmaxDetails.remove("Provider");
            showmaxDetails.remove("Status");
            showmaxDetails.put("amount", showmaxDetails.remove("Value"));
        });
        return showmaxList;
    }


    protected void resetAndSaveInMongo(Payload payload) throws UnknownHostException, GeneralFailureException {
        mongoDBWrapper.resetAndSaveCollection(lookupKey + "_before_transformation", payload);
    }
}
