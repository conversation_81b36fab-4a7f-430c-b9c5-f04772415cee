package com.cit.vericash.apis.receiver_approval;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.receiver_approval.RespondToReceiverApprovalResponseTransformer")
public class RespondToReceiverApprovalResponseTransformer implements MuleResponseTransformer {

    private static final Map<String, Object> result = new HashMap<>();
    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        return result;
    }
}
