package com.cit.vericash.apis.receiver_approval;

import static com.cit.vericash.apis.receiver_approval.ReceiverApprovalService.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.cit.vericash.api.components.impl.MuleMessageSenderImpl;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.util.RequestParsingUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class ReceiverApprovalServiceTest {

    @Mock
    private ConnectionUtil connectionUtil;

    @Mock
    private PropertyLoaderComponent propertyLoaderComponent;

    @Mock
    private MuleMessageSenderImpl muleMessageSender;

    @Mock
    private RequestParsingUtils requestParsingUtils;

    @InjectMocks
    private ReceiverApprovalService receiverApprovalService;

    private Request mockRequest;
    private Record mockRecord;

    @BeforeEach
    void setUp() {
        mockRequest = new Request();
        Payload payload = new Payload();
        Message message = new Message();
        message.setPayload(payload);
        mockRequest.setMessage(message);
        payload.put(APPROVAL_REQUEST_ID, "123");
        payload.put(CUSTOMER_ID, 456L);
        payload.put(APPROVE_OR_REJECT, true);

        mockRecord = new Record();
        mockRecord.put(EXPIRY_DATE, LocalDateTime.now().plusDays(1).toString());
        mockRecord.put(REQUEST_ID, 123L);
        mockRecord.put(AMOUNT, new BigDecimal("100.00"));
        mockRecord.put(FULL_NAME, "John Doe");
        mockRecord.put(MSISDN, "123456789");
        mockRecord.put(STATUS_CODE, new BigDecimal("200"));
        mockRecord.put(STATUS_NAME, "Approved");
        mockRecord.put(CORRELATION_ID, "corr-id-123");
        mockRecord.put(API_CODE, "12345");
        mockRecord.put(REQUEST_DATE, LocalDateTime.now().toString());
    }

    @Test
    void testGetReceiverRequestApproval_ValidRequest() throws Exception {
        when(connectionUtil.executeSelect(anyString(), anyList(), anyList())).thenReturn(List.of(mockRecord));
        when(propertyLoaderComponent.getPropertyAsString(anyString())).thenReturn("Mocked Property");

        var result = receiverApprovalService.getReceiverRequestApproval(mockRequest);

        assertNotNull(result);
        assertEquals(123L, result.getApprovalRequestId());
        assertEquals("John Doe", result.getSenderFullName());
    }

    @Test
    void testGetReceiverRequestApproval_ExpiredRequest() {

        when(connectionUtil.executeSelect(anyString(), anyList(), anyList())).thenReturn(List.of(mockRecord));
        mockRecord.put(EXPIRY_DATE, LocalDateTime.now().minusDays(1).toString());

        APIException exception = assertThrows(APIException.class,
                () -> receiverApprovalService.getReceiverRequestApproval(mockRequest));
        assertEquals("Expired request", exception.getMessage());
    }

    @Test
    void testUpdateReceiverRequestApproval_ValidRequest() throws Exception {
        when(connectionUtil.executeSelect(anyString(), anyList(), anyList())).thenReturn(List.of(mockRecord));
        when(propertyLoaderComponent.getPropertyAsString(anyString())).thenReturn("Mocked Property");

        Map<String, Object> result = receiverApprovalService.updateReceiverRequestApproval(mockRequest);

        assertNotNull(result);
        assertEquals(123L, result.get(ReceiverApprovalService.APPROVAL_REQUEST_ID));
        verify(muleMessageSender).respondToQueue(anyBoolean(), eq("corr-id-123"), anyString());
        verify(connectionUtil).executeUpdate(anyString(), anyList());
    }

    @Test
    void testUpdateReceiverRequestApproval_InvalidRequest() {
        when(connectionUtil.executeSelect(anyString(), anyList(), anyList())).thenReturn(List.of());

        APIException exception = assertThrows(APIException.class,
                () -> receiverApprovalService.updateReceiverRequestApproval(mockRequest));
        assertEquals("Invalid approvalRequestId", exception.getMessage());
    }

    @Test
    void testLoadRequestApproval_ValidRequest() {
        when(connectionUtil.executeSelect(anyString(), anyList(), anyList())).thenReturn(List.of(mockRecord));

        Record result = receiverApprovalService.loadRequestApproval(mockRequest);

        assertNotNull(result);
        assertEquals("John Doe", result.getValueAsString(ReceiverApprovalService.FULL_NAME));
    }

    @Test
    void testLoadRequestApproval_InvalidRequest() {
        when(connectionUtil.executeSelect(anyString(), anyList(), anyList())).thenReturn(List.of());

        APIException exception = assertThrows(APIException.class,
                () -> receiverApprovalService.loadRequestApproval(mockRequest));
        assertEquals("Invalid approvalRequestId", exception.getMessage());
    }

    @Test
    void testCheckExpiredAndUpdateStatus_ValidDate() {
        LocalDateTime expiryDate = receiverApprovalService.checkExpiredAndUpdateStatus(mockRecord);

        assertNotNull(expiryDate);
        verify(connectionUtil, never()).executeUpdate(anyString(), anyList());
    }

    @Test
    void testCheckExpiredAndUpdateStatus_ExpiredDate() {
        mockRecord.put(EXPIRY_DATE, LocalDateTime.now().minusDays(1).toString());
        APIException exception = assertThrows(APIException.class,
                () -> receiverApprovalService.checkExpiredAndUpdateStatus(mockRecord));
        assertEquals("Expired request", exception.getMessage());
    }
}
