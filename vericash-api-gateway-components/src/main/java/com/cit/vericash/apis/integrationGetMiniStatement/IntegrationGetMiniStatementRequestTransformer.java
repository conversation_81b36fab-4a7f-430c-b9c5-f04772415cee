package com.cit.vericash.apis.integrationGetMiniStatement;

import com.cit.mpaymentapp.common.corebaking.full.statement.ExternalTransactionPeriod;
import com.cit.mpaymentapp.common.paymentmethod.SourcePaymentMethodDTO;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;

@Component
public class IntegrationGetMiniStatementRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String externalTransactionPeriodKey = "externalTransactionPeriod";
    private final String sourcePaymentMethodKey = "source-Payment-Method";
    private final long External_Bank_Account_sourec = 852;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

        ObjectMapper mapper = new ObjectMapper();
        ObjectMapper mapperSourcPaymentMethod = new ObjectMapper();

        ExternalTransactionPeriod externalTransactionPeriod;
        LinkedHashMap<String, Object> externalTransactionPeriodMap = null;

        Object SourcePaymentMethodObj = message.getPayload().get(cacheableFieldParameters.getRequestParameters(sourcePaymentMethodKey));
        if (SourcePaymentMethodObj != null) {
            String sourceJson = mapperSourcPaymentMethod.writeValueAsString(SourcePaymentMethodObj);
            SourcePaymentMethodDTO sourcePaymentMethod = mapperSourcPaymentMethod.readValue(sourceJson, SourcePaymentMethodDTO.class);
            if (sourcePaymentMethod.getPaymentMethodType() != External_Bank_Account_sourec) {
                throw new APIException("unimplemented  payment method", ErrorCode.UNIMPLEMENTED_PAYMENT_METHOD.getErrorCode());
            }
        }
        Object externalTransactionObject = message.getPayload().getAttribute(cacheableFieldParameters.getResponseParameters(externalTransactionPeriodKey));

        if (externalTransactionObject != null && externalTransactionObject instanceof LinkedHashMap) {
            externalTransactionPeriodMap = (LinkedHashMap<String, Object>) externalTransactionObject;
        }

        if (externalTransactionPeriodMap != null && externalTransactionPeriodMap.size() > 0) {
            String respData = mapper.writeValueAsString(message.getPayload().getAttribute(cacheableFieldParameters.getResponseParameters(externalTransactionPeriodKey)));
            externalTransactionPeriod = mapper.readValue(respData, ExternalTransactionPeriod.class);
        } else {
            externalTransactionPeriod = new ExternalTransactionPeriod();
            Date date = Calendar.getInstance().getTime();
            DateFormat endDateDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String endDate = endDateDateFormat.format(date);
            externalTransactionPeriod.setEndDate(endDate);
            DateFormat startDateDateFormat = new SimpleDateFormat("yyyy-MM-01");
            String startDate = startDateDateFormat.format(date);
            externalTransactionPeriod.setStartDate(startDate);
        }
        message.getPayload().put("externalTransactionPeriod", externalTransactionPeriod);
    }
}
