package com.cit.vericash.apis.assignRedeemGifts;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component("assginAndRedeemGiftsResponseTransformer")
public class AssginAndRedeemGiftsResponseTransformer implements MuleResponseTransformer {
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, String> result = new HashMap<String, String>();
        result.put("redemptionCode", ((Map) businessMessage.getMicroServicesResponse()).get("redemptionCode").toString());
        result.put("serialNumber", ((Map) businessMessage.getMicroServicesResponse()).get("redemptionCode").toString());
        return result;
    }
}
