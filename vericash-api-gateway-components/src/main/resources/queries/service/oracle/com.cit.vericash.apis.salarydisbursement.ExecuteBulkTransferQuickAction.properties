getRecipientsForNormalFlow=SELECT gts.GROUP_NAME,c.CUSTOMER_FEE_PROFILE_ID ,gtr.TRANSFER_ID,gtr.AMOUNT,gtr.BENEFICIARY_ID,gtr.BENEFICIARY_NAME,gtr.<PERSON><PERSON><PERSON>_CODE,gtr.<PERSON><PERSON><PERSON>_NAME,gtr.CARD_NUMBER,gtr.COUNTRY_NAME,gtr.INSTITUTION_CODE,gtr.INSTITUTION_NAME,gtr.TRANSFER_TYPE,gtr.TRANSFER_TYPE_CATEGORY,gtr.WALLET_SHORT_CODE,gtr.GROUP_TRANSFER_SETUP_ID,gtr.STATUS,gtr.CLIENT_ID,gtr.RECEIVER_TYPE,gtr.WALLET_PROVIDER,gtr.CURRENCY,gtr.PAYMENT_ALIAS,gtr.MOBILE_NUMBER,gtr.IS_DELETED,gtr.VALIDATION_ERROR,gtr.GAR_ID,gtr.PAYMENT_METHOD_TYPE ,cg.ACCOUNT_ID, gtr.CUSTOMER_ID  FROM GROUP_TRANSFER_RECIPIENT gtr INNER JOIN CUSTOMER_GAR cg  ON gtr.GAR_ID = cg.GARID INNER JOIN CUSTOMER c ON c.CUSTOMER_ID = gtr.CUSTOMER_ID INNER JOIN GROUP_TRANSFER_SETUP gts ON gts.GROUP_TRANSFER_SETUP_ID = gtr.GROUP_TRANSFER_SETUP_ID  WHERE gtr.GROUP_TRANSFER_SETUP_ID= %1$s