package com.cit.vericash.apis.BillPaymentDSTVInquiry;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class billPaymentDstvInquiryResponseTransformer implements MuleResponseTransformer {
    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        String jsonString = (String) businessMessage.getIntegrationResponse();

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonString);

        JsonNode descriptionNode = rootNode.path("Response").path("Description");
        result.put("Details", descriptionNode);

        return  result;
    }
}
