package com.cit.vericash.apis.openBankAccount;


import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;


@Component
@Qualifier("com.cit.vericash.apis.openBankAccount.CreateBankAccountResponseTransformer")
public class CreateBankAccountResponseTransformer implements MuleResponseTransformer {
	public Object transform(BusinessMessage businessMessage) throws Exception {

		if (businessMessage.getParameters().containsKey("bankAccountNumber")) {
			return businessMessage.getParameters().getAttributeAsString("bankAccountNumber");
		}
		return null;
	}


}
