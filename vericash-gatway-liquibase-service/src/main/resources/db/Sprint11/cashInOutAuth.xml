<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet id="insert two children services" author="<PERSON> Or<PERSON>">
        <sql>
        INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID,IS_GENERIC_SERVICE,SERVICE_TYPE_NAME,SERVICE_MODE,SERVICE_CODE,IS_INDEMNITY,SEPARATE_QUEUE,SEPARATE_FRAMEWORK_QUEUE,HAS_WORKFLOW,SERVICE_CATEGORY,PARENT_SERVICE_CODE,ROLE_BASED)
             VALUES (500600,0,'Generate OTP SMS',0,'500600',1,0,0,0,0,333,0);
        INSERT INTO SERVICE_CONFIG_MAP (SERVICE_TYPE_ID,IS_GENERIC_SERVICE,SERVICE_TYPE_NAME,SERVICE_MODE,SERVICE_CODE,IS_INDEMNITY,SEPARATE_QUEUE,SEPARATE_FRAMEWORK_QUEUE,HAS_WORKFLOW,SERVICE_CATEGORY,PARENT_SERVICE_CODE,ROLE_BASED)
            VALUES (500601,0,'Generate OTP App Notification',0,'500601',1,0,0,0,0,333,0);
        </sql>
    </changeSet>
    <changeSet id="configure the services to business entity" author="Mohamed Orabi">
        <sql>
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID,BANKING_AGENT,BUSINESSSERVICECATEGORY,ISDEFAULTSERVICE,IS_EXPOSABLE,IS_MULTI_WALLET,NAME,BUSINESS_SERVICE_TYPE,SEGMENTATIONTYPE_ID,ORGANIZATION_ID,IS_ENABLED)
            VALUES ((SELECT MAX (ID)+1 FROM BUSINESS_SERVICE_CONFIG) ,0,0,1,0,0,'Generate OTP SMS',500600,0,2421,1);
            INSERT INTO BUSINESS_SERVICE_CONFIG (ID,BANKING_AGENT,BUSINESSSERVICECATEGORY,ISDEFAULTSERVICE,IS_EXPOSABLE,IS_MULTI_WALLET,NAME,BUSINESS_SERVICE_TYPE,SEGMENTATIONTYPE_ID,ORGANIZATION_ID,IS_ENABLED)
            VALUES ((SELECT MAX (ID)+1 FROM BUSINESS_SERVICE_CONFIG),0,0,1,0,0,'Generate OTP App Notification',500601,0,2421,1);
        </sql>
    </changeSet>
</databaseChangeLog>