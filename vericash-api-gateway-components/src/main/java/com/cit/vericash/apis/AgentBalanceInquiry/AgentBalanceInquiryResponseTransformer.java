package com.cit.vericash.apis.AgentBalanceInquiry;

import com.cit.mpaymentapp.common.customer.message.BalanceEnquiry;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.AgentBalanceInquiry.AgentBalanceInquiryResponseTransformer")
public class AgentBalanceInquiryResponseTransformer implements MuleResponseTransformer {
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    private String businessEntityNameKey = "business_Entity_Name";
    private String accountKey = "Account";

    public Object transform(BusinessMessage businessMessage) throws Exception {
//     cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
        String businessEntityName = cacheableFieldParameters.getResponseParameters(businessEntityNameKey);
        String account = cacheableFieldParameters.getResponseParameters(accountKey);
        Map<String, String> result = new HashMap<String, String>();
        ObjectMapper mapperObj = new ObjectMapper();

        List<BalanceEnquiry> balanceEnquiryList = businessMessage.getPrimarySenderInfo().getPaymentMethod().getBalanceEnquiryList();
        String BussinessEntityName = businessMessage.getPrimarySenderInfo().getEntityInfo().getName();
        String jsonMessage = "";

        if (balanceEnquiryList != null && !balanceEnquiryList.isEmpty()) {
            jsonMessage = mapperObj.writeValueAsString(balanceEnquiryList);
        }
//     JSONObject jsonObject = new JSONObject();
//     jsonObject.put("accountType",balanceEnquiryList.get(0).getAccountType());

        result.put(account, jsonMessage);
        result.put(businessEntityName, BussinessEntityName);
        return result;
    }
}
