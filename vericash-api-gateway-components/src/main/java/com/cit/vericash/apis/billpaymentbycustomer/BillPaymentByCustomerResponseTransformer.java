package com.cit.vericash.apis.billpaymentbycustomer;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.model.transaction.TransactionExecutionSummary;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.billpaymentbycustomer.BillPaymentByCustomerResponseTransformer")
public class BillPaymentByCustomerResponseTransformer implements MuleResponseTransformer {
    private final String trxIdKey = "Transaction_Execution_Summary_ID";
    private final String trxAmountKey = "Transaction-Amount";
    private final String totalFeeAmountKey = "Total-Fee-Amount";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {

//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();


        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if (businessMessage.getSoftFields().get("TRANS_EXEC_SUMMARY") != null) {
                TransactionExecutionSummary transactionExecutionSummary = (TransactionExecutionSummary) businessMessage
                        .getSoftFields().get("TRANS_EXEC_SUMMARY");
                result.put(cacheableFieldParameters.getResponseParameters(trxAmountKey), transactionExecutionSummary.getTransactionAmount());
                //result.put("transactionExecutionSummaryID",transactionExecutionSummary.getTransactionExecutionSummaryID());
                result.put(cacheableFieldParameters.getResponseParameters(totalFeeAmountKey), (transactionExecutionSummary.getTotalFeeAmount() == null) ? 0 : transactionExecutionSummary.getTotalFeeAmount());
            }

        } catch (Exception e) {
            throw e;
        }

        return result;
    }

}
