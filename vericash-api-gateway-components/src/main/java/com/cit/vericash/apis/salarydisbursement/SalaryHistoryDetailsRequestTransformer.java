package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.common.group.transfers.GroupTransferDTO;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursemen.SalaryHistoryDetailsRequestTransformer")
public class SalaryHistoryDetailsRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        Gson gson = new Gson();
        String groupTransferJson = gson.toJson(message.getPayload().getAttribute("groupTransfer"));
        GroupTransferDTO groupTransferDTO = gson.fromJson(groupTransferJson, GroupTransferDTO.class);
        message.getPayload().setAttribute("groupTransfer", groupTransferDTO);
    }
}
