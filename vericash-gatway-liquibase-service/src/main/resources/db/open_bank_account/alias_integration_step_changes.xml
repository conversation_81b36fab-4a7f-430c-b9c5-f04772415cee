<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="aliasIntegrationServiceAdditionChange" author="Amir Nabil">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE (ID, DESCRIPTION, IS_MIGRATED, SERVICE_LEVEL, NAME, OWNER_SERVICE, SERVICE_REVERSAL_ID,
                                             SERVICE_STACK_ID, SERVICE_STACK_TYPE, CATEGORY_ID, FLOW_MIGRATION_FLAG,
                                             CLIENT_SERVICE_NAME, DESTINATION, SUPPORTED_CHANNELS)
            VALUES ((SELECT MAX(ID) + 1 FROM PRO_MI_NO_C.SERVICE), 'Open Account Alias Integration', 1, 0, 'Open Account Alias Integration', null, null,
                    'OpenAccountAliasIntegration', 0, null, 1, null, null, 'all')
        </sql>
    </changeSet>

    <changeSet id="aliasIntegrationStepChange" author="Amir Nabil">
        <sql>

            -- Insert into BUSINESS_SERVICE_STEP using the captured v_serviceId
            INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_STEP (ID, DESCRIPTION, IS_MIGRATED, STEP_ORDER, TYPE, BUSINESS_SERVICE_CONFIG_ID, SERVICE, TRANSACTION_DEF)
            VALUES ((SELECT MAX(ID) + 1 FROM PRO_MI_NO_C.BUSINESS_SERVICE_STEP), 'OpenAccountAliasIntegration', 0, 5, 1, ***************, (SELECT MAX(ID) FROM PRO_MI_NO_C.SERVICE), null);
        </sql>
    </changeSet>

    <changeSet id="changeLastStepOrder" author="Amir Nabil">
        <sql>
            UPDATE PRO_MI_NO_C.BUSINESS_SERVICE_STEP t
            SET t.STEP_ORDER = 6
            WHERE t.ID = 166565657780022279;

        </sql>
    </changeSet>

    <changeSet id="addValidationServiceToTable" author="AmirNabil">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE (ID, DESCRIPTION, IS_MIGRATED, SERVICE_LEVEL, NAME, OWNER_SERVICE, SERVICE_REVERSAL_ID, SERVICE_STACK_ID, SERVICE_STACK_TYPE, CATEGORY_ID, FLOW_MIGRATION_FLAG, CLIENT_SERVICE_NAME, DESTINATION, SUPPORTED_CHANNELS, SERVICE_NAME, ENDPOINT, TOPIC_REQUEST, ENABLED, REQUEST_TYPE) VALUES (166565657780022281, 'validateOBAPaymentMethodParameters', 1, 0, 'validateOBAPaymentMethodParameters', null, null, 'validateOBAPaymentMethodParameters', 0, null, 1, null, null, 'ALL', null, null, null, null, null);

        </sql>
    </changeSet>

    <changeSet id="changeLastStepOrderLatest" author="Amir Nabil">
        <sql>

            UPDATE PRO_MI_NO_C.BUSINESS_SERVICE_STEP t
            SET t.STEP_ORDER = 7
            WHERE t.ID = 166565657780022279;

            INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_STEP (ID, DESCRIPTION, IS_MIGRATED, STEP_ORDER, TYPE,
                                                           BUSINESS_SERVICE_CONFIG_ID, SERVICE, TRANSACTION_DEF)
            VALUES (166565657780022582, 'validateOBAPaymentMethodParameters', null, 6, 1, ***************, 166565657780022281,
                    null);
        </sql>
    </changeSet>
</databaseChangeLog>
