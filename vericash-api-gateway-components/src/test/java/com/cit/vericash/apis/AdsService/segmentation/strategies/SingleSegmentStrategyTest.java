package com.cit.vericash.apis.AdsService.segmentation.strategies;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cit.vericash.api.entity.Ad;
import com.cit.vericash.api.entity.GeneralLookups;
import com.cit.vericash.api.repository.GeneralLookupsRepository;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.AdditionalData;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.EchoData;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {SingleSegmentStrategy.class})
@ExtendWith(SpringExtension.class)
class SingleSegmentStrategyTest {
    @MockBean
    private ConnectionUtil connectionUtil;

    @MockBean
    private GeneralLookupsRepository generalLookupsRepository;

    @Autowired
    private SingleSegmentStrategy singleSegmentStrategy;

    /**
     * Test {@link SingleSegmentStrategy#loadSegmentationData(Request)}.
     * <p>
     * Method under test: {@link SingleSegmentStrategy#loadSegmentationData(Request)}
     */
    @Test
    @DisplayName("Test loadSegmentationData(Request)")
    @Tag("MaintainedByDiffblue")
    void testLoadSegmentationData() {
        // Arrange
        ArrayList<Record> resultRecordList = new ArrayList<>();
        resultRecordList.add(new Record());
        when(connectionUtil.executeSelect(Mockito.<String>any(), Mockito.<List<Parameter>>any()))
                .thenReturn(resultRecordList);
        Optional<GeneralLookups> emptyResult = Optional.empty();
        when(generalLookupsRepository.findLookupvalueByLookupkey(Mockito.<String>any())).thenReturn(emptyResult);

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(new Payload());

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        // Act and Assert
        assertThrows(APIException.class, () -> singleSegmentStrategy.loadSegmentationData(request));
        verify(generalLookupsRepository).findLookupvalueByLookupkey(eq("NewCustomerPeriod"));
        verify(connectionUtil).executeSelect(eq(
                        "SELECT c.CUSTOMER_ACCOUNT_TYPE, c.COUNTRY, c.CREATION_DATE , c.CUSTOMER_TYPE_ID , c.BE_CUSTOMER_ID FROM CUSTOMER c WHERE c.CUSTOMER_ID = ?"),
                isA(List.class));
    }

    /**
     * Test {@link SingleSegmentStrategy#loadSegmentationData(Request)}.
     * <p>
     * Method under test: {@link SingleSegmentStrategy#loadSegmentationData(Request)}
     */
    @Test
    @DisplayName("Test loadSegmentationData(Request)")
    @Tag("MaintainedByDiffblue")
    void testLoadSegmentationData2() {
        // Arrange
        SingleSegmentStrategy singleSegmentStrategy = new SingleSegmentStrategy(new ConnectionUtil(),
                mock(GeneralLookupsRepository.class));

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(new Payload());

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        // Act and Assert
        assertThrows(APIException.class, () -> singleSegmentStrategy.loadSegmentationData(request));
    }

    /**
     * Test {@link SingleSegmentStrategy#loadSegmentationData(Request)}.
     * <ul>
     *   <li>Given {@link GeneralLookupsRepository}.</li>
     *   <li>Then throw {@link APIException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link SingleSegmentStrategy#loadSegmentationData(Request)}
     */
    @Test
    @DisplayName("Test loadSegmentationData(Request); given GeneralLookupsRepository; then throw APIException")
    @Tag("MaintainedByDiffblue")
    void testLoadSegmentationData_givenGeneralLookupsRepository_thenThrowAPIException() {
        // Arrange
        when(connectionUtil.executeSelect(Mockito.<String>any(), Mockito.<List<Parameter>>any()))
                .thenReturn(new ArrayList<>());

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(new Payload());

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        // Act and Assert
        assertThrows(APIException.class, () -> singleSegmentStrategy.loadSegmentationData(request));
        verify(connectionUtil).executeSelect(eq(
                        "SELECT c.CUSTOMER_ACCOUNT_TYPE, c.COUNTRY, c.CREATION_DATE , c.CUSTOMER_TYPE_ID , c.BE_CUSTOMER_ID FROM CUSTOMER c WHERE c.CUSTOMER_ID = ?"),
                isA(List.class));
    }

    /**
     * Test {@link SingleSegmentStrategy#loadSegmentationData(Request)}.
     * <ul>
     *   <li>Then calls {@link GeneralLookups#getLookupvalue()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link SingleSegmentStrategy#loadSegmentationData(Request)}
     */
    @Test
    @DisplayName("Test loadSegmentationData(Request); then calls getLookupvalue()")
    @Tag("MaintainedByDiffblue")
    void testLoadSegmentationData_thenCallsGetLookupvalue() {
        // Arrange
        ArrayList<Record> resultRecordList = new ArrayList<>();
        resultRecordList.add(new Record());
        when(connectionUtil.executeSelect(Mockito.<String>any(), Mockito.<List<Parameter>>any()))
                .thenReturn(resultRecordList);
        GeneralLookups generalLookups = mock(GeneralLookups.class);
        when(generalLookups.getLookupvalue()).thenReturn("foo");
        doNothing().when(generalLookups).setLookupkey(Mockito.<String>any());
        doNothing().when(generalLookups).setLookupvalue(Mockito.<String>any());
        generalLookups.setLookupkey("Lookupkey");
        generalLookups.setLookupvalue("42");
        Optional<GeneralLookups> ofResult = Optional.of(generalLookups);
        when(generalLookupsRepository.findLookupvalueByLookupkey(Mockito.<String>any())).thenReturn(ofResult);

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(new Payload());

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        // Act and Assert
        assertThrows(APIException.class, () -> singleSegmentStrategy.loadSegmentationData(request));
        verify(generalLookups).getLookupvalue();
        verify(generalLookups).setLookupkey(eq("Lookupkey"));
        verify(generalLookups).setLookupvalue(eq("42"));
        verify(generalLookupsRepository).findLookupvalueByLookupkey(eq("NewCustomerPeriod"));
        verify(connectionUtil).executeSelect(eq(
                        "SELECT c.CUSTOMER_ACCOUNT_TYPE, c.COUNTRY, c.CREATION_DATE , c.CUSTOMER_TYPE_ID , c.BE_CUSTOMER_ID FROM CUSTOMER c WHERE c.CUSTOMER_ID = ?"),
                isA(List.class));
    }

    /**
     * Test {@link SingleSegmentStrategy#loadSegmentationData(Request)}.
     * <ul>
     *   <li>Then return size is six.</li>
     * </ul>
     * <p>
     * Method under test: {@link SingleSegmentStrategy#loadSegmentationData(Request)}
     */
    @Test
    @DisplayName("Test loadSegmentationData(Request); then return size is six")
    @Tag("MaintainedByDiffblue")
    void testLoadSegmentationData_thenReturnSizeIsSix() {
        // Arrange
        ArrayList<Record> resultRecordList = new ArrayList<>();
        resultRecordList.add(new Record());
        when(connectionUtil.executeSelect(Mockito.<String>any(), Mockito.<List<Parameter>>any()))
                .thenReturn(resultRecordList);

        GeneralLookups generalLookups = new GeneralLookups();
        generalLookups.setLookupkey("Lookupkey");
        generalLookups.setLookupvalue("42");
        Optional<GeneralLookups> ofResult = Optional.of(generalLookups);
        when(generalLookupsRepository.findLookupvalueByLookupkey(Mockito.<String>any())).thenReturn(ofResult);

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(new Payload());

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        // Act
        LinkedHashMap<String, Object> actualLoadSegmentationDataResult = singleSegmentStrategy
                .loadSegmentationData(request);

        // Assert
        verify(generalLookupsRepository).findLookupvalueByLookupkey(eq("NewCustomerPeriod"));
        verify(connectionUtil).executeSelect(eq(
                        "SELECT c.CUSTOMER_ACCOUNT_TYPE, c.COUNTRY, c.CREATION_DATE , c.CUSTOMER_TYPE_ID , c.BE_CUSTOMER_ID FROM CUSTOMER c WHERE c.CUSTOMER_ID = ?"),
                isA(List.class));
        assertEquals(6, actualLoadSegmentationDataResult.size());
        assertNull(actualLoadSegmentationDataResult.get("beCustomerId"));
        assertNull(actualLoadSegmentationDataResult.get("country"));
        assertNull(actualLoadSegmentationDataResult.get("creationDate"));
        assertNull(actualLoadSegmentationDataResult.get("customerAccountTypeId"));
        assertNull(actualLoadSegmentationDataResult.get("customerTypeId"));
        assertEquals(42L, ((Long) actualLoadSegmentationDataResult.get("newCustomerPeriod")).longValue());
    }

    /**
     * Test {@link SingleSegmentStrategy#fetchNewCustomerPeriod()}.
     * <ul>
     *   <li>Then return longValue is forty-two.</li>
     * </ul>
     * <p>
     * Method under test: {@link SingleSegmentStrategy#fetchNewCustomerPeriod()}
     */
    @Test
    @DisplayName("Test fetchNewCustomerPeriod(); then return longValue is forty-two")
    @Tag("MaintainedByDiffblue")
    void testFetchNewCustomerPeriod_thenReturnLongValueIsFortyTwo() {
        // Arrange
        GeneralLookups generalLookups = new GeneralLookups();
        generalLookups.setLookupkey("Lookupkey");
        generalLookups.setLookupvalue("42");
        Optional<GeneralLookups> ofResult = Optional.of(generalLookups);
        when(generalLookupsRepository.findLookupvalueByLookupkey(Mockito.<String>any())).thenReturn(ofResult);

        // Act
        Long actualFetchNewCustomerPeriodResult = singleSegmentStrategy.fetchNewCustomerPeriod();

        // Assert
        verify(generalLookupsRepository).findLookupvalueByLookupkey(eq("NewCustomerPeriod"));
        assertEquals(42L, actualFetchNewCustomerPeriodResult.longValue());
    }

    /**
     * Test {@link SingleSegmentStrategy#fetchNewCustomerPeriod()}.
     * <ul>
     *   <li>Then throw {@link IllegalStateException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link SingleSegmentStrategy#fetchNewCustomerPeriod()}
     */
    @Test
    @DisplayName("Test fetchNewCustomerPeriod(); then throw IllegalStateException")
    @Tag("MaintainedByDiffblue")
    void testFetchNewCustomerPeriod_thenThrowIllegalStateException() {
        // Arrange
        Optional<GeneralLookups> emptyResult = Optional.empty();
        when(generalLookupsRepository.findLookupvalueByLookupkey(Mockito.<String>any())).thenReturn(emptyResult);

        // Act and Assert
        assertThrows(IllegalStateException.class, () -> singleSegmentStrategy.fetchNewCustomerPeriod());
        verify(generalLookupsRepository).findLookupvalueByLookupkey(eq("NewCustomerPeriod"));
    }

    /**
     * Test {@link SingleSegmentStrategy#filterAd(Stream, LinkedHashMap)}.
     * <p>
     * Method under test: {@link SingleSegmentStrategy#filterAd(Stream, LinkedHashMap)}
     */
    @Test
    @DisplayName("Test filterAd(Stream, LinkedHashMap)")
    @Tag("MaintainedByDiffblue")
    void testFilterAd() {
        // Arrange
        ArrayList<Ad> adList = new ArrayList<>();
        Stream<Ad> adStream = adList.stream();

        // Act
        Stream<Ad> actualFilterAdResult = singleSegmentStrategy.filterAd(adStream, new LinkedHashMap<>());

        // Assert
        assertTrue(actualFilterAdResult.limit(5).collect(Collectors.toList()).isEmpty());
    }

    @Test
    @DisplayName("filterAd returns Ad when customerTypeId is RETAIL_AND_AGENT_ACCOUNT_TYPE (4L)")
    void testFilterAd_retailAndAgentAccountType() {
        Ad ad = new Ad();
        ad.setCustomerTypeId(4L);
        Stream<Ad> adStream = Stream.of(ad);

        LinkedHashMap<String, Object> ctx = new LinkedHashMap<>();
        // No special context needed

        List<Ad> result = singleSegmentStrategy.filterAd(adStream, ctx).collect(Collectors.toList());
        assertEquals(1, result.size());
        assertEquals(ad, result.get(0));
    }

    /**
     * Test filterAd where adStream contains Ad with customerTypeId = RETAIL_ACCOUNT_TYPE (0L) and beCustomerId == null.
     * Should return the Ad only if segmentationTypeId equals customerTypeId in context.
     */
    @Test
    @DisplayName("filterAd returns Ad for RETAIL_ACCOUNT_TYPE when beCustomerId is null and segmentationTypeId matches")
    void testFilterAd_retailAccountType_match() {
        Ad ad = new Ad();
        ad.setCustomerTypeId(0L);
        ad.setSegmentationTypeId(123L);
        Stream<Ad> adStream = Stream.of(ad);

        LinkedHashMap<String, Object> ctx = new LinkedHashMap<>();
        ctx.put("beCustomerId", null);
        ctx.put("customerTypeId", 123L);

        List<Ad> result = singleSegmentStrategy.filterAd(adStream, ctx).collect(Collectors.toList());
        assertEquals(1, result.size());
        assertEquals(ad, result.get(0));
    }

    /**
     * Test filterAd where adStream contains Ad with customerTypeId = RETAIL_ACCOUNT_TYPE (0L) but beCustomerId is present.
     * Should not return the Ad.
     */
    @Test
    @DisplayName("filterAd returns empty for RETAIL_ACCOUNT_TYPE when beCustomerId is present")
    void testFilterAd_retailAccountType_beCustomerIdPresent() {
        Ad ad = new Ad();
        ad.setCustomerTypeId(0L);
        ad.setSegmentationTypeId(999L);
        Stream<Ad> adStream = Stream.of(ad);

        LinkedHashMap<String, Object> ctx = new LinkedHashMap<>();
        ctx.put("beCustomerId", 1L);
        ctx.put("customerTypeId", 999L);

        List<Ad> result = singleSegmentStrategy.filterAd(adStream, ctx).collect(Collectors.toList());
        assertTrue(result.isEmpty());
    }

    /**
     * Test filterAd where adStream contains Ad with customerTypeId = AGENT_ACCOUNT_TYPE (3L) and beCustomerId is null.
     * Should not return the Ad.
     */
    @Test
    @DisplayName("filterAd returns empty for AGENT_ACCOUNT_TYPE when beCustomerId is null")
    void testFilterAd_agentAccountType_beCustomerIdNull() {
        Ad ad = new Ad();
        ad.setCustomerTypeId(3L);
        ad.setSegmentationTypeId(10L);
        Stream<Ad> adStream = Stream.of(ad);

        LinkedHashMap<String, Object> ctx = new LinkedHashMap<>();
        ctx.put("beCustomerId", null);
        ctx.put("customerTypeId", 10L);

        List<Ad> result = singleSegmentStrategy.filterAd(adStream, ctx).collect(Collectors.toList());
        assertTrue(result.isEmpty());
    }

    /**
     * Test filterAd where adStream contains Ad with customerTypeId = AGENT_ACCOUNT_TYPE (3L) and beCustomerId present,
     * but agentTypeRecords returned from connectionUtil is empty (should return false).
     */
    @Test
    @DisplayName("filterAd returns empty for AGENT_ACCOUNT_TYPE when agentTypeRecords is empty")
    void testFilterAd_agentAccountType_agentTypeRecordsEmpty() {
        Ad ad = new Ad();
        ad.setCustomerTypeId(3L);
        ad.setSegmentationTypeId(10L);
        Stream<Ad> adStream = Stream.of(ad);

        LinkedHashMap<String, Object> ctx = new LinkedHashMap<>();
        ctx.put("beCustomerId", 101L);

        when(connectionUtil.executeSelect(Mockito.anyString(), Mockito.anyList())).thenReturn(new ArrayList<>());

        List<Ad> result = singleSegmentStrategy.filterAd(adStream, ctx).collect(Collectors.toList());
        assertTrue(result.isEmpty());
    }

    /**
     * Test filterAd where adStream contains Ad with customerTypeId = AGENT_ACCOUNT_TYPE (3L), beCustomerId present,
     * agentTypeRecords has a record but AGENT_TYPE_ID does not match ad.getSegmentationTypeId().
     */
    @Test
    @DisplayName("filterAd returns empty for AGENT_ACCOUNT_TYPE when AGENT_TYPE_ID does not match segmentationTypeId")
    void testFilterAd_agentAccountType_agentTypeIdMismatch() {
        Ad ad = new Ad();
        ad.setCustomerTypeId(3L);
        ad.setSegmentationTypeId(999L);
        Stream<Ad> adStream = Stream.of(ad);

        LinkedHashMap<String, Object> ctx = new LinkedHashMap<>();
        ctx.put("beCustomerId", 101L);

        Record rec = new Record();
        rec.put("AGENT_TYPE_ID", new BigDecimal(123)); // does not match ad's segmentationTypeId
        List<Record> records = new ArrayList<>();
        records.add(rec);

        when(connectionUtil.executeSelect(Mockito.anyString(), Mockito.anyList())).thenReturn(records);

        List<Ad> result = singleSegmentStrategy.filterAd(adStream, ctx).collect(Collectors.toList());
        assertTrue(result.isEmpty());
    }

    /**
     * Test filterAd where adStream contains Ad with customerTypeId = AGENT_ACCOUNT_TYPE (3L), beCustomerId present,
     * agentTypeRecords has a record and AGENT_TYPE_ID matches ad.getSegmentationTypeId().
     */
    @Test
    @DisplayName("filterAd returns Ad for AGENT_ACCOUNT_TYPE when AGENT_TYPE_ID matches segmentationTypeId")
    void testFilterAd_agentAccountType_agentTypeIdMatches() {
        Ad ad = new Ad();
        ad.setCustomerTypeId(3L);
        ad.setSegmentationTypeId(123L);
        Stream<Ad> adStream = Stream.of(ad);

        LinkedHashMap<String, Object> ctx = new LinkedHashMap<>();
        ctx.put("beCustomerId", 101L);

        Record rec = new Record();
        rec.put("AGENT_TYPE_ID", new BigDecimal(123)); // matches ad's segmentationTypeId
        List<Record> records = new ArrayList<>();
        records.add(rec);

        when(connectionUtil.executeSelect(Mockito.anyString(), Mockito.anyList())).thenReturn(records);

        List<Ad> result = singleSegmentStrategy.filterAd(adStream, ctx).collect(Collectors.toList());
        assertEquals(1, result.size());
        assertEquals(ad, result.get(0));
    }

    /**
     * Test filterAd where adStream contains Ad with customerTypeId = 55L (unknown type).
     * Should not return the Ad.
     */
    @Test
    @DisplayName("filterAd returns empty for unknown customerTypeId")
    void testFilterAd_unknownCustomerTypeId() {
        Ad ad = new Ad();
        ad.setCustomerTypeId(55L);
        ad.setSegmentationTypeId(1L);
        Stream<Ad> adStream = Stream.of(ad);

        LinkedHashMap<String, Object> ctx = new LinkedHashMap<>();
        ctx.put("customerTypeId", 1L);

        List<Ad> result = singleSegmentStrategy.filterAd(adStream, ctx).collect(Collectors.toList());
        assertTrue(result.isEmpty());
    }
}
