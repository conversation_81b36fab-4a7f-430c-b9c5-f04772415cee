<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">



    <changeSet id="added channel json records to DB" author="Amir Nabil">
        <sql>
            INSERT INTO PRO_MI_NO_C.API_CHANNEL_JSON_SCHEMA (API_CODE, FILE_NAME, CHANNEL_ID)
            VALUES ('112004', 'ValidateOpenAccountData.json', 1);

            INSERT INTO PRO_MI_NO_C.API_CHANNEL_JSON_SCHEMA (API_CODE, FILE_NAME, CHANNEL_ID)
            VALUES ('112004', 'ValidateOpenAccountData.json', 2);
        </sql>
    </changeSet>

    <changeSet id="modified API Data for validate" author="Amir Nabil">
        <sql>
            UPDATE PRO_MI_NO_C.API_VERICASH_APIS t
            SET t.NAME                = 'Validate Open Account Data',
                t.SUCCESS_MESSAGE     = 'Success',
                t.SCHEMA_CONFIG_FILE  = 'ValidateOpenAccountData.json',
                t.MAPPING_CONFIG_FILE = 'ValidateOpenAccountData.xml',
                t.SERVICE_NAME        = 'Validate Open Account Data'
            WHERE t.CODE LIKE '112004' ESCAPE '#';

        </sql>
    </changeSet>


    <changeSet id="modified API Action for validate" author="Amir Nabil">
        <sql>
            UPDATE PRO_MI_NO_C.API_VERICASH_APIS t SET t.REQUEST_ACTION = 'com.cit.vericash.apis.openBankAccount.validateOpenBankAccountData' WHERE t.CODE LIKE '112004' ESCAPE '#';
        </sql>
    </changeSet>
</databaseChangeLog>