package com.cit.vericash.dynamicrequest.transformer;

import com.cit.vericash.api.commons.dao.PaymentMethodServiceDAO;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ApiValidationException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.mastercard.api.core.exception.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class PaymentMethodDynamicGroupTransformer {
    @Autowired
    PaymentMethodServiceDAO paymentMethodServiceDAO;

    final String SOURCE_PAYMENT_METHOD = "sourcePaymentMethod";
    final String DESTINATION_PAYMENT_METHOD = "destinationPaymentMethod";
    final String PAYMENT_METHOD_TYPE = "paymentMethodType";
    final String DYNAMIC_GROUP = "dynamicGroup";
    final String GROUP_ID = "groupId";
    final String GROUP_NAME = "groupName";
    final String INPUT_PARAMETERS = "inputParameters";
    final String ID = "parameterCode";
    final String VALUE = "parameterValue";
    final String PARAMETER_CODE = "parameterCode";
    final String PARAMETER_VALUE = "parameterValue";

    public void transform(Message targetMassage, List<Map<String, Object>> originalDynamicGroup) throws Exception {
        Map<String, Object> sourcePaymentMethod = (Map<String, Object>) targetMassage.getPayload().getAttribute(SOURCE_PAYMENT_METHOD);
        Map<String, Object> destinationPaymentMethod = (Map<String, Object>) targetMassage.getPayload().getAttribute(DESTINATION_PAYMENT_METHOD);

        if (sourcePaymentMethod == null && destinationPaymentMethod == null)
            return;
        else if(destinationPaymentMethod != null && destinationPaymentMethod.get(PAYMENT_METHOD_TYPE) != null && !isNumricString(destinationPaymentMethod.get(PAYMENT_METHOD_TYPE).toString()))
            throw new APIException(ErrorCode.INVALID_PAYMENT_METHOD);
        else if (destinationPaymentMethod != null && destinationPaymentMethod.get(PAYMENT_METHOD_TYPE) != null) {
            Long destinationPaymentMethodType = Long.valueOf(destinationPaymentMethod.get(PAYMENT_METHOD_TYPE).toString());
            Map<String, Object> dynamicGroup = mapToPaymentMethodDynamicGroup(destinationPaymentMethodType, originalDynamicGroup);
            if(dynamicGroup != null)
                destinationPaymentMethod.put(DYNAMIC_GROUP, dynamicGroup);
        }
        else if(sourcePaymentMethod != null && sourcePaymentMethod.get(PAYMENT_METHOD_TYPE) != null && !isNumricString(sourcePaymentMethod.get(PAYMENT_METHOD_TYPE).toString()))
            throw new APIException(ErrorCode.INVALID_PAYMENT_METHOD);
        else if (sourcePaymentMethod != null && sourcePaymentMethod.get(PAYMENT_METHOD_TYPE) != null) {
            Long sourcePaymentMethodType = Long.valueOf(sourcePaymentMethod.get(PAYMENT_METHOD_TYPE).toString());
            Map<String, Object> dynamicGroup = mapToPaymentMethodDynamicGroup(sourcePaymentMethodType, originalDynamicGroup);
            if(dynamicGroup != null)
                sourcePaymentMethod.put(DYNAMIC_GROUP, dynamicGroup);
        }


    }

    private Map<String, Object> mapToPaymentMethodDynamicGroup(Long paymentMethodType, List<Map<String, Object>> originalDynamicGroup) {

        Long groupIdDb = paymentMethodServiceDAO.PaymentMethodInputParametersGroupId(paymentMethodType);
        List<Map<String, Object>> collect = new ArrayList<>();
        if (groupIdDb != null) {
            collect = originalDynamicGroup.stream()
                    .filter(dynamicGroup -> Long.valueOf(dynamicGroup.get(GROUP_ID).toString()).equals(groupIdDb))
                    .map(dynamicGroup -> {
                        Long groupId = Long.valueOf(dynamicGroup.get(GROUP_ID).toString());
                        String groupName = dynamicGroup.get(GROUP_NAME).toString();
                        Map<String, Object> inputParameters = (Map<String, Object>) dynamicGroup.get(INPUT_PARAMETERS);
                        List<Map<String, Object>> inputParametersNewList = inputParameters.values().stream()
                                .map(eachInputValueObject -> {
                                    Map<String, Object> requiredInputParametersStructure = new HashMap<>();
                                    Long paramCode = Long.valueOf(((Map<String, Object>) eachInputValueObject).get(ID).toString());
                                    String paramValue = ((Map<String, Object>) eachInputValueObject).get(VALUE).toString();
                                    requiredInputParametersStructure.put(PARAMETER_CODE, paramCode);
                                    requiredInputParametersStructure.put(PARAMETER_VALUE, paramValue);
                                    return requiredInputParametersStructure;
                                }).collect(Collectors.toList());
                        Map<String, Object> newDynamicGroup = new HashMap<>();
                        newDynamicGroup.put(GROUP_ID, groupId);
                        newDynamicGroup.put(GROUP_NAME, groupName);
                        newDynamicGroup.put(INPUT_PARAMETERS, inputParametersNewList);
                        return newDynamicGroup;
                    })
                    .collect(Collectors.toList());
        }
        if(collect.isEmpty())
            return null;
        return collect.get(0);
    }
    private boolean isNumricString(String str){
        try{
            Long.valueOf(str);
            return true;
        } catch (NumberFormatException numberFormatException){
            return false;
        }
    }

}


