enableBackendSecurity=true

Authentications_key=_Authentications

lookupFW_url=${LOOKUP_FRAMEWORK_URL}
transactionHistorySeparatedList=false

transactionHistoryListOrder=DESCENDING

enableDynamicAuth=true
entrustUrl =http://222.222.100.67:9001/entrust
debitCardValidationUrl=http://222.222.100.67:8080/DebitCardValidation
cardAuthenticationSuccesCode = 00
spring.main.allow-bean-definition-overriding=true
validate.mobile.number.Error=Mobile number already registered
