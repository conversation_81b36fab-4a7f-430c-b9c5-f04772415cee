package com.cit.vericash.apis.transferpoints;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.receipt.PaymentMethodAccountIdentifier;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class TransferPointsResponseTransformer implements MuleResponseTransformer {

    @Autowired
    PaymentMethodAccountIdentifier paymentMethodAccountIdentifier;

    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {

        String balance=businessMessage.getSourcePaymentMethod().getPaymentBalance();

        Map<String, Object> result = new HashedMap();
        result.put("paymentBalance", balance);
        return result;
    }
}
