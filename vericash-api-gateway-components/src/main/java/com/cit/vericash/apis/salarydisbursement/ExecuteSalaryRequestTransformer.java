package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.common.group.transfers.ExecuteGroupTransfer;
import com.cit.mpaymentapp.common.message.PaymentMethodDetail;
import com.cit.mpaymentapp.model.Enums;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursemen.ExecuteSalaryRequestTransformer")
public class ExecuteSalaryRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        message.getPayload().setAttribute("ownerType", Enums.OwnerTypeEnum.CUSTOMER);

        Gson gson = new Gson();

        String executeGroupTransferJson = gson.toJson(message.getPayload().getAttribute("executeGroupTransfer"));
        ExecuteGroupTransfer executeGroupTransfer = gson.fromJson(executeGroupTransferJson, ExecuteGroupTransfer.class);
        message.getPayload().setAttribute("executeGroupTransfer", executeGroupTransfer);

        String paymentMethodJson = gson.toJson(message.getPayload().getAttribute("paymentMethod"));
        PaymentMethodDetail paymentMethodDetail = gson.fromJson(paymentMethodJson, PaymentMethodDetail.class);
        message.getPayload().setAttribute("paymentMethod", paymentMethodDetail);
        /*
        List<GroupTransferRecipientDTO> groupTransferRecipient =
                (List<GroupTransferRecipientDTO>) message.getPayload().getAttribute("groupTransferRecipients");
        message.getPayload().setAttribute("groupTransferRecipients", groupTransferRecipient);
         */
    }
}
