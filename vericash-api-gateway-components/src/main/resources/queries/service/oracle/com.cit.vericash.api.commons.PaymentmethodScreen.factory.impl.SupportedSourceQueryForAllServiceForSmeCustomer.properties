createQuery_SME=SELECT  PM. NAME "paymentMethodName" , SPM. ID "paymentMethodType",SPMS.API_CODE, \
  SPMS.USER_TYPE,SPMS.VAILDATE_PM,PM.PAYMETN_METHOD_ORDER \
  FROM CUSTOMER C JOIN PAYMENT_METHOD_CUST_PROF_CG PMCP ON C.CUSTOMER_TYPE_ID = PMCP.CUSTOMER_TYPE_ID \
  JOIN PAYMENT_METHOD PM ON PM. ID = PMCP.PAYMENT_METHOD_ID \
  JOIN SOURCE_PAYMENT_METHOD SPM ON SPM. ID = PM. ID \
  left join SERVICES_PAYMENTMETHOD  SPMS on  SPM.ID =SPMS.PAYMENMETHODTYPE \
  where C.CUSTOMER_ID = ( select  BE.MAIN_CUSTOMER_ID  From  BE_CUSTOMER  BE \
  LEFT JOIN CUSTOMER C  ON C.BE_CUSTOMER_ID = BE.ID   where C.USER_ID= ? ) \
  AND SPMS.LOOKUP_ENUM = ?  AND  SPMS.USER_TYPE = ? 
apiCodeCondition=AND SPMS.API_CODE in ( %1$s )
