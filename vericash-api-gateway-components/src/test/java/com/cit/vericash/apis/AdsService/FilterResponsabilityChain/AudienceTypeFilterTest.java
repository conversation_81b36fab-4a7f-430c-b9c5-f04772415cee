package com.cit.vericash.apis.AdsService.FilterResponsabilityChain;

import com.cit.vericash.api.entity.Ad;
import com.cit.vericash.apis.dto.request.Request;
import com.mastercard.api.core.exception.ApiException;
import oracle.sql.TIMESTAMP;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.LinkedHashMap;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class AudienceTypeFilterTest {

    private AudienceTypeFilter audienceTypeFilter;

    @Mock
    private AdsFilterHandler nextHandler;

    @Mock
    private Request request;

    private LinkedHashMap<String, Object> requiredData = new LinkedHashMap<>();

    @BeforeEach
    void setUp() throws Exception {
        requiredData.put("newCustomerPeriod", 30);
        requiredData.put("creationDate", new TIMESTAMP(Timestamp.from(Instant.now().minus(15, ChronoUnit.DAYS))));
        audienceTypeFilter = new AudienceTypeFilter(requiredData);
    }

    @Test
    void handleRequest_shouldFilterAdsByAudienceType() throws ApiException {
        // Arrange
        Ad ad1 = new Ad();
        ad1.setAudienceId(0L); // REGISTERED
        Ad ad2 = new Ad();
        ad2.setAudienceId(2L); // NEW_REGISTERED
        Ad ad3 = new Ad();
        ad3.setAudienceId(1L); // REGISTERED_AND_NEW_REGISTERED

        Stream<Ad> adStream = Stream.of(ad1, ad2, ad3);

        // Act
        Stream<Ad> result = audienceTypeFilter.handleRequest(request, adStream);

        // Assert
        assertEquals(2, result.count()); // ad1 and ad3 should pass
    }

    @Test
    void handleRequest_shouldThrowApiExceptionOnError() {
        // Arrange
        requiredData.put("creationDate", "invalid-date");
        audienceTypeFilter = new AudienceTypeFilter(requiredData);
        Stream<Ad> adStream = Stream.empty();

        // Act & Assert
        assertThrows(ApiException.class, () -> audienceTypeFilter.handleRequest(request, adStream));
    }

    @Test
    void filterAdsByAudienceType_shouldIncludeAllWhenAudienceTypeIsBoth() {
        // Arrange
        Ad ad1 = new Ad();
        ad1.setAudienceId(1L); // REGISTERED_AND_NEW_REGISTERED

        // Act
        Stream<Ad> result = audienceTypeFilter.filterAdsByAudienceType(Stream.of(ad1), 0);

        // Assert
        assertEquals(1, result.count());
    }

    @Test
    void determineCustomerAudienceTypeId_shouldReturnNewRegisteredWhenWithinPeriod() {
        // Act
        Integer result = audienceTypeFilter.determineCustomerAudienceTypeId(30L, 15L);

        // Assert
        assertEquals(2, result); // NEW_REGISTERED
    }

    @Test
    void determineCustomerAudienceTypeId_shouldReturnRegisteredWhenOutsidePeriod() {
        // Act
        Integer result = audienceTypeFilter.determineCustomerAudienceTypeId(30L, 31L);

        // Assert
        assertEquals(0, result); // REGISTERED
    }
}