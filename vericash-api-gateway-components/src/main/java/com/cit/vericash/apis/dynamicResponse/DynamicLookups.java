package com.cit.vericash.apis.dynamicResponse;

import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.LookupFramework.common.util.enums;
import com.cit.vericash.LookupFramework.common.util.response.LookupResponse;
import com.cit.vericash.LookupFramework.manager.LookupFrameWorkManager;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.backend.commons.dynamicpayload.AdditionalData;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Qualifier("com.cit.vericash.apis.dynamicResponse.DynamicLookups")
public class DynamicLookups {
    @Autowired
    LookupFrameWorkManager lookupFrameWorkManager;

    @Autowired
    DynamicCommons dynamicCommons;

    public LinkedHashMap<String, Object> getLookup(Request request) throws GeneralFailureException {
        String language = request.getMessage().getPayload().getAttributeAsString("language");
        Payload lookupData = new Payload();
        Payload payload = new Payload();
        //Payload payload = request.getMessage().getPayload();
        Header header =request.getMessage().getHeader();
        AdditionalData additionalData =request.getMessage().getAdditionalData();
        payload.putAll(additionalData);
        payload.putAll(header);
        payload.putAll(request.getMessage().getPayload());
        LookupResponse lookupResponse;
        Object lookups = payload.getAttribute("lookupList");
        if (language != null && !language.equals("en")) {
            lookups = updateLookups(lookups , language);
        }
        lookupData.setAttribute("filters", payload);
        lookupData.setAttribute("lookups", lookups);
        lookupResponse = lookupFrameWorkManager.getLookups(lookupData);
        if (lookupResponse==null || lookupResponse.size() == 0) {
            Map<String, String> errMsg = new HashMap<>();
            errMsg.put("invalid.lookupList", "There is a problem with your lookup please check your query");
            throw new GeneralFailureException(ErrorCode.INVALID_LOOKUP.getErrorCode(), errMsg);
//            throw new GeneralFailureException("There is a problem with your lookup please check your query");
        }
        Set<String> keys = lookupResponse.keySet();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        resultMap = dynamicCommons.extractResultMap(lookupResponse, keys, resultMap);
        LinkedHashMap<String, Object> updatedResultMap = updateKeys(resultMap, keys);
        return updatedResultMap;
    }

    public static LinkedHashMap<String, Object> updateKeys(
            LinkedHashMap<String, Object> resultMap,
            Set<String> keys) {

        LinkedHashMap<String, Object> updates = new LinkedHashMap<>();
            for (String key : keys) {
                String newKey = enums.TranslatedLookups.getOriginalValueIfContains(key);
                if (resultMap.containsKey(key) && !key.equals(newKey) && enums.TranslatedLookups.isKeySimilar(key)) {
                    Object value = resultMap.remove(key);
                    updates.put(newKey, value);
                }
            }
        // Apply the updates to the original map
        for (String newKey : updates.keySet()) {
            if (!resultMap.containsKey(newKey)) {
                resultMap.put(newKey, updates.get(newKey));
            }
        }
        return resultMap;
    }



    public List<String> updateLookups(Object lookups, String lang) {

        if (lookups == null || lang == null || lang.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> updatedLookups = new ArrayList<>();

        if (lookups instanceof List) {
            List<?> lookupList = (List<?>) lookups;

            for (Object obj : lookupList) {
                if (obj instanceof String) {
                    String lookup = (String) obj;
                    String filteredKey = filterBasedOnEnumKey(lookup);

                    // Update the lookup if it matches the filtered key
                    if (lookup.equals(filteredKey)) {
                        updatedLookups.add(lookup + "." + lang);
                    } else {
                        // Keep the original lookup if it doesn't match
                        updatedLookups.add(lookup);
                    }
                }
            }
        }
        return updatedLookups;
    }

    public String filterBasedOnEnumKey(String key) {
        enums.TranslatedLookups translatedLookups = enums.TranslatedLookups.getMatchedTranslatedLookup(key);
        if (translatedLookups != null) {
            return translatedLookups.getKey();
        }
        return null;
    }

}
