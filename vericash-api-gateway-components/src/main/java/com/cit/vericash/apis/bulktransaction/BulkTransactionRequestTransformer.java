package com.cit.vericash.apis.bulktransaction;

import com.cit.vericash.apis.calculatefee.GroupCalculateFeesRequestTransformer;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.dto.request.Request;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import static com.cit.vericash.apis.calculatefee.GroupCalculateFeesRequestTransformer.SERVICE_TYPE;

@Component
@Qualifier("com.cit.vericash.apis.bulktransaction.BulkTransactionRequestTransformer")
@RequiredArgsConstructor
@Slf4j
public class BulkTransactionRequestTransformer implements VericashAction {
    public static final String API_CODE = "apiCode";
    private final GroupCalculateFeesRequestTransformer groupCalculateFeesRequestTransformer;
    @Override
    public Object process(Request request) throws Exception {
        log.info("Inside BulkTransactionRequestTransformer");
        request.getMessage().getPayload().put(SERVICE_TYPE, request.getMessage().getHeader().getAttributeAsString(API_CODE));
        groupCalculateFeesRequestTransformer.process(request);
        return request;
    }
}
