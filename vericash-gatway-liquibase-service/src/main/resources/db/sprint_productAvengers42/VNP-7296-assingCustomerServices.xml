<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

  <changeSet id="creat " author="fatma">
    <comment>Insert service config and business service config for points views</comment>
    
    <!-- SERVICE_CONFIG_MAP inserts -->
    <insert tableName="SERVICE_CONFIG_MAP" >
        <column name="SERVICE_TYPE_ID" value="************"/>
        <column name="IS_GENERIC_SERVICE" value="0"/>
        <column name="SERVICE_TYPE_NAME" value="View Transfered Points"/>
        <column name="SERVICE_MODE" value="0"/>
        <column name="CATEGORY_ID" value="NULL"/>
        <column name="ORGANIZATION_ID" value="NULL"/>
        <column name="SERVICE_TYPE" value="NULL"/>
        <column name="SERVICE_CODE" value="************"/>
        <column name="IS_INDEMNITY" value="1"/>
        <column name="SEPARATE_QUEUE" value="0"/>
        <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
        <column name="HAS_WORKFLOW" value="0"/>
        <column name="SERVICE_CATEGORY" value="0"/>
        <column name="SERVICE_CATEGORY_ID" value="NULL"/>
        <column name="HOME_SCREEN_CONFIG" value="NULL"/>
        <column name="ENABLED_PROFILES" value="NULL"/>
        <column name="PARENT_SERVICE_CODE" value="NULL"/>
        <column name="ROLE_BASED" value="0"/>
        <column name="SERVICE_TYPE_NAME_FR" value="NULL"/>
        <column name="SERVICE_TYPE_NAME_PT" value="NULL"/>
    </insert>

    <insert tableName="SERVICE_CONFIG_MAP" >
        <column name="SERVICE_TYPE_ID" value="************"/>
        <column name="IS_GENERIC_SERVICE" value="0"/>
        <column name="SERVICE_TYPE_NAME" value="View Redeemed Points"/>
        <column name="SERVICE_MODE" value="0"/>
        <column name="CATEGORY_ID" value="NULL"/>
        <column name="ORGANIZATION_ID" value="NULL"/>
        <column name="SERVICE_TYPE" value="NULL"/>
        <column name="SERVICE_CODE" value="************"/>
        <column name="IS_INDEMNITY" value="1"/>
        <column name="SEPARATE_QUEUE" value="0"/>
        <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
        <column name="HAS_WORKFLOW" value="0"/>
        <column name="SERVICE_CATEGORY" value="0"/>
        <column name="SERVICE_CATEGORY_ID" value="NULL"/>
        <column name="HOME_SCREEN_CONFIG" value="NULL"/>
        <column name="ENABLED_PROFILES" value="NULL"/>
        <column name="PARENT_SERVICE_CODE" value="NULL"/>
        <column name="ROLE_BASED" value="0"/>
        <column name="SERVICE_TYPE_NAME_FR" value="NULL"/>
        <column name="SERVICE_TYPE_NAME_PT" value="NULL"/>
    </insert>

    <insert tableName="SERVICE_CONFIG_MAP" >
        <column name="SERVICE_TYPE_ID" value="************"/>
        <column name="IS_GENERIC_SERVICE" value="0"/>
        <column name="SERVICE_TYPE_NAME" value="View Received Points"/>
        <column name="SERVICE_MODE" value="0"/>
        <column name="CATEGORY_ID" value="NULL"/>
        <column name="ORGANIZATION_ID" value="NULL"/>
        <column name="SERVICE_TYPE" value="NULL"/>
        <column name="SERVICE_CODE" value="************"/>
        <column name="IS_INDEMNITY" value="1"/>
        <column name="SEPARATE_QUEUE" value="0"/>
        <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
        <column name="HAS_WORKFLOW" value="0"/>
        <column name="SERVICE_CATEGORY" value="0"/>
        <column name="SERVICE_CATEGORY_ID" value="NULL"/>
        <column name="HOME_SCREEN_CONFIG" value="NULL"/>
        <column name="ENABLED_PROFILES" value="NULL"/>
        <column name="PARENT_SERVICE_CODE" value="NULL"/>
        <column name="ROLE_BASED" value="0"/>
        <column name="SERVICE_TYPE_NAME_FR" value="NULL"/>
        <column name="SERVICE_TYPE_NAME_PT" value="NULL"/>
    </insert>

    
</changeSet>
<changeSet id="add_in _business_service_config" author="fatma">
    <comment>Insert business service config with dynamic ID generation using Liquibase tags</comment>

    <!-- First record - View Transfered Points -->
    <insert tableName="BUSINESS_SERVICE_CONFIG" >
        <column name="ID" valueComputed="(SELECT MAX(ID)+1 FROM BUSINESS_SERVICE_CONFIG)"/>
        <column name="BANKING_AGENT" value="0"/>
        <column name="BUSINESSSERVICECATEGORY" value="0"/>
        <column name="ISDEFAULTSERVICE" value="1"/>
        <column name="IS_EXPOSABLE" value="0"/>
        <column name="IS_MULTI_WALLET" value="0"/>
        <column name="NAME" value="View Transfered Points"/>
        <column name="BUSINESS_SERVICE_TYPE" value="************"/>
        <column name="SEGMENTATIONTYPE_ID" value="3"/>
        <column name="ORGANIZATION_ID" value="2421"/>
        <column name="VERSION" value="NULL"/>
        <column name="IS_ENABLED" value="1"/>
        <column name="CATEGORY" value="NULL"/>
        <column name="NARRATION" value="NULL"/>
    </insert>

    <!-- Second record - View Redeemed Points -->
    <insert tableName="BUSINESS_SERVICE_CONFIG" >
        <column name="ID" valueComputed="(SELECT MAX(ID)+1 FROM BUSINESS_SERVICE_CONFIG)"/>
        <column name="BANKING_AGENT" value="0"/>
        <column name="BUSINESSSERVICECATEGORY" value="0"/>
        <column name="ISDEFAULTSERVICE" value="1"/>
        <column name="IS_EXPOSABLE" value="0"/>
        <column name="IS_MULTI_WALLET" value="0"/>
        <column name="NAME" value="View Redeemed Points"/>
        <column name="BUSINESS_SERVICE_TYPE" value="************"/>
        <column name="SEGMENTATIONTYPE_ID" value="3"/>
        <column name="ORGANIZATION_ID" value="2421"/>
        <column name="VERSION" value="NULL"/>
        <column name="IS_ENABLED" value="1"/>
        <column name="CATEGORY" value="NULL"/>
        <column name="NARRATION" value="NULL"/>
    </insert>

    <!-- Third record - View Received Points -->
    <insert tableName="BUSINESS_SERVICE_CONFIG" >
        <column name="ID" valueComputed="(SELECT MAX(ID)+1 FROM BUSINESS_SERVICE_CONFIG)"/>
        <column name="BANKING_AGENT" value="0"/>
        <column name="BUSINESSSERVICECATEGORY" value="0"/>
        <column name="ISDEFAULTSERVICE" value="1"/>
        <column name="IS_EXPOSABLE" value="0"/>
        <column name="IS_MULTI_WALLET" value="0"/>
        <column name="NAME" value="View Received Points"/>
        <column name="BUSINESS_SERVICE_TYPE" value="************"/>
        <column name="SEGMENTATIONTYPE_ID" value="3"/>
        <column name="ORGANIZATION_ID" value="2421"/>
        <column name="VERSION" value="NULL"/>
        <column name="IS_ENABLED" value="1"/>
        <column name="CATEGORY" value="NULL"/>
        <column name="NARRATION" value="NULL"/>
    </insert>
</changeSet>
<changeSet id="alter-app-menus-service-type" author="fatma">
    <comment>Modify SERVICE_TYPE_ID column to NUMBER(19,0) with DEFAULT NULL in APP_MENUS table</comment>
    
    <modifyDataType 
        tableName="APP_MENUS" 
        columnName="SERVICE_TYPE_ID" 
        newDataType="NUMBER(19,0)"
        />
        
    <addDefaultValue 
        tableName="APP_MENUS" 
        columnName="SERVICE_TYPE_ID" 
        defaultValue="NULL"
         />
</changeSet>


	
</databaseChangeLog>




