//package com.cit.vericash.apis.commons.util;
//
//import static org.junit.Assert.assertNotNull;
//import static org.junit.Assert.assertNull;
//import static org.junit.Assert.assertThat;
//import static org.mockito.Matchers.anyList;
//import static org.mockito.Matchers.anyLong;
//import static org.mockito.Matchers.anyString;
//import static org.mockito.Mockito.atLeast;
//import static org.mockito.Mockito.atLeastOnce;
//import static org.mockito.Mockito.never;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//import java.math.BigDecimal;
//import java.sql.SQLException;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.BDDMockito;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import com.cit.vericash.apis.commons.model.VericashAPI;
//import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
//import com.cit.vericash.apis.commons.util.connection.Record;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest(VericashApiLoader.class)
//public class CacheableVericashAPiImplTest {
//
//	@Test
//	public void test_getVericashAPI_AddVericashApiInMap_AddSuucessfull() throws NumberFormatException, Exception {
//		VericashAPI mockVericashAPI = initSampleVericashApi();
//         VericashApiLoader vericashApiLoader = Mockito.mock(VericashApiLoader.class);
//	     when(vericashApiLoader.LoadVericahApi(new Long("1234"))).thenReturn(mockVericashAPI);
//
//		CacheableVericashAPiImpl cacheableVericashAPiImpl = new CacheableVericashAPiImpl(vericashApiLoader);
//		VericashAPI vericashAPI = null;
//		for(int i= 0 ;i < 3 ;i++) {
//		 vericashAPI = cacheableVericashAPiImpl.getVericashAPI(new Long(1234));
//		}
//		assertNotNull(vericashAPI);
//		verify(vericashApiLoader,times(1)).LoadVericahApi(anyLong());
//	}
//
//	@Test
//	public void test_getVericashAPI_WithNullVerivashApi_CacheFailld() throws NumberFormatException, Exception {
//         VericashApiLoader vericashApiLoader = Mockito.mock(VericashApiLoader.class);
//	     when(vericashApiLoader.LoadVericahApi(new Long("1235"))).thenReturn(null);
//
//		CacheableVericashAPiImpl cacheableVericashAPiImpl = new CacheableVericashAPiImpl(vericashApiLoader);
//		VericashAPI vericashAPI = cacheableVericashAPiImpl.getVericashAPI(new Long(1235));
//		assertNull(vericashAPI);
//
//	}
//
//	/*@Test
//	public void test_getVericashAPI_withApiLoaderError_ThrowException() throws NumberFormatException, Exception {
//		VericashAPI mockVericashAPI = initSampleVericashApi();
//         VericashApiLoader vericashApiLoader = Mockito.mock(VericashApiLoader.class);
//	     when(vericashApiLoader.LoadVericahApi(new Long("1236"))).thenThrow(Exception.class);
//
//		CacheableVericashAPiImpl cacheableVericashAPiImpl = new CacheableVericashAPiImpl(vericashApiLoader);
//		VericashAPI vericashAPI = cacheableVericashAPiImpl.getVericashAPI(new Long(1234));
//
//		assertNull(vericashAPI);
//
//	}*/
//
//
//	private VericashAPI initSampleVericashApi() {
//		 VericashAPI vericashAPI = new VericashAPI();
//		 vericashAPI.setCode(new Long(1234));
//		 vericashAPI.setName("UBA");
//		 vericashAPI.setServiceCode("123");
//		 vericashAPI.setInboundQueue(null);
//		 vericashAPI.setOutboundQueue(null);
//		 vericashAPI.setMappingConfigFile(null);
//		 vericashAPI.setOutboundToMule(false);
//		 vericashAPI.setRequestAction(null);
//		 vericashAPI.setRequestAction(null);
//		 vericashAPI.setResponseAction(null);
//		 vericashAPI.setSchemaConfigFile(null);
//
//    	return vericashAPI;
//	}
//
//}
