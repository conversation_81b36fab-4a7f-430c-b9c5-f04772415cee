package com.cit.vericash.apis.transactionreceiptinfo;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class GetTransactionReceiptInformationRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String transactionReceiptInformationKey = "transactionReceiptInformation";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
//        ObjectMapper mapper = new ObjectMapper();
//        Object transactionReceiptInformationObject = message.getPayload().getAttribute(cacheableFieldParameters.getRequestParameters(transactionReceiptInformationKey));
//        if (transactionReceiptInformationObject != null) {
//            String respData = mapper.writeValueAsString(message.getPayload().getAttribute(cacheableFieldParameters.getResponseParameters(transactionReceiptInformationKey)));
//            TransactionReceiptInformation transactionReceiptInformation = (TransactionReceiptInformation) mapper.readValue(respData, ExternalTransactionInformation.class);
//            message.getPayload().put("TransactionReceiptInformation", transactionReceiptInformation);
//        }
    }
}
