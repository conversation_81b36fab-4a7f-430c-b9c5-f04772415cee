package com.cit.vericash.apis.selfregistration;

import com.cit.mpaymentapp.common.message.Gender;
import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.*;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.mastercard.api.core.exception.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Qualifier("com.cit.vericash.apis.selfregistration.CustomerSelfRegistrationRequestTransformer")
public class CustomerSelfRegistrationRequestTransformer extends MessageToBusinessMessageTransformerImpl {
	@Autowired
	private CacheableWalletInfo walletInfoCache;
	@Autowired
	private CacheableFieldParameters cacheableFieldParameters ;
	@Autowired
	MsisdnFormatterImpl msisdnFormatter;
	private final String OWNERTYPE="sender-OwnerType";
	private final String WALLET_SHORT_CODE="wallet-Short-Code";
	private final String CURRENCY="currency";
	private final String CONTACT_US="contact-US";
	private final String COUNTRY_ISO2="country-iso2";
	private final String MSISDN="msisdn";
	private final String GENDER="gender";
	private final String USER_KEY="user-Key";
	private final String IS_SELF_REG="is-self-reg";


	

	
	
//	public CustomerSelfRegistrationRequestTransformer() {
//		walletInfoCache = new CacheableWalletInfoImpl();
//
//	}


	@Override
	protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
		String walletShortCodeKey = cacheableFieldParameters.getRequestParameters(WALLET_SHORT_CODE);
		String walletShortCode = message.getHeader().getAttributeAsString(walletShortCodeKey);
		WalletInfo walletInfo = walletInfoCache.getWalletInfo(walletShortCode);
		Pattern regex = Pattern.compile("[^A-Za-z]");
		Matcher matcherFirst = regex.matcher(message.getPayload().getAttributeAsString("firstName"));
		Matcher matcherLast = regex.matcher(message.getPayload().getAttributeAsString("lastName"));

		int registrationChannelId=message.getPayload().getAttributeAsInteger("registrationChannelId");
		String campaignId=message.getPayload().getAttributeAsString("campaignId");
		if ((registrationChannelId==2 || registrationChannelId==3) && campaignId==null){
			throw new APIException("campaign Id cannot be null");
		}
		if (matcherFirst.find() || matcherLast.find()) {
			throw new APIException("Invalid Name Format",
					ErrorCode.INVALID_NAME_FORMAT.getErrorCode());
		}
		String gender = message.getPayload().getAttributeAsString("gender");
		CustomerGenderValidation(gender);
		try{
			String ownerTypeKey = cacheableFieldParameters.getRequestParameters(OWNERTYPE);
			String ownerTypeString = message.getPayload().getAttributeAsString(ownerTypeKey);
			if (ownerTypeString==null || ownerTypeString.equals("None"))
				throw new APIException("Invalid senderOwnerType");
			String genderKey = cacheableFieldParameters.getRequestParameters(GENDER);
			String msisdnKey = cacheableFieldParameters.getRequestParameters(MSISDN);
			String msisdn = message.getPayload().getAttributeAsString(msisdnKey);
			String region =walletInfo.getCountryIso2();

			msisdn = msisdnFormatter.formatMSISDN(msisdn, region,walletShortCode);

			String userKey = message.getHeader().getAttributeAsString(walletShortCodeKey)+msisdn;
			Gender genderEnum = EnumTransformer.convertGender(gender);
		//	OwnerTypeEnum ownerTypeEnum = converOwnerType(ownerTypeString);
			String userKey_Key = cacheableFieldParameters.getRequestParameters(USER_KEY);
			String selfReg_Key = cacheableFieldParameters.getRequestParameters(IS_SELF_REG);


			
			message.getPayload().put(cacheableFieldParameters.getRequestParameters(CURRENCY), walletInfo.getCurrency());
			message.getPayload().put(userKey_Key, userKey);
			message.getPayload().put(selfReg_Key, new Boolean(true));
			message.getPayload().put(cacheableFieldParameters.getRequestParameters(CONTACT_US),walletInfo.getContactUS());
			message.getPayload().put(cacheableFieldParameters.getRequestParameters(COUNTRY_ISO2),walletInfo.getCountryIso2());
			message.getPayload().put(genderKey, genderEnum);
		//message.getPayload().put(ownerTypeKey, ownerTypeEnum);
			message.getPayload().put(msisdnKey,msisdn);
		}catch (APIException e){
			e.printStackTrace();
			throw new APIException("Invalid senderOwnerType",
					ErrorCode.INVALID_SENDEROWNERTYPE.getErrorCode());
		}catch(Exception e){
			throw e;
		}
	}
	
	

	void CustomerGenderValidation(String gender){
		//genderValidation

		try {
			if (gender==null||gender.isEmpty())
				return;
			Gender genderEnum = EnumTransformer.convertGender(gender);
			Gender.valueOf(genderEnum.name());
		}
		catch (Exception e){
			throw new APIException(ErrorCode.INVALID_CUSTOMER_GENDER.getErrorCode());
		}
	}


}
