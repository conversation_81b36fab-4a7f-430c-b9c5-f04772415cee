<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="insert-business-service-configs" author="khaled">
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Own Transfer BU mkento wallet to prepaid card',-->
<!--                    134,-->
<!--                    0,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--    </changeSet>-->

<!--    <changeSet id="insert-business-service-configs-2" author="yourname">-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Own Transfer BU mkento wallet to mkento wallet',-->
<!--                    133,-->
<!--                    0,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--    </changeSet>-->

<!--    <changeSet id="insert-business-service-configs-3" author="yourname">-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Own Transfer prepaid card to prepaid card',-->
<!--                    132,-->
<!--                    0,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--    </changeSet>-->

<!--    <changeSet id="insert-business-service-configs-4" author="yourname">-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Own Transfer prepaid card to mkento wallet',-->
<!--                    131,-->
<!--                    0,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
        <sql>
            <![CDATA[
            INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
                ID,
                BANKING_AGENT,
                BUSINESSSERVICECATEGORY,
                ISDEFAULTSERVICE,
                IS_EXPOSABLE,
                IS_MULTI_WALLET,
                NAME,
                BUSINESS_SERVICE_TYPE,
                SEGMENTATIONTYPE_ID,
                ORGANIZATION_ID,
                IS_ENABLED
            ) VALUES (
                 (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
                0,
                0,
                1,
                0,
                0,
                'Own Transfer mkento wallet to prepaid card',
                130,
                0,
                2421,
                1
            );
        ]]>
        </sql>
        <sql>
            <![CDATA[
            INSERT INTO PRO_MI_NO_C.BUSINESS_SERVICE_CONFIG (
                ID,
                BANKING_AGENT,
                BUSINESSSERVICECATEGORY,
                ISDEFAULTSERVICE,
                IS_EXPOSABLE,
                IS_MULTI_WALLET,
                NAME,
                BUSINESS_SERVICE_TYPE,
                SEGMENTATIONTYPE_ID,
                ORGANIZATION_ID,
                IS_ENABLED
            ) VALUES (
                 (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),
                0,
                0,
                1,
                0,
                0,
                'Own Transfer mkento wallet to mkento wallet',
                153,
                0,
                2421,
                1
            );
        ]]>
        </sql>
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                )-->
<!--                VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From VC To Prepaid Card GTP by Customer',-->
<!--                    154,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Own Transfer mkento wallet to mkento wallet',-->
<!--                    135,-->
<!--                    0,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From VC To Debit Card by Customer',-->
<!--                    152,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From VC To CIB by Customer',-->
<!--                    151,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From Prepaid To Debit Card by Customer',-->
<!--                    150,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From Prepaid To CIB by Customer',-->
<!--                    149,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From Wallet To Debit Card by Customer',-->
<!--                    148,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From Wallet To CIB by Customer',-->
<!--                    147,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From Debit Card To Debit Card by Customer',-->
<!--                    146,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From Debit Card To Prepaid by Customer',-->
<!--                    145,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From Debit Card To Wallet by Customer',-->
<!--                    144,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Internal Transfer - From Debit Card To VC by Customer',-->
<!--                    143,-->
<!--                    3,-->
<!--                    2421,-->
<!--                     1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Top Up Wallet with Debit Card',-->
<!--                    142,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Top Up Wallet with VC',-->
<!--                    141,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Top Up Wallet with Prepaid',-->
<!--                    140,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Cash in With Cash',-->
<!--                    139,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Cash in With Debit Card',-->
<!--                    138,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Cash in With VC',-->
<!--                    137,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--                INSERT INTO BUSINESS_SERVICE_CONFIG (-->
<!--                    ID,-->
<!--                    BANKING_AGENT,-->
<!--                    BUSINESSSERVICECATEGORY,-->
<!--                    ISDEFAULTSERVICE,-->
<!--                    IS_EXPOSABLE,-->
<!--                    IS_MULTI_WALLET,-->
<!--                    NAME,-->
<!--                    BUSINESS_SERVICE_TYPE,-->
<!--                    SEGMENTATIONTYPE_ID,-->
<!--                    ORGANIZATION_ID,-->
<!--                    IS_ENABLED-->
<!--                ) VALUES (-->
<!--                    (SELECT COALESCE(MAX(ID), 0) + 1 FROM BUSINESS_SERVICE_CONFIG),-->
<!--                    0,-->
<!--                    0,-->
<!--                    1,-->
<!--                    0,-->
<!--                    0,-->
<!--                    'Cash Out From Wallet',-->
<!--                    136,-->
<!--                    3,-->
<!--                    2421,-->
<!--                    1-->
<!--                );-->
<!--            ]]>-->
<!--        </sql>-->
    </changeSet>
</databaseChangeLog>
