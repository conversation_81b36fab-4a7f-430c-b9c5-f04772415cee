<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
		xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
		xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
   http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">


	<!-- old one because there is exist code used in INTE and TEST--> 
	<!--<changeSet failOnError="true" author="fatma" id="CIBSource">
		<insert tableName="PAYMENT_METHOD">
			<column name="ID" valueComputed="855"/>
			<column name="AVAILABILITY" value="1"/>
			<column name="IS_DEFAULT" value="0"/>
			<column name="NAME" value="CIBSource"/>
			<column name="TYPE" value="0"/>
			<column name="BUSINESS_ENTITY_ID" value="2421"/>
			<column name="CURRENCY_OR_UNIT" value="R"/>
			<column name="GROUP_ID" value="43430"/>
			<column name="PAYMETN_METHOD_ORDER" value="5"/>
			<column name="PAYMENT_METHOD_IDENTIFIER_CODE" value="0"/>
		</insert>
	</changeSet>
	<changeSet failOnError="true" author="fatma" id="paymentMethodInstatution">
		<insert tableName="PAYMENT_METHOD_INSTITUTION">
			<column name="PAYMENT_METHOD_ID" valueComputed="855"/>
			<column name="INSTITUTION_ID" valueComputed="(SELECT BUSINESS_ENTITY_ID  FROM BUSINESS_ENTITY_HIERARCHY where BUSINESS_ENTITY_NAME ='CIB')"/>
			
		</insert>
	</changeSet>


	<changeSet failOnError="true" author="fatma" id="SOURCE_PAYMENT_METHOD_CIB">
		<insert tableName="SOURCE_PAYMENT_METHOD">
		       <column name="ID" valueComputed="855"/>
			<column name="OVER_DRAFT_LIMIT" valueComputed="null"/>
			<column name="ACCEPT_OVER_DRAFT" value="1"/>
		
			<column name="ALLOW_BALANCE_INQUIRY" value="1"/>
			<column name="ALLOW_DELETE" value="1"/>
			<column name="ALLOW_FREEZE" value="1"/>
			<column name="ALLOW_UPDATE" value="1"/>
			<column name="AUTOMATICALLY_OPENED" value="0"/>
			<column name="HAS_EQUIVALENT_BALANCE" value="null"/>
			<column name="IS_CREDIT" value="1"/>
			<column name="IS_DEBIT" value="1"/>
			<column name="IS_DEBT" value="1"/>
			<column name="OPENED_UPON_CUS_REQ" value="1"/>
			<column name="IS_SINGLE_ACCOUNT" value="null"/>
			<column name="STORAGELOCATION" value="1"/>
			<column name="ALLOW_UNFREEZE" value="1"/>
			<column name="ALLOW_DEFAULT_SENDER" value="1"/>
			
			<column name="ALLOW_DEFAULT_RECEIVER" value="1"/>
		<column name="IS_COMMISSION_ROLL_UP" value="0"/>
		</insert>
	</changeSet>


   <changeSet failOnError="true" author="fatma" id="AllowedPAYMENTMETHODsForCIB">
     <insert tableName="SERVICES_PAYMENTMETHOD">
      <column name="ID" valueComputed="(select max(ID) +1 from SERVICES_PAYMENTMETHOD)"/>
      <column name="API_CODE" value="11171"/>
      <column name="PAYMENMETHODTYPE" value="855"/>
      <column name="LOOKUP_ENUM" value="2"/>
      <column name="GAR_STATUS" value="1"/>
     <column name="USER_TYPE" value="1"/>
	 </insert>
	 
	
    </changeSet>
	
	
	  <changeSet failOnError="true" author="fatma" id="assignforProfilesForCIB">
     <insert tableName="PAYMENT_METHOD_CUST_PROF_CG">
      <column name="ID" valueComputed="(select max(ID) +1 from PAYMENT_METHOD_CUST_PROF_CG)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L0 Basic')"/>
      <column name="PAYMENT_METHOD_ID" value="855"/>
   
	 </insert>
	   <insert tableName="PAYMENT_METHOD_CUST_PROF_CG">
      <column name="ID" valueComputed="(select max(ID) +1 from PAYMENT_METHOD_CUST_PROF_CG)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L2')"/>
      <column name="PAYMENT_METHOD_ID" value="855"/>
   
	 </insert>
	
	
	  <insert tableName="PAYMENT_METHOD_CUST_PROF_CG">
      <column name="ID" valueComputed="(select max(ID) +1 from PAYMENT_METHOD_CUST_PROF_CG)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L1')"/>
      <column name="PAYMENT_METHOD_ID" value="855"/>
   
	 </insert>
    </changeSet>
	
	
	
	
	<changeSet failOnError="true" author="fatma" id="assignforProfilesForCIBin Destination">
     <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L0 Basic')"/>
      <column name="PAYMENT_METHOD_ID" value="855"/>
   
	 </insert>
	   <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L2')"/>
      <column name="PAYMENT_METHOD_ID" value="855"/>
   
	 </insert>
	
	
	  <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L1')"/>
      <column name="PAYMENT_METHOD_ID" value="855"/>
   
	 </insert>
	 
	 <insert tableName="DESTINATION_PAYMENT_METHOD">
		       <column name="ID" valueComputed="855"/>
			<column name="DESTINATION_GROUP_ID" valueComputed="2"/>
			
		</insert>
    </changeSet>
	
	<changeSet author="fatma" id="accountidentifere">
    <update 
            tableName="PAYMENT_METHOD">
        <column name="PAYMENT_METHOD_IDENTIFIER_CODE" value="332" type="NUMBER"/>
        <where>ID = '855'</where>
    </update>
</changeSet>-->
<changeSet failOnError="false" author="fatma" id="CIBSourcenew">
		<insert tableName="PAYMENT_METHOD">
			<column name="ID" valueComputed="871"/>
			<column name="AVAILABILITY" value="1"/>
			<column name="IS_DEFAULT" value="0"/>
			<column name="NAME" value="CIB-Source"/>
			<column name="TYPE" value="0"/>
			<column name="BUSINESS_ENTITY_ID" value="2421"/>
			<column name="CURRENCY_OR_UNIT" value="R"/>
			<column name="GROUP_ID" value="43430"/>
			<column name="PAYMETN_METHOD_ORDER" value="5"/>
			<column name="PAYMENT_METHOD_IDENTIFIER_CODE" value="0"/>
		</insert>
	</changeSet>
	<changeSet failOnError="true" author="fatma" id="paymentMethodInstatutionForCibSource">
		<insert tableName="PAYMENT_METHOD_INSTITUTION">
			<column name="PAYMENT_METHOD_ID" valueComputed="871"/>
			<column name="INSTITUTION_ID" valueComputed="(SELECT BUSINESS_ENTITY_ID  FROM BUSINESS_ENTITY_HIERARCHY where BUSINESS_ENTITY_NAME ='CIB')"/>
			
		</insert>
	</changeSet>


	<changeSet failOnError="true" author="fatma" id="SOURCE_PAYMENT_METHOD_CIB-source">
		<insert tableName="SOURCE_PAYMENT_METHOD">
		       <column name="ID" valueComputed="871"/>
			<column name="OVER_DRAFT_LIMIT" valueComputed="null"/>
			<column name="ACCEPT_OVER_DRAFT" value="1"/>
		
			<column name="ALLOW_BALANCE_INQUIRY" value="1"/>
			<column name="ALLOW_DELETE" value="1"/>
			<column name="ALLOW_FREEZE" value="1"/>
			<column name="ALLOW_UPDATE" value="1"/>
			<column name="AUTOMATICALLY_OPENED" value="0"/>
			<column name="HAS_EQUIVALENT_BALANCE" value="null"/>
			<column name="IS_CREDIT" value="1"/>
			<column name="IS_DEBIT" value="1"/>
			<column name="IS_DEBT" value="1"/>
			<column name="OPENED_UPON_CUS_REQ" value="1"/>
			<column name="IS_SINGLE_ACCOUNT" value="null"/>
			<column name="STORAGELOCATION" value="1"/>
			<column name="ALLOW_UNFREEZE" value="1"/>
			<column name="ALLOW_DEFAULT_SENDER" value="1"/>
			<column name="ALLOW_DEFAULT_RECEIVER" value="1"/>
		   <column name="IS_COMMISSION_ROLL_UP" value="0"/>
		</insert>
	</changeSet>


   <changeSet failOnError="true" author="fatma" id="AllowedPAYMENTMETHODsForCIB-source">
     <insert tableName="SERVICES_PAYMENTMETHOD">
      <column name="ID" valueComputed="(select max(ID) +1 from SERVICES_PAYMENTMETHOD)"/>
      <column name="API_CODE" value="11171"/>
      <column name="PAYMENMETHODTYPE" value="871"/>
      <column name="LOOKUP_ENUM" value="2"/>
      <column name="GAR_STATUS" value="1"/>
     <column name="USER_TYPE" value="1"/>
	 </insert>
	 
	
    </changeSet>
	
	
	  <changeSet failOnError="true" author="fatma" id="assignforProfilesForCIB-source">
     <insert tableName="PAYMENT_METHOD_CUST_PROF_CG">
      <column name="ID" valueComputed="(select max(ID) +1 from PAYMENT_METHOD_CUST_PROF_CG)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L0 Basic')"/>
      <column name="PAYMENT_METHOD_ID" value="871"/>
   
	 </insert>
	   <insert tableName="PAYMENT_METHOD_CUST_PROF_CG">
      <column name="ID" valueComputed="(select max(ID) +1 from PAYMENT_METHOD_CUST_PROF_CG)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L2')"/>
      <column name="PAYMENT_METHOD_ID" value="871"/>
   
	 </insert>
	
	
	  <insert tableName="PAYMENT_METHOD_CUST_PROF_CG">
      <column name="ID" valueComputed="(select max(ID) +1 from PAYMENT_METHOD_CUST_PROF_CG)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L1')"/>
      <column name="PAYMENT_METHOD_ID" value="871"/>
   
	 </insert>
    </changeSet>
	
	
	
	
	<changeSet failOnError="true" author="fatma" id="assignforProfilesForCIBsourcein Destination">
     <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L0 Basic')"/>
      <column name="PAYMENT_METHOD_ID" value="871"/>
   
	 </insert>
	   <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L2')"/>
      <column name="PAYMENT_METHOD_ID" value="871"/>
   
	 </insert>
	
	
	  <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L1')"/>
      <column name="PAYMENT_METHOD_ID" value="871"/>
   
	 </insert>
	 
	 <insert tableName="DESTINATION_PAYMENT_METHOD">
		       <column name="ID" valueComputed="871"/>
			<column name="DESTINATION_GROUP_ID" valueComputed="2"/>
			
		</insert>
    </changeSet>
	
	<changeSet author="fatma" id="accountidentifereforcibsource">
    <update 
            tableName="PAYMENT_METHOD">
        <column name="PAYMENT_METHOD_IDENTIFIER_CODE" value="332" type="NUMBER"/>
        <where>ID = '871'</where>
    </update>
</changeSet>

	<changeSet failOnError="true" author="fatma" id="Debit cardSource">
		<insert tableName="PAYMENT_METHOD">
			<column name="ID" valueComputed="870"/>
			<column name="AVAILABILITY" value="1"/>
			<column name="IS_DEFAULT" value="0"/>
			<column name="NAME" value="DebitCardSource"/>
			<column name="TYPE" value="4"/>
			<column name="BUSINESS_ENTITY_ID" value="2421"/>
			<column name="CURRENCY_OR_UNIT" value="R"/>
			<column name="GROUP_ID" value="43459"/>
			<column name="PAYMETN_METHOD_ORDER" value="6"/>
			<column name="PAYMENT_METHOD_IDENTIFIER_CODE" value="332"/>
		</insert>
	</changeSet>
	<changeSet failOnError="true" author="fatma" id="paymentMethodInstatution FOR DebitCardSource">
		<insert tableName="PAYMENT_METHOD_INSTITUTION">
			<column name="PAYMENT_METHOD_ID" valueComputed="870"/>
			<column name="INSTITUTION_ID" valueComputed="(SELECT BUSINESS_ENTITY_ID  FROM BUSINESS_ENTITY_HIERARCHY where BUSINESS_ENTITY_NAME ='CIB')"/>
			
		</insert>
	</changeSet>


	<changeSet failOnError="true" author="fatma" id="SOURCE_PAYMENT_METHOD_Debit card">
		<insert tableName="SOURCE_PAYMENT_METHOD">
		       <column name="ID" valueComputed="870"/>
			<column name="OVER_DRAFT_LIMIT" valueComputed="null"/>
			<column name="ACCEPT_OVER_DRAFT" value="1"/>
		
			<column name="ALLOW_BALANCE_INQUIRY" value="1"/>
			<column name="ALLOW_DELETE" value="1"/>
			<column name="ALLOW_FREEZE" value="1"/>
			<column name="ALLOW_UPDATE" value="1"/>
			<column name="AUTOMATICALLY_OPENED" value="0"/>
			<column name="HAS_EQUIVALENT_BALANCE" value="null"/>
			<column name="IS_CREDIT" value="1"/>
			<column name="IS_DEBIT" value="1"/>
			<column name="IS_DEBT" value="1"/>
			<column name="OPENED_UPON_CUS_REQ" value="1"/>
			<column name="IS_SINGLE_ACCOUNT" value="null"/>
			<column name="STORAGELOCATION" value="1"/>
			<column name="ALLOW_UNFREEZE" value="1"/>
			<column name="ALLOW_DEFAULT_SENDER" value="1"/>
			
			<column name="ALLOW_DEFAULT_RECEIVER" value="1"/>
		<column name="IS_COMMISSION_ROLL_UP" value="0"/>
		</insert>
	</changeSet>


   <changeSet failOnError="true" author="fatma" id="AllowedPAYMENTMETHODsForDebitcard">
     <insert tableName="SERVICES_PAYMENTMETHOD">
      <column name="ID" valueComputed="(select max(ID) +1 from SERVICES_PAYMENTMETHOD)"/>
      <column name="API_CODE" value="11171"/>
      <column name="PAYMENMETHODTYPE" value="870"/>
      <column name="LOOKUP_ENUM" value="2"/>
      <column name="GAR_STATUS" value="1"/>
     <column name="USER_TYPE" value="1"/>
	 </insert>
	 
	
    </changeSet>
	
	
	  <changeSet failOnError="true" author="fatma" id="assignforProfilesForDebitcard">
     <insert tableName="PAYMENT_METHOD_CUST_PROF_CG">
      <column name="ID" valueComputed="(select max(ID) +1 from PAYMENT_METHOD_CUST_PROF_CG)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L0 Basic')"/>
      <column name="PAYMENT_METHOD_ID" value="870"/>
   
	 </insert>
	   <insert tableName="PAYMENT_METHOD_CUST_PROF_CG">
      <column name="ID" valueComputed="(select max(ID) +1 from PAYMENT_METHOD_CUST_PROF_CG)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L2')"/>
      <column name="PAYMENT_METHOD_ID" value="870"/>
   
	 </insert>
	
	
	  <insert tableName="PAYMENT_METHOD_CUST_PROF_CG">
      <column name="ID" valueComputed="(select max(ID) +1 from PAYMENT_METHOD_CUST_PROF_CG)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L1')"/>
      <column name="PAYMENT_METHOD_ID" value="870"/>
   
	 </insert>
    </changeSet>
	
	
	
	
	<changeSet failOnError="true" author="fatma" id="assignforProfilesForDebitcardin Destination">
     <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L0 Basic')"/>
      <column name="PAYMENT_METHOD_ID" value="870"/>
   
	 </insert>
	   <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L2')"/>
      <column name="PAYMENT_METHOD_ID" value="870"/>
   
	 </insert>
	
	
	  <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L1')"/>
      <column name="PAYMENT_METHOD_ID" value="870"/>
   
	 </insert>
	 
	 <insert tableName="DESTINATION_PAYMENT_METHOD">
		       <column name="ID" valueComputed="870"/>
			<column name="DESTINATION_GROUP_ID" valueComputed="2"/>
			
		</insert>
    </changeSet>
	
	
	
	
	<changeSet failOnError="true" author="fatma" id="CIBDEST">
		<insert tableName="PAYMENT_METHOD">
			<column name="ID" valueComputed="856"/>
			<column name="AVAILABILITY" value="1"/>
			<column name="IS_DEFAULT" value="0"/>
			<column name="NAME" value="CIBDEST"/>
			<column name="TYPE" value="0"/>
			<column name="BUSINESS_ENTITY_ID" value="2421"/>
			<column name="CURRENCY_OR_UNIT" value="R"/>
			<column name="GROUP_ID" value="43503"/>
			<column name="PAYMETN_METHOD_ORDER" value="5"/>
			<column name="PAYMENT_METHOD_IDENTIFIER_CODE" value="332"/>
		</insert>
	</changeSet>
	<changeSet failOnError="true" author="fatma" id="paymentMethodInstatutionFORCIBDEST">
		<insert tableName="PAYMENT_METHOD_INSTITUTION">
			<column name="PAYMENT_METHOD_ID" valueComputed="856"/>
			<column name="INSTITUTION_ID" valueComputed="(SELECT BUSINESS_ENTITY_ID  FROM BUSINESS_ENTITY_HIERARCHY where BUSINESS_ENTITY_NAME ='CIB')"/>
			
		</insert>
	</changeSet>




   <changeSet failOnError="true" author="fatma" id="AllowedPAYMENTMETHODsForCIBDEST">
     <insert tableName="SERVICES_PAYMENTMETHOD">
      <column name="ID" valueComputed="(select max(ID) +1 from SERVICES_PAYMENTMETHOD)"/>
      <column name="API_CODE" value="********"/>
      <column name="PAYMENMETHODTYPE" value="856"/>
      <column name="LOOKUP_ENUM" value="3"/>
      <column name="GAR_STATUS" value="1"/>
     <column name="USER_TYPE" value="1"/>
	 </insert>
	 
	
    </changeSet>
	
	
	  
	
	
	
	
	<changeSet failOnError="true" author="fatma" id="assignforProfilesForCIBDEST in Destination">
     <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L0 Basic')"/>
      <column name="PAYMENT_METHOD_ID" value="856"/>
   
	 </insert>
	   <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L2')"/>
      <column name="PAYMENT_METHOD_ID" value="856"/>
   
	 </insert>
	
	
	  <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L1')"/>
      <column name="PAYMENT_METHOD_ID" value="856"/>
   
	 </insert>
	 
	 <insert tableName="DESTINATION_PAYMENT_METHOD">
		       <column name="ID" valueComputed="856"/>
			<column name="DESTINATION_GROUP_ID" valueComputed="2"/>
			
		</insert>
    </changeSet>
	
	
	
	
	<changeSet failOnError="true" author="fatma" id="QNB_dest">
		<insert tableName="PAYMENT_METHOD">
			<column name="ID" valueComputed="858"/>
			<column name="AVAILABILITY" value="1"/>
			<column name="IS_DEFAULT" value="0"/>
			<column name="NAME" value="QNB_dest"/>
			<column name="TYPE" value="0"/>
			<column name="BUSINESS_ENTITY_ID" value="2421"/>
			<column name="CURRENCY_OR_UNIT" value="R"/>
			<column name="GROUP_ID" value="43537"/>
			<column name="PAYMETN_METHOD_ORDER" value="6"/>
			<column name="PAYMENT_METHOD_IDENTIFIER_CODE" value="332"/>
		</insert>
	</changeSet>
	<changeSet failOnError="true" author="fatma" id="paymentMethodInstatutionFORQNB_dest">
		<insert tableName="PAYMENT_METHOD_INSTITUTION">
			<column name="PAYMENT_METHOD_ID" valueComputed="858"/>
			<column name="INSTITUTION_ID" valueComputed="(SELECT BUSINESS_ENTITY_ID  FROM BUSINESS_ENTITY_HIERARCHY where BUSINESS_ENTITY_NAME ='Bank Department' and PARENT_BUSINESS_ENTITY='2421')"/>
			
		</insert>
	</changeSet>




   <changeSet failOnError="true" author="fatma" id="AllowedPAYMENTMETHODsForQNB_dest">
     <insert tableName="SERVICES_PAYMENTMETHOD">
      <column name="ID" valueComputed="(select max(ID) +1 from SERVICES_PAYMENTMETHOD)"/>
      <column name="API_CODE" value="********"/>
      <column name="PAYMENMETHODTYPE" value="858"/>
      <column name="LOOKUP_ENUM" value="3"/>
      <column name="GAR_STATUS" value="1"/>
     <column name="USER_TYPE" value="1"/>
	 </insert>
	 
	
    </changeSet>
	
	
	  
	
	
	
	
	<changeSet failOnError="true" author="fatma" id="assignforProfilesForQNB_dest in Destination">
     <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L0 Basic')"/>
      <column name="PAYMENT_METHOD_ID" value="858"/>
   
	 </insert>
	   <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L2')"/>
      <column name="PAYMENT_METHOD_ID" value="858"/>
   
	 </insert>
	
	
	  <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L1')"/>
      <column name="PAYMENT_METHOD_ID" value="858"/>
   
	 </insert>
	 
	 <insert tableName="DESTINATION_PAYMENT_METHOD">
		       <column name="ID" valueComputed="858"/>
			<column name="DESTINATION_GROUP_ID" valueComputed="2"/>
			
		</insert>
    </changeSet>
	
	
	
	<changeSet failOnError="true" author="fatma" id="HSBC_dest">
		<insert tableName="PAYMENT_METHOD">
			<column name="ID" valueComputed="857"/>
			<column name="AVAILABILITY" value="1"/>
			<column name="IS_DEFAULT" value="0"/>
			<column name="NAME" value="HSBC_dest"/>
			<column name="TYPE" value="0"/>
			<column name="BUSINESS_ENTITY_ID" value="2421"/>
			<column name="CURRENCY_OR_UNIT" value="R"/>
			<column name="GROUP_ID" value="43555"/>
			<column name="PAYMETN_METHOD_ORDER" value="7"/>
			<column name="PAYMENT_METHOD_IDENTIFIER_CODE" value="332"/>
		</insert>
	</changeSet>
	<changeSet failOnError="true" author="fatma" id="paymentMethodInstatutionFOR HSBC_dest">
		<insert tableName="PAYMENT_METHOD_INSTITUTION">
			<column name="PAYMENT_METHOD_ID" valueComputed="857"/>
			<column name="INSTITUTION_ID" valueComputed="(SELECT BUSINESS_ENTITY_ID  FROM BUSINESS_ENTITY_HIERARCHY where BUSINESS_ENTITY_NAME ='Bank Department' and PARENT_BUSINESS_ENTITY='2421')"/>
			
		</insert>
	</changeSet>




   <changeSet failOnError="true" author="fatma" id="AllowedPAYMENTMETHODsForHSBC_dest">
     <insert tableName="SERVICES_PAYMENTMETHOD">
      <column name="ID" valueComputed="(select max(ID) +1 from SERVICES_PAYMENTMETHOD)"/>
      <column name="API_CODE" value="********"/>
      <column name="PAYMENMETHODTYPE" value="857"/>
      <column name="LOOKUP_ENUM" value="3"/>
      <column name="GAR_STATUS" value="1"/>
     <column name="USER_TYPE" value="1"/>
	 </insert>
	 
	
    </changeSet>
	
	
	  
	
	
	
	
	<changeSet failOnError="true" author="fatma" id="assignforProfilesForHSBC_dest in Destination">
     <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L0 Basic')"/>
      <column name="PAYMENT_METHOD_ID" value="857"/>
   
	 </insert>
	   <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L2')"/>
      <column name="PAYMENT_METHOD_ID" value="857"/>
   
	 </insert>
	
	
	  <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L1')"/>
      <column name="PAYMENT_METHOD_ID" value="857"/>
   
	 </insert>
	 
	 <insert tableName="DESTINATION_PAYMENT_METHOD">
		       <column name="ID" valueComputed="857"/>
			<column name="DESTINATION_GROUP_ID" valueComputed="2"/>
			
		</insert>
    </changeSet>
	
	
	

	
	
	
	<changeSet failOnError="true" author="fatma" id="NBK_dest">
		<insert tableName="PAYMENT_METHOD">
			<column name="ID" valueComputed="859"/>
			<column name="AVAILABILITY" value="1"/>
			<column name="IS_DEFAULT" value="0"/>
			<column name="NAME" value="HSBC_dest"/>
			<column name="TYPE" value="0"/>
			<column name="BUSINESS_ENTITY_ID" value="2421"/>
			<column name="CURRENCY_OR_UNIT" value="R"/>
			<column name="GROUP_ID" value="43537"/>
			<column name="PAYMETN_METHOD_ORDER" value="8"/>
			<column name="PAYMENT_METHOD_IDENTIFIER_CODE" value="332"/>
		</insert>
	</changeSet>
	<changeSet failOnError="true" author="fatma" id="paymentMethodInstatutionFOR NBK_dest">
		<insert tableName="PAYMENT_METHOD_INSTITUTION">
			<column name="PAYMENT_METHOD_ID" valueComputed="859"/>
			<column name="INSTITUTION_ID" valueComputed="(SELECT BUSINESS_ENTITY_ID  FROM BUSINESS_ENTITY_HIERARCHY where BUSINESS_ENTITY_NAME ='Bank Department' and PARENT_BUSINESS_ENTITY='2421')"/>
			
		</insert>
	</changeSet>




   <changeSet failOnError="true" author="fatma" id="AllowedPAYMENTMETHODsForNBK_dest">
     <insert tableName="SERVICES_PAYMENTMETHOD">
      <column name="ID" valueComputed="(select max(ID) +1 from SERVICES_PAYMENTMETHOD)"/>
      <column name="API_CODE" value="********"/>
      <column name="PAYMENMETHODTYPE" value="859"/>
      <column name="LOOKUP_ENUM" value="3"/>
      <column name="GAR_STATUS" value="1"/>
     <column name="USER_TYPE" value="1"/>
	 </insert>
	 
	
    </changeSet>
	
	
	  
	
	
	
	
	<changeSet failOnError="true" author="fatma" id="assignforProfilesForNBK_dest in Destination">
     <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L0 Basic')"/>
      <column name="PAYMENT_METHOD_ID" value="859"/>
   
	 </insert>
	   <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L2')"/>
      <column name="PAYMENT_METHOD_ID" value="859"/>
   
	 </insert>
	
	
	  <insert tableName="DEST_PAYMENT_METHOD_CUST_PROF">
      <column name="ID" valueComputed="(select max(ID) +1 from DEST_PAYMENT_METHOD_CUST_PROF)"/>
      <column name="CUSTOMER_TYPE_ID" valueComputed="(select ID  from CUSTOMERTYPE where NAME ='KYC L1')"/>
      <column name="PAYMENT_METHOD_ID" value="859"/>
   
	 </insert>
	 
	 <insert tableName="DESTINATION_PAYMENT_METHOD">
		       <column name="ID" valueComputed="859"/>
			<column name="DESTINATION_GROUP_ID" valueComputed="2"/>
			
		</insert>
    </changeSet>
	
	
</databaseChangeLog>
	
