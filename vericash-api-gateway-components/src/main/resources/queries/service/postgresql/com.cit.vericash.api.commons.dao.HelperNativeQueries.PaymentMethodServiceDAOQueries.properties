findPaymentMethodTypeById=SELECT PM.TYPE From PAYMENT_METHOD PM where PM.id = %1$s
isPaymentMethodInputParameters=SELECT IPD.GROUP_CODE FROM PAYMENT_METHOD PM JOIN INPUT_PARAM_GROUP_WALLET IW \
  ON IW.ID = PM.GROUP_ID JOIN INPUT_PARAM_GROUP_DEF IPD ON IPD.ID = IW.GROUP_DEF_ID \
  WHERE PM.ID = %1$s
getGarIdByMsisdnOrAlias=SELECT cg.GARID  FROM CUSTOMER_GAR cg LEFT JOIN CUSTOMER c \
  ON cg.CUSTOMER_ID = c.CUSTOMER_ID LEFT JOIN PAYMENT_METHOD pm ON cg.PAYMENT_METHOD_TYPE = pm.ID \
  WHERE pm.\"TYPE\" = %1$s and (c.MSISDN = '%2$s' OR cg.PAYMENT_ALIAS = '%3$s')
findPaymentMethodByInputParameters=SELECT DISTINCT CUSTOMER_GAR_PARAMETERS.GARID  \
  FROM CUSTOMER_GAR_PARAMETERS LEFT JOIN CUSTOMER_GAR ON (CUSTOMER_GAR_PARAMETERS.GARID = CUSTOMER_GAR.GARID) \
  WHERE PARAMETER_CODE IN (%1$s)  AND PARAMETER_VALUE IN (%2$s) AND CUSTOMER_GAR.STATUS !=3
getGroupIdByPaymentMethodType=SELECT IPGW.ID FROM PAYMENT_METHOD PM JOIN INPUT_PARAM_GROUP_WALLET IPGW \
  ON IPGW.ID = PM.GROUP_ID JOIN INPUT_PARAM_GROUP_DEF IPGD ON IPGW.GROUP_DEF_ID = IPGD.ID \
  WHERE PM.ID = %1$s
getPaymentMethodType=SELECT TYPE FROM PAYMENT_METHOD WHERE ID = %1$s
getGroupNameByPaymentMethodType=SELECT IPGD.NAME FROM PAYMENT_METHOD PM \
  JOIN INPUT_PARAM_GROUP_WALLET IPGW ON IPGW.ID = PM.GROUP_ID \
  JOIN INPUT_PARAM_GROUP_DEF IPGD ON IPGW.GROUP_DEF_ID = IPGD.ID \
  WHERE PM.ID = %1$s
getPaymentMethodTypeAndIdentifier=SELECT pm.\"TYPE\", pm.PAYMENT_METHOD_IDENTIFIER_CODE  \
  FROM PAYMENT_METHOD pm WHERE pm.ID = %1$s
getPaymentMethodCurrency=SELECT PM.CURRENCY_OR_UNIT FROM PAYMENT_METHOD PM WHERE PM.ID = %1$s
getPaymentAliasByAccountId=SELECT cg.PAYMENT_ALIAS FROM CUSTOMER_GAR cg WHERE cg.ACCOUNT_ID = %1$s
getPaymentAliasByGarId=SELECT cg.PAYMENT_ALIAS FROM CUSTOMER_GAR cg WHERE cg.GARID = %1$s
getMSISDNByAccountId=SELECT c.MSISDN FROM CUSTOMER_GAR cg INNER JOIN CUSTOMER c ON cg.CUSTOMER_ID = c.CUSTOMER_ID WHERE cg.ACCOUNT_ID = %1$s
getMSISDNByGarId=SELECT c.MSISDN FROM CUSTOMER_GAR cg INNER JOIN CUSTOMER c ON cg.CUSTOMER_ID = c.CUSTOMER_ID WHERE cg.GARID = %1$s
getServiceCodeWithPaymentMethodAndApiCode=SELECT SR.id ,SR.INTERNAL_SERVICE_CODE ,SR.SERVICE_NAME \
  FROM SERVICE_CODE_ROUTER SR INNER JOIN PAYMENT_METHOD_ROUTER PR ON SR.PAYMENT_METHOD_ID = PR.ID \
  INNER JOIN SENDER_TYPE_ROUTER STR ON STR. ID = SR.SENDER_TYPE_ID \
  WHERE PR.SOURCE_PAYMENTMETHOD_ID = %1$s AND PR.DESTINATION_PAYMENTMETHOD_ID = %2$s \
  AND STR.SENDER_TYPE = %3$s and STR.API_CODE = '%4$s'
getTransactionAPICodeRepository=SELECT TPR.INTERNAL_SERVICE_CODE,TPR.IS_CUSTOM,CPR.CUSTOM_API_CODE \
  FROM TRANSACTION_PM_ROUTING TPR \
  LEFT JOIN CUSTOM_PM_ROUTER CPR ON TPR.CUSTOM_ROUTER_ID = CPR.ID \
  LEFT JOIN TRANSFER_TYPE TT ON TPR.TRANSACTION_TYPE=TT.ID \
  INNER JOIN SENDER_TYPE_ROUTER STR ON STR. ID = TPR.SENDER_TYPE_ID \
  LEFT JOIN TRANSACTION_OPTIONS TRO ON TPR.TRANSACTION_OPTION = TRO.ID \
  WHERE (TPR.CUSTOM_ROUTER_ID IS NULL AND IS_CUSTOM=0 AND TT.TRANSFER_TYPE_DESCRIPTION = '%5$s' AND \
  TRO.SOURCE_TYPE= %3$s AND TRO.DESTINATION_TYPE= %4$s AND STR.SENDER_TYPE= %6$s AND STR.API_CODE= '%7$s') OR \
  (CPR.SOURCE_PM_ID = %1$s AND CPR.DESTINATION_PM_ID= %2$s AND IS_CUSTOM=1 \
  AND STR.SENDER_TYPE= %6$s AND STR.API_CODE = '%7$s' )
getDefaultTransactionServiceCode=SELECT  BPSR.INTERNAL_SERVICE_CODE FROM PAYMENT_METHODS_CONFIG_ROUTER PMCR \
  LEFT JOIN BACKEND_PM_SERVICE_ROUTER BPSR \
  ON PMCR.BACKEND_PM_SERVICE_ROUTER_ID=BPSR.ID \
  LEFT JOIN PAYMENT_METHOD_ROUTER PMR \
  ON PMCR.PAYMENT_METHOD_COLLECTION_ID =PMR.ID \
  LEFT JOIN TRANSACTION_OPTIONS TRO ON BPSR.TRANSACTION_OPTION =TRO.ID \
  LEFT JOIN TRANSFER_TYPE TT ON TT.ID = BPSR.TRANSFER_TYPE \
  WHERE PMCR.API_CODE= '%5$s' \
  AND TRO.SOURCE_TYPE= %1$s \
  AND TRO.DESTINATION_TYPE = %2$s \
  AND BPSR.SENDER_TYPE=%4$s \
  AND TT.ID =%3$s \
  AND PMCR.PAYMENT_METHOD_COLLECTION_ID IS NULL
getCustomTransactionServiceCode=SELECT  BPSR.INTERNAL_SERVICE_CODE \
  FROM PAYMENT_METHODS_CONFIG_ROUTER PMCR \
  LEFT JOIN BACKEND_PM_SERVICE_ROUTER BPSR \
  ON PMCR.BACKEND_PM_SERVICE_ROUTER_ID=BPSR.ID \
  LEFT JOIN PAYMENT_METHOD_ROUTER PMR \
  ON PMCR.PAYMENT_METHOD_COLLECTION_ID =PMR.ID \
  LEFT JOIN TRANSACTION_OPTIONS TRO ON BPSR.TRANSACTION_OPTION =TRO.ID \
  LEFT JOIN TRANSFER_TYPE TT ON TT.ID = BPSR.TRANSFER_TYPE \
  WHERE PMCR.API_CODE= %7$s \
  AND TRO.SOURCE_TYPE= %3$s \
  AND TRO.DESTINATION_TYPE = %4$s \
  AND BPSR.SENDER_TYPE=%6$s \
  AND TT.ID =%5$s \
  AND PMCR.PAYMENT_METHOD_COLLECTION_ID IS NOT NULL \
  AND PMR.DESTINATION_PAYMENTMETHOD_ID =%2$s \
  AND PMR.SOURCE_PAYMENTMETHOD_ID =%1$s
