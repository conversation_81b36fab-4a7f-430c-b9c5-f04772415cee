<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">


    <changeSet id="maxNoFavorites" author="Amir Nabil">

        <preConditions onFail="MARK_RAN">
            <!-- Check if FCY_BOARD_MAX_NO_FAVORITE doesn't exist -->
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM GENERAL_LOOKUPS WHERE LOOKUP_KEY = 'FCY_BOARD_MAX_NO_FAVORITE';
            </sqlCheck>
        </preConditions>

        <!-- Insert statement for FCY_BOARD_MAX_NO_FAVORITE -->
        <sql>
            INSERT INTO GENERAL_LOOKUPS
                (LO<PERSON><PERSON>_KEY, LOOKUP_VALUE)
            VALUES ('FCY_BOARD_MAX_NO_FAVORITE', '5');

            INSERT INTO PRO_MI_NO_C.PORTAL_LOOKUPS_CONFIG
                (ID, LOOKUP_NAME, LOOKUP_KEY, LOOKUP_QUERY, CACHEABLE)
            VALUES
                (
                 null,
                 'FCY Max Allowed Number Of Favorites',
                 'get.fcy.max.allowed.customer.favorites',
                 'SELECT LOOKUP_KEY,LOOKUP_VALUE FROM ( SELECT LOOKUP_KEY AS LOOKUP_KEY,CAST(LOOKUP_VALUE AS VARCHAR2(100)) AS LOOKUP_VALUE FROM GENERAL_LOOKUPS WHERE LOOKUP_KEY = ''FCY_BOARD_MAX_NO_FAVORITE'' )',
                 0
                );
        </sql>
    </changeSet>

    <changeSet id="allowChangeBaseCurrencyLookup" author="Amir Nabil">

        <preConditions onFail="MARK_RAN">
            <!-- Check if FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY doesn't exist -->
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM GENERAL_LOOKUPS WHERE LOOKUP_KEY = 'FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY';
            </sqlCheck>
        </preConditions>

        <!-- Insert statement for FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY -->
        <sql>
            INSERT INTO GENERAL_LOOKUPS
                (LOOKUP_KEY, LOOKUP_VALUE)
            VALUES
                ('FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY', 'true');

            INSERT INTO PRO_MI_NO_C.PORTAL_LOOKUPS_CONFIG
                (ID, LOOKUP_NAME, LOOKUP_KEY, LOOKUP_QUERY, CACHEABLE)
            VALUES
                (
                    null,
                    'FCY Allow Change Base Currency',
                    'get.allow.change.base.currency',
                    'SELECT LOOKUP_KEY, LOOKUP_VALUE FROM ( SELECT LOOKUP_KEY AS LOOKUP_KEY, CAST(LOOKUP_VALUE AS VARCHAR2(100)) AS LOOKUP_VALUE FROM GENERAL_LOOKUPS WHERE LOOKUP_KEY = ''FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY'')',
                    0
                );

        </sql>
    </changeSet>

    <changeSet id="defaultBaseCurrency" author="Amir Nabil">

        <preConditions onFail="MARK_RAN">
            <!-- Check if FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY doesn't exist -->
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM GENERAL_LOOKUPS WHERE LOOKUP_KEY = 'FCY_BOARD_DEFAULT_BASE_CURRENCY';
            </sqlCheck>
        </preConditions>

        <!-- Insert statement for FCY_BOARD_ALLOW_CHANGE_BASE_CURRENCY -->
        <sql>
            INSERT INTO GENERAL_LOOKUPS
                (LOOKUP_KEY, LOOKUP_VALUE)
            VALUES ('FCY_BOARD_DEFAULT_BASE_CURRENCY', '104');


            INSERT INTO GENERAL_LOOKUPS
                (LOOKUP_KEY, LOOKUP_VALUE)
            VALUES ('FCY_BOARD_DEFAULT_BASE_CURRENCY_ICON', 'http://192.168.4.35/NGN.png');

            INSERT INTO GENERAL_LOOKUPS
                (LOOKUP_KEY, LOOKUP_VALUE)
            VALUES ('FCY_BOARD_DEFAULT_BASE_CURRENCY_CODE', 'NGN');


            INSERT INTO PRO_MI_NO_C.PORTAL_LOOKUPS_CONFIG
                (ID, LOOKUP_NAME, LOOKUP_KEY, LOOKUP_QUERY, CACHEABLE)
            VALUES (
                    null,
                    'FCY Default Base Currency',
                    'get.fcy.default.base.currency',
                    'SELECT LOOKUP_KEY,LOOKUP_VALUE FROM ( SELECT LOOKUP_KEY AS LOOKUP_KEY,CAST(LOOKUP_VALUE AS VARCHAR2(100)) AS LOOKUP_VALUE FROM GENERAL_LOOKUPS WHERE LOOKUP_KEY = ''FCY_BOARD_DEFAULT_BASE_CURRENCY_CODE'' OR LOOKUP_KEY = ''FCY_BOARD_DEFAULT_BASE_CURRENCY_ICON'' OR LOOKUP_KEY = ''FCY_BOARD_DEFAULT_BASE_CURRENCY'')',
                    0
                   );

        </sql>
    </changeSet>
</databaseChangeLog>