package com.cit.vericash.apis.selfregistration;

import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.CacheableWalletInfo;
import com.cit.vericash.apis.commons.util.MsisdnFormatterImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.selfregistration.QRSendRedemptionCodeForRegisteredUserRequestTransformer")
public class QRSendRedemptionCodeForRegisteredUserRequestTransformer extends MessageToBusinessMessageTransformerImpl {


    private final CacheableWalletInfo walletInfoCache;

    private final CacheableFieldParameters cacheableFieldParameters ;

    private final MsisdnFormatterImpl msisdnFormatter;

    private final String WALLET_SHORT_CODE="wallet-Short-Code";
    private final String MSISDN="msisdn";

    public QRSendRedemptionCodeForRegisteredUserRequestTransformer(CacheableWalletInfo walletInfoCache,
                                                                   CacheableFieldParameters cacheableFieldParameters,
                                                                   MsisdnFormatterImpl msisdnFormatter) {
        this.walletInfoCache = walletInfoCache;
        this.cacheableFieldParameters = cacheableFieldParameters;
        this.msisdnFormatter = msisdnFormatter;
    }

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        String walletShortCodeKey = cacheableFieldParameters.getRequestParameters(WALLET_SHORT_CODE);
        String walletShortCode = message.getHeader().getAttributeAsString(walletShortCodeKey);
        WalletInfo walletInfo = walletInfoCache.getWalletInfo(walletShortCode);

        try {
            String msisdnKey = cacheableFieldParameters.getRequestParameters(MSISDN);

            String msisdn = message.getPayload().getAttributeAsString(msisdnKey);
            String region =walletInfo.getCountryIso2();

            msisdnFormatter.formatMSISDN(msisdn, region,walletShortCode);

        }catch(Exception e){
            throw e;
        }
    }
}
