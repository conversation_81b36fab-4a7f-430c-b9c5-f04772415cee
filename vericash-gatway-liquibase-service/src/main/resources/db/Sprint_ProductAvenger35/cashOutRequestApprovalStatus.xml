<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="insert_receiver_request_approval_status" author="<PERSON>">
        <insert tableName="RECEIVER_REQUEST_APPROVAL_STATUS">
            <column name="ID" value="1" />
            <column name="STATUS_NAME" value="Initiated" />
        </insert>
        <insert tableName="RECEIVER_REQUEST_APPROVAL_STATUS">
            <column name="ID" value="2" />
            <column name="STATUS_NAME" value="Approved" />
        </insert>
        <insert tableName="RECEIVER_REQUEST_APPROVAL_STATUS">
            <column name="ID" value="3" />
            <column name="STATUS_NAME" value="Rejected" />
        </insert>
        <insert tableName="RECEIVER_REQUEST_APPROVAL_STATUS">
            <column name="ID" value="4" />
            <column name="STATUS_NAME" value="Expired" />
        </insert>
        <insert tableName="RECEIVER_REQUEST_APPROVAL_STATUS">
            <column name="ID" value="5" />
            <column name="STATUS_NAME" value="Completed" />
        </insert>
    </changeSet>

</databaseChangeLog>
