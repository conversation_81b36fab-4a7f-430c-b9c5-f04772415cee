package com.cit.vericash.apis.getcustomerdetails;

import com.cit.mpaymentapp.common.message.PartyDetails;
import com.cit.mpaymentapp.model.Enums;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.getcustomerdetails.GetCustomerDetailsRequestTransformer")
public class GetCustomerDetailsRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNER_TYPE = "sender-OwnerType";
    private final String WALLET_SHORT_CODE = "wallet-Short-Code";
    private final String CUSTOMER_MOBILE_NUMBER = "customerMobileNumber";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
//        cacheableFieldParameters = CacheableFieldParametersImpl.getInstance();
        String ownerTypeKey = cacheableFieldParameters.getRequestParameters(OWNER_TYPE);
        String ownerTypeString = message.getPayload().getAttributeAsString(ownerTypeKey);
        String walletShortCodeKey = cacheableFieldParameters.getRequestParameters(WALLET_SHORT_CODE);
        String walletShortCode = message.getHeader().getAttributeAsString(walletShortCodeKey);
        String customerMobileNumberKey = cacheableFieldParameters.getRequestParameters(CUSTOMER_MOBILE_NUMBER);
        String customerMobileNumber = message.getPayload().getAttributeAsString(customerMobileNumberKey);
        try {


            if (message.getPayload().get(ownerTypeKey) == Enums.OwnerTypeEnum.BUSINESSENTITY) {
                List<PartyDetails> otherSenders = new LinkedList<PartyDetails>();
                PartyDetails partyDetails = new PartyDetails();
                partyDetails.setMsisdn(customerMobileNumber);
                partyDetails.setWalletShortCode(walletShortCode);
                otherSenders.add(partyDetails);
                message.getPayload().put("otherSender", otherSenders);
            }
        } catch (Exception e) {
            throw e;
        }
    }
}
