package com.cit.vericash.apis.customerReferral;

import com.cit.vericash.api.components.JsonValidationStrategy;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.ApiValidationException;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.mastercard.api.core.exception.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.customerReferral.InviteUserRequestTransformer")
public class InviteUserRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    @Autowired
    JsonValidationStrategy jsonValidationStrategy;
    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        Request request = new Request();
        request.setMessage(message);
        request.setSignature("123123123");
        jsonValidationStrategy.validateJsonStrategy(request);

        return;
    }
}
