package com.cit.vericash.apis.gettopupproducts;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.gettopupproducts.GetTopUpProductsRequestTransformer")
public class GetTopUpProductsRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNER_TYPE = "sender-OwnerType";
    private final String BILLER_ID = "biller-Id";
    private final String ATTRIBUTES = "attributes";
    private final String SPECIFIC_BILLER_ID = "specific_biller-Id";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
//        cacheableFieldParameters = CacheableFieldParametersImpl.getInstance();
        String ownerTypeKey = cacheableFieldParameters.getRequestParameters(OWNER_TYPE);
        String billerIdKey = cacheableFieldParameters.getRequestParameters(BILLER_ID);
        String attributesKey = cacheableFieldParameters.getRequestParameters(ATTRIBUTES);
        String specificbillerIdKey = cacheableFieldParameters.getRequestParameters(SPECIFIC_BILLER_ID);
        Map<String, String> billAttribute = message.getPayload().containsKey(attributesKey) ? (Map<String, String>) message.getPayload().get(attributesKey) : null;
        String ownerTypeString = message.getPayload().getAttributeAsString(ownerTypeKey);
        String billerId = message.getPayload().getAttributeAsString(billerIdKey);
        //  try {

        if (!StringUtils.isBlank(billerId)) {
            message.getPayload().put("billerIdKey", billerId);
        }
        if (!StringUtils.isBlank(billerId)) {
            message.getPayload().put("specificBillerId", specificbillerIdKey);
        }
        message.getPayload().put("billAttribute", billAttribute);

//            if (!StringUtils.isBlank(ownerTypeString)) {
//                Enums.OwnerTypeEnum ownerType;
//
//                switch (Integer.valueOf(ownerTypeString)) {
//                    case 1: {
//                        ownerType = Enums.OwnerTypeEnum.CUSTOMER;
//                        break;
//                    }
//                    case 2:
//                    case 3:
//                    case 4:
//                    case 5:
//                    case 6: {
//                        ownerType = Enums.OwnerTypeEnum.BUSINESSENTITY;
//                        break;
//                    }
//                    default: {
//                        ownerType = Enums.OwnerTypeEnum.None;
//                        break;
//                    }
//                }

        //               message.getPayload().put(ownerTypeKey, ownerType);
        //          }


//        } catch (Exception e) {
//           throw e;
//       }
    }
}
