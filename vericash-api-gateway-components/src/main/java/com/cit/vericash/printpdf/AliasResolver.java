package com.cit.vericash.printpdf;

import com.cit.mpaymentapp.model.Enums;
import com.cit.mpaymentapp.model.transaction.TransactionStatus;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.receipt.PaymentMethodAccountIdentifier;
import com.cit.vericash.util.TransactionUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@RequiredArgsConstructor
@Slf4j
public class AliasResolver {
    public static final ObjectMapper objectMapper = new ObjectMapper();

    public static final String SERVICE_NAME = "SERVICE_NAME";
    public static final String TRANSACTION_STATUS = "TRANSACTION_STATUS";
    public static final String SOURCE_PM = "SOURCE_PM";
    public static final String DESTINATION_PM = "DESTINATION_PM";
    public static final String SOURCE_PAYMENTMETHOD_ID = "SOURCE_PAYMENTMETHOD_ID";
    public static final String SENDER_MSISDN = "SENDER_MSISDN";
    public static final String SENDER_ACCOUNT = "SENDER_ACCOUNT";
    public static final String DESTINATION_PAYMENTMETHOD_ID = "DESTINATION_PAYMENTMETHOD_ID";
    public static final String RECEIVER_MSISDN = "RECEIVER_MSISDN";
    public static final String RECEIEVER_ACCOUNT = "RECEIEVER_ACCOUNT";
    public static final String PAYMENT_METHOD_TYPE = "PAYMENT_METHOD_TYPE";
    public static final String PARAMETER_CODE = "PARAMETER_CODE";
    public static final String PARAMETER_VALUE = "PARAMETER_VALUE";
    public static final String MSISDN = "MSISDN";
    public static final String FEE_AMOUNT = "FEE_AMOUNT";
    public static final String TRANSACTION_AMOUNT = "TRANSACTION_AMOUNT";
    public static final String TOTAL_AMOUNT = "TOTAL_AMOUNT";
    public static final String TRANSACTION_TYPE = "TRANSACTION_TYPE";
    public static final String CURRENCY = "CURRENCY";
    public static final String RECEIVER_USER_MSISDN = "RECEIVER_USER_MSISDN";
    private final PaymentMethodAccountIdentifier paymentMethodAccountIdentifier;
    private final ConnectionUtil connectionUtil;

    private static final Map<String, QueryKeyResolver> aliasResolvers = new HashMap<>();

    @PostConstruct
    public void init() {
        // add here any custom implementations of QueryKeyResolver
        aliasResolvers.put(SERVICE_NAME, (record, queryValue, serviceName, msisdn) -> queryValue == null ? serviceName : queryValue.toString());
        aliasResolvers.put(RECEIVER_USER_MSISDN, (record, queryValue, serviceName, msisdn) -> handleSplitReceiver(record, queryValue));
        aliasResolvers.put(FEE_AMOUNT, handleAmountKey());
        aliasResolvers.put(TRANSACTION_AMOUNT, handleDefaultAmountKey());
        aliasResolvers.put(TOTAL_AMOUNT, handleAmountKey());
        aliasResolvers.put(TRANSACTION_TYPE, (record, queryValue, serviceName, msisdn) -> handleTransactionType(record, msisdn));
        aliasResolvers.put(DESTINATION_PM, (record, queryValue, serviceName, msisdn) -> handlePMKey(record, DESTINATION_PAYMENTMETHOD_ID, DESTINATION_PM, RECEIVER_MSISDN, RECEIEVER_ACCOUNT));
        aliasResolvers.put(SOURCE_PM, (record, queryValue, serviceName, msisdn) -> handlePMKey(record, SOURCE_PAYMENTMETHOD_ID, SOURCE_PM, SENDER_MSISDN, SENDER_ACCOUNT));
        aliasResolvers.put(TRANSACTION_STATUS, (record, queryValue, serviceName, msisdn) -> handleTransactionKey(queryValue));
    }

    private String handleTransactionType(Record record, String msisdn) {
        Object senderMsisdn = Objects.requireNonNullElse(record.get(SENDER_MSISDN), "");
        Object receiverMsisdn = Objects.requireNonNullElse(record.get(RECEIVER_MSISDN), "");
        if (senderMsisdn.toString().equals(msisdn)) {
            return Enums.ActionTypeEnum.DEBIT.toString();
        }
        if (receiverMsisdn.toString().equals(msisdn)) {
            return Enums.ActionTypeEnum.CREDIT.toString();
        }
        return null;
    }

    private static String handleSplitReceiver(Record record, Object queryValue) {
        Object receiverMsisdn = record.get(RECEIVER_MSISDN);
        if (queryValue != null) {
            return queryValue.toString();
        }
        if (receiverMsisdn != null) {
            return receiverMsisdn.toString();
        }
        return null;
    }

    public String extractContentInsideBraces(String input) {
        Pattern pattern = Pattern.compile("\\{([^}]+)}");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    public String resolveAlias(Record record, String serviceName, String alias, String msisdn) {
        Object queryValue = record.get(alias);
        String value;
        if (aliasResolvers.containsKey(alias)) {
            value = aliasResolvers.get(alias).resolveQueryKey(record, queryValue, serviceName, msisdn);
        } else {
            value = queryValue == null ? null :queryValue.toString();
        }
        return value;
    }

    public String handlePMKey(Record record, String paymentMethodIdAlias, String pmAlias, String msisdnAlias, String accountAlias) {
        String pmValue;
        Long paymentMethodType = record.getValueAsLong(paymentMethodIdAlias);
        Object garParameters = record.get(pmAlias);
        Long accountId = record.getValueAsLong(accountAlias);
        Object msisdn = record.get(msisdnAlias);
        pmValue = resolvePaymentMethod(paymentMethodType, garParameters, msisdn, accountId);
        if (pmValue == null) {
            pmValue = handleNull(accountId, msisdn);
        }
        return pmValue;
    }

    public String resolvePaymentMethod(Long paymentMethodType, Object garParameters, Object msisdn, Long account) {
        Map<String, String> garParametersMap;
        try {
            garParametersMap = objectMapper.readValue(garParameters.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Failed to parse gar parameters", e);
            return null;
        }
        if (msisdn != null) {
            garParametersMap.put(MSISDN, msisdn.toString());
        }
        return paymentMethodAccountIdentifier.getPaymentMethodAccountNumber(
                paymentMethodType,
                garParametersMap,
                account,
                null
        );
    }

    public String getLast4DigitsMasked(String input) {
        if (input == null) {
            return null;
        }
        return input.length() > 4 ? "****" + input.substring(input.length() - 4) : input;
    }

    public String handleNull(Long accountId, Object msisdn) {
        if (accountId != null) {
            String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getGarParametersByCustomerGarAccountID",
                    this.getClass());
            List<Parameter> parameters = List.of(new Parameter(1, accountId));
            List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters);
            if (records == null || records.isEmpty()) {
                return null;
            }
            Long paymentMethodType = records.get(0).getValueAsLong(PAYMENT_METHOD_TYPE);
            Map<String, String> inputParameters = new HashMap<>();
            inputParameters.put(MSISDN, msisdn == null ? records.get(0).getValueAsString(MSISDN) : msisdn.toString());
            records.stream()
                    .filter(record -> record.get(PARAMETER_CODE) != null && record.get(PARAMETER_VALUE) != null)
                    .forEach(record -> inputParameters.put(record.get(PARAMETER_CODE).toString(), record.get(PARAMETER_VALUE).toString()));
            return paymentMethodAccountIdentifier.getPaymentMethodAccountNumber(
                    paymentMethodType,
                    inputParameters,
                    accountId,
                    null
            );
        }
        return null;
    }

    public String handleTransactionKey(Object queryValue) {
        TransactionStatus transactionStatus = TransactionUtils.getTransactionStatusName(queryValue);
        if (transactionStatus == null) {
            return null;
        }
        return transactionStatus.getStatus();
    }
    public QueryKeyResolver handleDefaultAmountKey() {
        return (record, queryValue, serviceName, msisdn) -> {
            String currency = Objects.requireNonNullElse(record.getValueAsString(CURRENCY), "");
            String amount = Objects.requireNonNullElse(queryValue, "0").toString();
            return currency + amount;
        };
    }
    public QueryKeyResolver handleAmountKey() {
        return (record, queryValue, serviceName, msisdn) -> {
            String currency = Objects.requireNonNullElse(record.getValueAsString(CURRENCY), "");
            String amount = Objects.requireNonNullElse(queryValue, "0").toString();
            String feeAmount = Objects.requireNonNullElse(record.get(FEE_AMOUNT), "").toString();
            if (feeAmount.isEmpty() || feeAmount.equals("0")) {
                return null;
            }
            return currency + amount;
        };
    }
}
