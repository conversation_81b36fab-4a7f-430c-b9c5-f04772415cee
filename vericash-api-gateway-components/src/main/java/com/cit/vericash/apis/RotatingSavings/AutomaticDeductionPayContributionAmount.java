package com.cit.vericash.apis.RotatingSavings;


import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ParametersMap;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.service.commons.codeMapping.PaymentMethodDto;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.components.LoadPaymentMethodComponent;
import com.cit.vericash.api.components.SMEConfigLoaderComponent;
import com.cit.vericash.api.components.impl.LoadTransformWalletServiceComponent;
import com.cit.vericash.api.components.impl.ModelMapperInitializerImpl;
import com.cit.vericash.api.components.impl.PrepareTransformCommonsComponent;
import com.cit.vericash.apis.commons.api.ModelMapperInitializer;
import com.cit.vericash.apis.commons.api.MuleMessageSender;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.commons.util.ModelMapper;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.backend.commons.dynamicmodelmapper.Field;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.security.authentication.AuthorizedValidationStep;
import com.cit.vericash.config.VericashBackendConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jxpath.JXPathContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
@Qualifier("com.cit.vericash.apis.RotatingSavings.AutomaticDeduction")
public class AutomaticDeductionPayContributionAmount {
    static Long businessServiceID = null;
    @Autowired
    LoadTransformWalletServiceComponent loadTransformWalletServiceComponent;
    @Autowired
    PrepareTransformCommonsComponent prepareTransformCommonsComponent;
    @Autowired
    SMEConfigLoaderComponent smeConfigLoader;
    @Autowired
    LoadPaymentMethodComponent loadPaymentMethodComponent;
    @Autowired
    AuthorizedValidationStep authorizedValidationStep;
    @Autowired
    VericashBackendConfig config;
    PaymentMethodDto sourcePaymentMethod = new PaymentMethodDto();
    @Autowired
    ConnectionUtil connectionUtil;
    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;
    @Autowired
    private MuleMessageSender muleMessageSender;
    @Autowired
    private CachableVericashApi vericashApiCache;
    private ModelMapperInitializer modelMapperInitializer = new ModelMapperInitializerImpl();

    public void process(Request request) throws Exception {
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        List<Record> records = new ArrayList<Record>();
        fields = setFields(fields);
        Integer chunkSize = propertyLoaderComponent.getPropertyAsInteger("ChunkSize", "application", "application");
        boolean readMoreRecords = true;
        int rowFrom = 0;
        int rowTo = chunkSize;

        while (readMoreRecords) {
            String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("Process_1", this.getClass(), String.valueOf(rowFrom), String.valueOf(rowTo));
            List<Record> tempRecords = connectionUtil.executeSelect(sqlQuery, parameters, fields);
            if (tempRecords == null || tempRecords.size() == 0) {
                readMoreRecords = false;
            }
            records.addAll(tempRecords);
            rowFrom += chunkSize;
            rowTo += chunkSize;
        }
        Message message = request.getMessage();
        VericashAPI vericashAPI = vericashApiCache.getVericashAPI(2288L);
        if (records.size() == 0) {
            throw new GeneralFailureException("Oracle Database Error");
        }
        for (Record record : records) {
            BusinessMessage businessMessage = getBusinessMessage(record, message);
            BusinessMessage businessMessageResponse = (BusinessMessage) muleMessageSender.sendMessageWithJson(businessMessage, vericashAPI);
            if (businessMessageResponse.getStatus().getErrorFlag() && businessMessageResponse.getStatus().getStatusMsg() == null) {
                throw new GeneralFailureException("Runtime Error");
            }
        }

    }


    private BusinessMessage getBusinessMessage(Record record, Message message) throws Exception {
        Payload payload = message.getPayload();
        Long id = record.getValueAsLong("ID");
        Long userId = record.getValueAsLong("CUSTOMER_ID");
        Integer currentTurn = record.getValueAsInt("CURRENT_TURN");
        Long paymentMethodCode = record.getValueAsLong("PAY_FROM_ACCOUNT");
        Long memberID = record.getValueAsLong("MEMBER_ID");
        BigDecimal paymentMethodTypeBig = record.getValueAsBigDecimal("PAYMENT_METHOD_TYPE");
        String paymentMethodType = paymentMethodTypeBig.toString();
        sourcePaymentMethod.setPaymentMethodCode(paymentMethodCode);
        sourcePaymentMethod.setPaymentMethodType(paymentMethodType);
        Long walletId = message.getHeader().getAttributeAsLong("walletId");
        String walletShortCode = record.getValueAsString("WALLET_SHORT_CODE");
        payload.setAttribute("requiredTurn", 0);
        payload.setAttribute("rotatingSavingsId", id);
        payload.setAttribute("turn", currentTurn);
        payload.setAttribute("sourcePaymentMethod", sourcePaymentMethod);
        payload.setAttribute("memberID", memberID);
        message.getHeader().setAttribute("apiCode", "2288");
        message.setPayload(payload);
        BusinessMessage businessMessage = transform(message);
        // to be get from query
        if (businessServiceID == null) {
            businessServiceID = getBusinessServiceId();
        }
        businessMessage.getServiceInfo().setId("" + businessServiceID);
        businessMessage.getHeader().setWalletId(walletId);
        businessMessage.getHeader().setWalletShortCode(walletShortCode);
        businessMessage.getHeader().setCustomerId(userId);


        ParametersMap parameters = new ParametersMap();
        parameters.putAll(payload);
        System.out.println(parameters);
        businessMessage.setParameters(parameters);

        return businessMessage;
    }

    public Long getBusinessServiceId() {
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getBusinessServiceId", this.getClass());
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields.add("ID");
        List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters, fields);
        return records.get(0).getValueAsLong("ID");

    }


    public List<String> setFields(List<String> fields) {
        fields.add("ID");
        fields.add("CURRENT_TURN");
        fields.add("TURN_DAY");
        fields.add("WALLET_SHORT_CODE");
        fields.add("PAY_FROM_ACCOUNT");
        fields.add("PAYMENT_METHOD_TYPE");
        fields.add("WALLET_ID");
        fields.add("MEMBER_ID");
        fields.add("CUSTOMER_ID");
        fields.add("RN");
        return fields;
    }

    public final BusinessMessage transform(Message message) throws Exception {
        BusinessMessage businessMessage = null;

        try {
            Long apiCode = message.getHeader().getAttributeAsLong("apiCode");
            VericashAPI vericashAPI = vericashApiCache.getVericashAPI(apiCode);
            ModelMapper modelMapper = modelMapperInitializer.getModelMapper(vericashAPI);
            List<Field> fieldList = modelMapper.getFields();
            businessMessage = new BusinessMessage();
            JXPathContext targetObject = JXPathContext.newContext(businessMessage);
            smeConfigLoader.prepareSMEBusinessMessage(message, targetObject);
            loadPaymentMethodComponent.loadPaymentMethodUsingOptions(message, targetObject);
            loadTransformWalletServiceComponent.loadTransformWalletService(vericashAPI, null, message, targetObject, businessMessage);
            prepareTransformCommonsComponent.prepareTransformationToBusinessMessage(message, targetObject);
            businessMessage.getServiceInfo().setCode("100038");
            enrichCustomerMessageWithCustomValues(message);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }

        return businessMessage;
    }

    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
    }

}
