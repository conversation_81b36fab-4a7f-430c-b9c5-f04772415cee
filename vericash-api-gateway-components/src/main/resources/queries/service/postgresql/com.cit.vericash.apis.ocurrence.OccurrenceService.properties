#listOfOccurence=SELECT * from (SELECT SCHEDULER_OCCURRENCE.ID, OCCURRENCE_STATUS.NAME AS STATUS  ,\
#  SCHEDULER_OCCURRENCE.DATE_TIME , SCHEDULER_OCCURRENCE.DETAILS, SCHEDULER_OCCURRENCE.FAILURE_REASON,rownum r \
#  FROM SCHEDULER_OCCURRENCE INNER JOIN OCCURRENCE_STATUS ON SCHEDULER_OCCURRENCE.STATUS=OCCURRENCE_STATUS.ID \
#  WHERE SCHEDULER_ID=%1$s) \
#  where r > %2$s and r < %3$s
list.of.occurrences.begin=SELECT * FROM ( SELECT sub.*, row_number() OVER () AS r FROM ( SELECT \
so.ID, \
os.NAME AS STATUS, \
so.DATE_TIME \
AS DATE_TIME, \
so.DETAILS, \
so.REQUEST AS OCCURRENCE_REQUEST, \
s.REQUEST AS RECURRING_REQUEST, \
so.FAILURE_REASON \
FROM SCHEDULER s INNER JOIN SCHEDULER_OCCURRENCE so ON s.id = so.SCHEDULER_ID \
INNER JOIN OCCURRENCE_STATUS os ON so.STATUS = os.ID \
WHERE s.CUSTOMER_ID = %1$s AND s.id  = %2$s 
list.of.occurrences.filter.status=  AND os.ID = %1$s 
list.of.occurrences.filter.dateFrom=  AND so.DATE_TIME >= TO_TIMESTAMP('%1$s','YYYY-MM-DD HH24:MI:SS') 
list.of.occurrences.filter.dateTo=  AND so.DATE_TIME <= TO_TIMESTAMP('%1$s','YYYY-MM-DD HH24:MI:SS') 
list.of.occurrences.end= ORDER BY so.DATE_TIME DESC) sub ) AS temp WHERE r < %1$s  WHERE r > %2$s