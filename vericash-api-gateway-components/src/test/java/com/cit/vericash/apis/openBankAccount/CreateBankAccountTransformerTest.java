package com.cit.vericash.apis.openBankAccount;

import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Record;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class CreateBankAccountTransformerTest {
    @Mock
    ConnectionUtil connectionUtil;
    @InjectMocks
    CreateBankAccountTransformer createBankAccountTransformer;

    @Test
    public void testValidateRequest_withValidData_shouldPass() throws Exception {
        // Arrange
        Message mockMessage = mock(Message.class);
        Payload mockPayload = mock(Payload.class);
        when(mockMessage.getPayload()).thenReturn(mockPayload);
        when(mockPayload.getAttributeAsString("sourcePaymentMethodType")).thenReturn("VALID_PAYMENT_METHOD");
        when(mockPayload.get("fileDownloadURL")).thenReturn(List.of("fileDownloadURL"));

        Record mockRecord = mock(Record.class);
        when(mockRecord.getValueAsInt("OPENED_UPON_CUS_REQ")).thenReturn(1);
        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(Collections.singletonList(mockRecord));

        when(mockRecord.getValueAsString("REQUIRED_DATA")).thenReturn("{\"Documents\":[\"ID\"],\"types\":[\"pdf\",\"png\"],\"DocumentSize\":1}");
        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(Collections.singletonList(mockRecord));


        // Act & Assert
        assertDoesNotThrow(() -> createBankAccountTransformer.enrichCustomerMessageWithCustomValues(mockMessage));
    }

    @Test
    public void testValidateRequest_withInvalidPaymentMethod_shouldThrowException() {
        // Arrange
        Message mockMessage = mock(Message.class);
        Payload mockPayload = mock(Payload.class);
        when(mockMessage.getPayload()).thenReturn(mockPayload);
        when(mockPayload.getAttributeAsString("sourcePaymentMethodType")).thenReturn("INVALID_PAYMENT_METHOD");

        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(new ArrayList<>());

        // Act & Assert
        QuickActionException exception = assertThrows(QuickActionException.class, () ->
                createBankAccountTransformer.enrichCustomerMessageWithCustomValues(mockMessage));
        assertEquals(ErrorCode.OPEN_ACCOUNT_PM_NOT_CONFIGURED.getErrorCode(), exception.getErrorCode());
    }

    @Test
    public void testValidateUploadedDocuments_withValidDocuments_shouldPass() throws Exception {
        // Arrange
        Message mockMessage = mock(Message.class);
        Payload mockPayload = mock(Payload.class);
        when(mockMessage.getPayload()).thenReturn(mockPayload);
        when(mockPayload.getAttributeAsString("sourcePaymentMethodType")).thenReturn("VALID_PAYMENT_METHOD");
        when(mockPayload.get("fileDownloadURL")).thenReturn(List.of("fileDownloadURL"));


        Record mockRecord = mock(Record.class);
        when(mockRecord.getValueAsString("REQUIRED_DATA")).thenReturn("{\"Documents\": [\"doc1\", \"doc2\"]}");
        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(Collections.singletonList(mockRecord));

        when(mockRecord.getValueAsInt("OPENED_UPON_CUS_REQ")).thenReturn(1);
        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(Collections.singletonList(mockRecord));

        List<String> uploadedFiles = List.of("file1", "file2");
        when(mockPayload.get("fileDownloadURL")).thenReturn(uploadedFiles);


        // Act & Assert
        assertDoesNotThrow(() -> createBankAccountTransformer.enrichCustomerMessageWithCustomValues(mockMessage));
    }


    @Test
    public void testValidateOpenUponCustomerRequestFlag_withFlagNotSet_shouldThrowException() {
        // Arrange
        Message mockMessage = mock(Message.class);
        Payload mockPayload = mock(Payload.class);
        when(mockMessage.getPayload()).thenReturn(mockPayload);
        when(mockPayload.getAttributeAsString("sourcePaymentMethodType")).thenReturn("INVALID_PAYMENT_METHOD");

        Record mockRecord = mock(Record.class);
        when(mockRecord.getValueAsInt("OPENED_UPON_CUS_REQ")).thenReturn(0); // Flag not set
        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(Collections.singletonList(mockRecord));

        // Act & Assert
        QuickActionException exception = assertThrows(QuickActionException.class, () ->
                createBankAccountTransformer.enrichCustomerMessageWithCustomValues(mockMessage)
        );
        assertEquals(ErrorCode.OPEN_ACCOUNT_PM_NOT_CONFIGURED.getErrorCode(), exception.getErrorCode());
    }

    @Test
    public void testValidateUploadedDocuments_withNoPaymentMethodConfig_shouldThrowException() {
        // Arrange
        Message mockMessage = mock(Message.class);
        Payload mockPayload = mock(Payload.class);
        when(mockMessage.getPayload()).thenReturn(mockPayload);
        when(mockPayload.getAttributeAsString("sourcePaymentMethodType")).thenReturn("VALID_PAYMENT_METHOD");

        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(Collections.emptyList());

        // Act & Assert
        QuickActionException exception = assertThrows(QuickActionException.class, () ->
                createBankAccountTransformer.enrichCustomerMessageWithCustomValues(mockMessage)
        );
        assertEquals(ErrorCode.OPEN_ACCOUNT_PM_NOT_CONFIGURED.getErrorCode(), exception.getErrorCode());
    }

    @Test
    public void testParseRequiredDocuments_withValidJsonNoDocuments_shouldReturnZero() {
        // Arrange
        String validJson = "{\"Documents\": []}";

        // Act
        int documentCount = createBankAccountTransformer.parseRequiredDocuments(validJson);

        // Assert
        assertEquals(0, documentCount);
    }


    @Test
    public void testFetchPaymentMethodConfig_withEmptyResult_shouldReturnEmptyList() {
        // Arrange
        String paymentMethodTypeId = "INVALID_PAYMENT_METHOD";
        String queryName = "get.source.payment.method.config";

        when(connectionUtil.executeSelect(anyString(), anyList())).thenReturn(Collections.emptyList());

        // Act
        List<Record> result = createBankAccountTransformer.fetchPaymentMethodConfig(paymentMethodTypeId, queryName);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testValidateRequest_withNullPaymentMethodType_shouldThrowException() {
        // Arrange
        Message mockMessage = mock(Message.class);
        Payload mockPayload = mock(Payload.class);
        when(mockMessage.getPayload()).thenReturn(mockPayload);
        when(mockPayload.getAttributeAsString("sourcePaymentMethodType")).thenReturn(null);

        // Act & Assert
        QuickActionException exception = assertThrows(QuickActionException.class, () ->
                createBankAccountTransformer.enrichCustomerMessageWithCustomValues(mockMessage)
        );
        assertEquals(ErrorCode.OPEN_ACCOUNT_PM_NOT_CONFIGURED.getErrorCode(), exception.getErrorCode());
    }

}
