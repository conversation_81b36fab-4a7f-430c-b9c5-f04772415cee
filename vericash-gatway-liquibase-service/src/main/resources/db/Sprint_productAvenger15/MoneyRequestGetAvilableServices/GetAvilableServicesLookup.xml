<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

<!--    <changeSet failOnError="false" author="khairala" id="add Get avilable services lookup">-->
<!--      <sql>-->
<!--	  INSERT INTO LOOKUP_FRAMEWORK_CONFIG (ID, CACHING_KEY_PATTERN, CACHING_TYPE, IS_CACHED_LOOKUP, LOOKUP_NAME, LOOKUP_TYPE, RESPONSE_TRANSFORMER_MAPPING, CUSTOME_RESPONSE_TRANSFORMER, CUSTOM_TRANSFORMER_CONFIG_FILE, ENDPOINT_NAME, SERVICE_NAME) VALUES((select max(ID) +1 from LOOKUP_FRAMEWORK_CONFIG), '0', 0, 0, 'moneyRequest.get.available.services',0,NULL,'moneyRequestGetAvailableServices',NULL,NULL,NULL);-->

<!--</sql>-->
<!--    </changeSet>-->
<!--	-->
<!--	<changeSet failOnError="false" author="khairala" id="add Get avilable services lookup PORTAL">-->
<!--		<sql>-->
<!--			INSERT INTO PORTAL_LOOKUPS_CONFIG (ID, LOOKUP_NAME, LOOKUP_KEY, LOOKUP_QUERY, CACHEABLE) VALUES((select max(ID) +1 from PORTAL_LOOKUPS_CONFIG), 'Request Money Get Avilable Services','moneyRequest.get.available.services','SELECT  CODE  , NAME FROM MONEY_REQUEST_SERVICE_CONFIG MRS INNER JOIN MONEY_REQUEST_ADMIN_CONFIG MRA ON MRS.ADMIN_CONFIG_ID = MRA.ID INNER JOIN API_VERICASH_APIS AVA ON MRS.PARENT_API_CODE = AVA.CODE WHERE MRA."TYPE" =''${filters.getAttributeAsLong(''moneyRequestTypeId'')}''',0);-->

<!--		</sql>-->
<!--	</changeSet>-->
    <changeSet id="UpdateLookUpQuery" author="Zyad Elalfy">

        <update tableName="PORTAL_LOOKUPS_CONFIG">
            <column name="LOOKUP_QUERY" value="SELECT CODE , NAME , MRA.TYPE , MRT.TYPE_NAME FROM MONEY_REQUEST_SERVICE_CONFIG MRS INNER JOIN MONEY_REQUEST_ADMIN_CONFIG MRA ON MRS.ADMIN_CONFIG_ID = MRA.ID INNER JOIN API_VERICASH_APIS AVA ON MRS.PARENT_API_CODE = AVA.CODE LEFT JOIN MONEY_REQUEST_TYPE mrt ON MRT.ID =MRA.TYPE WHERE MRA.TYPE ='${filters.getAttributeAsLong('moneyRequestTypeId')}' OR '${filters.getAttributeAsLong('moneyRequestTypeId')}' IS NULL"/>
            <where>id=2444</where>
        </update>
    </changeSet>

	

</databaseChangeLog>