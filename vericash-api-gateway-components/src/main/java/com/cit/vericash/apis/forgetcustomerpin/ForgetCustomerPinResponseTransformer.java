package com.cit.vericash.apis.forgetcustomerpin;

import com.cit.mpaymentapp.common.message.AuthContainer;
import com.cit.mpaymentapp.common.message.AuthenticationModel;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.forgetcustomerpin.ForgetCustomerPinResponseTransformer")
public class ForgetCustomerPinResponseTransformer implements MuleResponseTransformer {
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, String> result = new HashMap<String, String>();

        HashMap<Object, List<Map<String, Object[]>>> resultMissingAuth = new HashMap<Object, List<Map<String, Object[]>>>();

        boolean skipDynamicAuth = businessMessage.getParameters().getAttributeAsBoolean("skipValidateMissingDynamicAuthentication");
        AuthContainer authContainer = businessMessage.getAuthContainer();

        if (!skipDynamicAuth && authContainer != null) {
            AuthenticationModel[] authenticationModelList = authContainer.getAuthenticationModels();
            List<Map<String, Object[]>> authContainers = new LinkedList<Map<String, Object[]>>();
            Map<String, Object[]> authmodel = new HashMap<String, Object[]>();
            authmodel.put("authenticationModels", authenticationModelList);
            authContainers.add(authmodel);
            resultMissingAuth.put("authContainers", authContainers);
            return resultMissingAuth;
        } else
            return result;
    }
}
