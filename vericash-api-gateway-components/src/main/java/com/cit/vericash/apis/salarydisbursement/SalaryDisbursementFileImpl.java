package com.cit.vericash.apis.salarydisbursement;

import com.cit.vericash.api.entity.DisbursementFile;
import com.cit.vericash.api.repository.DisbursementFileRepository;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;

@Component
public class SalaryDisbursementFileImpl implements SalaryDisbursementFile {
    @Autowired
    private DisbursementFileRepository disbursementFileRepository;

    /*
     *
     * convert from BufferedInputStream to ByteArray
     */
    private static byte[] toByteArray(InputStream is) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        int reads = is.read();

        while (reads != -1) {
            byteArrayOutputStream.write(reads);
            reads = is.read();
        }
        return byteArrayOutputStream.toByteArray();
    }

    @Override
    public String saveSalaryDisbursementFile(String groupId, MultipartFile file) throws IOException {
        Optional<String> fileExtension = getFileExtension(file.getOriginalFilename());
        if(fileExtension.isEmpty())
            throw new QuickActionException("no value retrieved", ErrorCode.NO_VALUE_RETRIEVED);
        DisbursementFile disbursementFile = new DisbursementFile();
        disbursementFile.setGroupId(Long.valueOf(groupId));
        disbursementFile.setFileExtention(fileExtension.get());
        disbursementFile.setFile(toByteArray(file.getInputStream()));
        disbursementFileRepository.save(disbursementFile);
        return "Success";
    }

    private Optional<String> getFileExtension(String fileName) {
        return Optional.ofNullable(fileName)
                .filter(f -> f.contains("."))
                .map(f -> f.substring(fileName.lastIndexOf(".") + 1));
    }
}
