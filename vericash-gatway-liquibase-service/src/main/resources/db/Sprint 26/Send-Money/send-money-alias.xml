<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- ChangeSet for inserting data into SERVICE_CONFIG_MAP -->
    <changeSet id="1" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="155"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From VC To Prepaied Card GTP by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="155"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="156"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Wallet to wallet"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="156"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="157"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From VC To Debit Card by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="157"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="158"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From VC To CIB by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="158"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="159"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Prepaid To Debit Card by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="159"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="160"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Prepaid To CIB by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="160"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="161"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Wallet To Debit Card by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="161"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="162"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Wallet To CIB by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="162"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="163"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Debit Card To Debit Card by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="163"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="164"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Debit Card To Prepaid by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="164"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="165"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Debit Card To Wallet by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="165"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="166"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Debit Card To VC by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="166"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="167"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Debit Card To CIB by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="167"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="168"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From CIB To Debit Card by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="168"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="169"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From CIB To VC by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="169"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="170"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From CIB To Prepaid by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="170"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="171"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From CIB To Wallet by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="171"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="172"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From CIB To CIB by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="172"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="173"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From VC To Wallet by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="173"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="174"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From VC To VC by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="174"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="175"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Wallet to VC"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="175"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="176"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Prepaid To VC by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="176"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="177"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Prepaid Card GTP To Prepaid Card GTP"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="177"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="178"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Prepaid Card GTP To Wallet"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="178"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="179"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Wallet to Prepaid Card GTP"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="179"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99552"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

    </changeSet>




</databaseChangeLog>
