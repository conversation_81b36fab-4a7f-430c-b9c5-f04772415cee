package com.cit.vericash.apis.EnquireBankAccount;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ExternalIntegrationResponse;
import com.cit.mpaymentapp.common.paymentmethod.DestinationPaymentMethodDTO;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.security.model.AuthContainer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.EnquireBankAccount.DepositEnquireBankAccountResponseTransformer")
public class DepositEnquireBankAccountResponseTransformer implements MuleResponseTransformer {
    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, Object> result = new HashMap<String, Object>();
        Map<String, Object> successScreenData = new HashMap<String, Object>();
        if (businessMessage.getExternalIntegrationResponse() != null) {
            ExternalIntegrationResponse externalIntegrationResponse = businessMessage.getExternalIntegrationResponse();
            successScreenData.put("First Name", externalIntegrationResponse.getFirstName());
            successScreenData.put("Middle Name", externalIntegrationResponse.getMiddleName());
            successScreenData.put("Last Name", externalIntegrationResponse.getLastName());
            successScreenData.put("Customer Mobile Number", externalIntegrationResponse.getPhoneNumber());
            successScreenData.put("Account Number", externalIntegrationResponse.getAccountNumber());
            successScreenData.put("Country", externalIntegrationResponse.getCountryCode());
            successScreenData.put("Currency", externalIntegrationResponse.getCurrency());
            successScreenData.put("paymentMethodType", businessMessage.getSourcePaymentMethod().getPaymentMethodType());
            result.put("successScreenData", successScreenData);
        }
        Map<String, Object> additionalScreenData = buildAdditionalScreenData(businessMessage);
        if (additionalScreenData.size() > 0) {
            result.put("AdditionalScreenData", additionalScreenData);
        }

        return result;
    }

    private Map<String, Object> buildAdditionalScreenData(BusinessMessage businessMessage) {
        Map<String, Object> additionalScreenData = new HashMap<String, Object>();
        ObjectMapper mapper = new ObjectMapper();
        DestinationPaymentMethodDTO destinationPaymentMethodDTO = mapper.convertValue(businessMessage.getParameters().get("destinationPaymentMethod"), new TypeReference<>() {
        });
        additionalScreenData.put("deposite_amount", businessMessage.getParameters().getAttributeAsString("amount"));
        additionalScreenData.put("account_type", businessMessage.getParameters().getAttributeAsString("accountType"));
        additionalScreenData.put("Payment-Options", businessMessage.getParameters().getAttributeAsString("PaymentOptions"));
        if (destinationPaymentMethodDTO != null) {
            additionalScreenData.put("payment_method", destinationPaymentMethodDTO.getPaymentMethodType());
            if (destinationPaymentMethodDTO.getDynamicGroup() != null && destinationPaymentMethodDTO.getDynamicGroup().getInputParameters().size() > 0) {
                additionalScreenData.put("bank_account_number", destinationPaymentMethodDTO.getDynamicGroup().getInputParameters().get(0).getParameterValue());
            }
        }
        return additionalScreenData;


    }

}
