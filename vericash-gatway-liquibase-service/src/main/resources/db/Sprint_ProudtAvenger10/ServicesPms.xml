<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
		xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
		xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
   http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">


   <changeSet failOnError="true" author="fatma" id="addNewCloumnInServicePayment">

	  <sql>
ALTER TABLE "SERVICES_PAYMENTMETHOD"
ADD ( "VAILDATE_PM" NUMBER(1) NULL  ) ;

 
	  </sql>
	 
	
    </changeSet>
	
	
	
   <changeSet failOnError="true" author="fatma" id="Add error code">
     <insert tableName="API_ERROR">
      <column name="ERROR_CODE" value="VAL0180"/>
      <column name="ERROR_DESCRIPTION" value="Invailed Account Number"/>
      
	 </insert>
	 
	
    </changeSet>
	
 <changeSet failOnError="false" author="fatma" id="addNewCloumnInApiVericash">

	  <sql>
     ALTER TABLE "API_VERICASH_APIS"
    ADD ( "IS_EXTERNAL_INTEGRATION" NUMBER DEFAULT 0  NOT NULL  ) ;

 
	  </sql>
	 
	
    </changeSet>
	
	
 <changeSet failOnError="true" author="fatma" id="insert vaildate into BUSINESS_SERVICE_CONFIG">
     <insert tableName="BUSINESS_SERVICE_CONFIG">
      <column name="ID" valueComputed="(select max(ID) +1 from BUSINESS_SERVICE_CONFIG)"/>
      <column name="BANKING_AGENT" value="0"/>
      <column name="BUSINESSSERVICECATEGORY" value="0"/>
      <column name="ISDEFAULTSERVICE" value="0"/>
      <column name="IS_EXPOSABLE" value="0"/>
     <column name="IS_MULTI_WALLET" value="0"/>
	 
	   <column name="NAME" value="Validate PM And Get Details"/>
	   
	     <column name="BUSINESS_SERVICE_TYPE" value="889099"/>
		 
		   <column name="SEGMENTATIONTYPE_ID" value="0"/>
		     <column name="ORGANIZATION_ID" value="2421"/>
			   <column name="IS_ENABLED" value="1"/>
		  
	 </insert>
	 </changeSet>
	 
	 <changeSet failOnError="true" author="fatma" id="addVAILDATE_PMasdefault">

	  <sql>
	  
	  
	  ALTER TABLE "SERVICES_PAYMENTMETHOD"
   MODIFY ( "VAILDATE_PM" NUMBER(1) DEFAULT 0  ) ;
	   </sql>
	 
	
    </changeSet>
	
		 
	 <changeSet failOnError="true" author="fatma" id="addVAILDATE_PMasdefaultnotnull">

	  <sql>
	  
	  
	ALTER TABLE "SERVICES_PAYMENTMETHOD"
MODIFY ( "VAILDATE_PM" NUMBER(1) DEFAULT 0  NOT NULL  ) ;
	   </sql>
	 
	
    </changeSet>


	
	   <changeSet failOnError="true" author="fatma" id="AllowedPAYMENTMETHODsForCIBSOURCEAndDEPITE">
     <insert tableName="SERVICES_PAYMENTMETHOD">
      <column name="ID" valueComputed="(select max(ID) +1 from SERVICES_PAYMENTMETHOD)"/>
      <column name="API_CODE" value="4000"/>
      <column name="PAYMENMETHODTYPE" value="871"/>
      <column name="LOOKUP_ENUM" value="1"/>
      <column name="GAR_STATUS" value="1"/>
     <column name="USER_TYPE" value="1"/>
	 </insert>
	 
	
	
	 <insert tableName="SERVICES_PAYMENTMETHOD">
      <column name="ID" valueComputed="(select max(ID) +1 from SERVICES_PAYMENTMETHOD)"/>
      <column name="API_CODE" value="4000"/>
      <column name="PAYMENMETHODTYPE" value="870"/>
      <column name="LOOKUP_ENUM" value="1"/>
      <column name="GAR_STATUS" value="1"/>
     <column name="USER_TYPE" value="1"/>
	 </insert>
    </changeSet>
	

</databaseChangeLog>
	
