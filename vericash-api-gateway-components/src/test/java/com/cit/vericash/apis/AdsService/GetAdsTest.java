package com.cit.vericash.apis.AdsService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.cit.vericash.api.entity.Ad;
import com.cit.vericash.api.entity.ApiVericashApi;
import com.cit.vericash.api.repository.ApiVericashApiRepository;
import com.cit.vericash.apis.AdsService.FilterResponsabilityChain.AdsFilterHandler;
import com.cit.vericash.apis.AdsService.segmentation.SegmentStrategyResolver;
import com.cit.vericash.apis.AdsService.segmentation.strategies.SingleSegmentStrategy;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.AdditionalData;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.EchoData;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;


import java.sql.Date;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Stream;

import oracle.sql.TIMESTAMP;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {GetAds.class})
@ExtendWith(SpringExtension.class)
class GetAdsTest {
    @MockBean
    private AdStrategy adStrategy;

    @MockBean
    private ApiVericashApiRepository apiVericashApiRepository;

    @Autowired
    private GetAds getAds;

    @MockBean
    private SegmentStrategyResolver segmentStrategyResolver;

    @MockBean
    private SingleSegmentStrategy singleSegmentStrategy;


    @Test
    @DisplayName("Test process with empty ads list")
    void testProcessWithEmptyAdsList() throws Exception {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("beCustomerId", null);
        map.put("country", 197L);
        map.put("creationDate", new TIMESTAMP());
        map.put("customerTypeId", 7806531L);
        map.put("customerAccountTypeId", 0L);
        map.put("newCustomerPeriod", 30L);

        // Arrange
        when(adStrategy.getAds(Mockito.<String>any())).thenReturn(new ArrayList<>());
        when(segmentStrategyResolver.getActiveStrategy()).thenReturn(singleSegmentStrategy);
        when(singleSegmentStrategy.loadSegmentationData(Mockito.<Request>any())).thenReturn(map);

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Payload payload = new Payload();

        LinkedHashMap<String, Object> map2 = new LinkedHashMap<>();
        map2.put("platformName", "android");

        payload.put("clientInfo",map2);
        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(payload);

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);

        // Act
        List<AdDTO> result = (List<AdDTO>) getAds.process(request);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Test process with null code")
    void testProcessWithNullCode() throws Exception {
        // Mock ad with null code
        Ad ad = new Ad();
        ad.setId(1);
        ad.setCode(null);

        List<Ad> mockAds = new ArrayList<>();
        mockAds.add(ad);

        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("beCustomerId", null);
        map.put("country", 197L);
        map.put("creationDate", new TIMESTAMP());
        map.put("customerTypeId", 7806531L);
        map.put("customerAccountTypeId", 0L);
        map.put("newCustomerPeriod", 30L);

        // Arrange
        when(singleSegmentStrategy.loadSegmentationData(Mockito.<Request>any())).thenReturn(map);

        when(adStrategy.getAds(Mockito.<String>any())).thenReturn(mockAds);
        when(segmentStrategyResolver.getActiveStrategy()).thenReturn(singleSegmentStrategy);

        Request request = createTestRequest();

        // Act
        List<AdDTO> result = (List<AdDTO>) getAds.process(request);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    @DisplayName("Test process with missing walletShortCode")
    void testProcessWithMissingWalletShortCode() throws Exception {
        // Arrange
        when(adStrategy.getAds(Mockito.<String>any())).thenReturn(new ArrayList<>());
        when(segmentStrategyResolver.getActiveStrategy()).thenReturn(singleSegmentStrategy);

        Request request = createTestRequest();
        request.getMessage().getHeader().remove("walletShortCode");

        // Act & Assert
        assertThrows(Exception.class, () -> getAds.process(request));
    }

    @Test
    @DisplayName("Test process with null ADS_IMAGE_URL")
    void testProcessWithNullImageUrl() throws Exception {
        // Arrange
        when(adStrategy.getAds(Mockito.<String>any())).thenReturn(new ArrayList<>());
        when(segmentStrategyResolver.getActiveStrategy()).thenReturn(singleSegmentStrategy);
//        when(System.getenv("ADS_IMAGE_URL")).thenReturn(null);

        Request request = createTestRequest();

        // Act & Assert
        assertThrows(Exception.class, () -> getAds.process(request));
    }

    private Request createTestRequest() {
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Payload payload = new Payload();

        LinkedHashMap<String, Object> map2 = new LinkedHashMap<>();
        map2.put("platformName", "android");

        payload.put("clientInfo",map2);

        Header header = new Header();
        header.put("walletShortCode", "2020");

        Message message = new Message();
        message.setDynamicPayload(dynamicPayload);
        message.setHeader(header);
        message.setPayload(payload);

        Request request = new Request();
        request.setMessage(message);
        return request;
    }

    @Test
    @DisplayName("Test process with successful ad retrieval")
    void testProcessWithSuccessfulAdRetrieval() throws Exception {
        // Create mock ad
        Ad mockAd = new Ad();

        mockAd.setId(1);
        mockAd.setName("Test Ad");
        mockAd.setTitle("Welcome Ad");
        mockAd.setDescription("This is a test ad");
        mockAd.setCode("TEST_CODE_123");
        mockAd.setCtaName("Learn More");
        mockAd.setCtaUrl("https://example.com/cta");
        mockAd.setImageUrl("ads/test-ad.jpg");
        mockAd.setDismissible(true);
        mockAd.setCustomerTypeId(4L);
        // For DeviceAndCountryFilter:
        mockAd.setCountryIds(Arrays.asList(197L)); // Must match segmentationData country
        mockAd.setAudienceCategoryId(1L); // 1 = ANDROID_ID

        // For AudienceTypeFilter:
        mockAd.setAudienceId(1L); // 1 = REGISTERED_AND_NEW_REGISTERED (will always pass filter), or set to expected

        // For PublishDateFilter:
        mockAd.setPublishDate(LocalDateTime.now().minusDays(1)); // already published
        mockAd.setExpiryDate(LocalDateTime.now().plusDays(1));   // not yet expired
        mockAd.setExecutionModeId(1); // 1 = REALTIME_ID or 2 = SCHEDULED_ID (make sure to match logic)


        List<Ad> mockAds = new ArrayList<>();
        mockAds.add(mockAd);

        // Mock API repository response
        ApiVericashApi mockApi = new ApiVericashApi();
        mockApi.setCode("TEST_CODE_123");
        mockApi.setName("Test API Service");

        // Arrange
        when(adStrategy.getAds(anyString())).thenReturn(mockAds);
        when(segmentStrategyResolver.getActiveStrategy()).thenReturn(singleSegmentStrategy);
        when(singleSegmentStrategy.filterAd(Mockito.<Stream<Ad>>any(), Mockito.<LinkedHashMap<String, Object>>any())).thenReturn(Stream.of(mockAd));

        LinkedHashMap<String, Object> segmentationData = new LinkedHashMap<>();
        segmentationData.put("beCustomerId", 123456L);
        segmentationData.put("country", 197L);
        segmentationData.put("creationDate", new TIMESTAMP(new Date(Instant.now().toEpochMilli())));
        segmentationData.put("customerTypeId", 7806531L);
        segmentationData.put("customerAccountTypeId", 0L);
        segmentationData.put("newCustomerPeriod", 30L);

        when(singleSegmentStrategy.loadSegmentationData(Mockito.<Request>any())).thenReturn(segmentationData);
        when(apiVericashApiRepository.findByCode("TEST_CODE_123")).thenReturn(Optional.of(mockApi));

        // Create request
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Payload payload = new Payload();

        LinkedHashMap<String, Object> map2 = new LinkedHashMap<>();
        map2.put("platformName", "android");

        payload.put("clientInfo", map2);

        Header header = new Header();
        header.put("walletShortCode", "2020");

        Message message = new Message();
        message.setDynamicPayload(dynamicPayload);
        message.setHeader(header);
        message.setPayload(payload);

        Request request = new Request();
        request.setMessage(message);

        // Act
        List<AdDTO> result = (List<AdDTO>) getAds.process(request);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());

        AdDTO adDto = result.get(0);
        assertEquals(1, adDto.getId());
        assertEquals("Test Ad", adDto.getName());
        assertEquals("Welcome Ad", adDto.getTitle());
        assertEquals("This is a test ad", adDto.getDescription());
        assertEquals("Learn More", adDto.getCtaName());
        assertEquals("https://example.com/cta", adDto.getCtaUrl());
    }

    @Test
    @DisplayName("Test process with successful ad retrieval null code")
    void testProcessWithSuccessfulAdRetrievalNullCode() throws Exception {
        // Create mock ad
        Ad mockAd = new Ad();

        mockAd.setId(1);
        mockAd.setName("Test Ad");
        mockAd.setTitle("Welcome Ad");
        mockAd.setDescription("This is a test ad");
        mockAd.setCode(null);
        mockAd.setCtaName("Learn More");
        mockAd.setCtaUrl("https://example.com/cta");
        mockAd.setImageUrl("ads/test-ad.jpg");
        mockAd.setDismissible(true);
        mockAd.setCustomerTypeId(4L);
        // For DeviceAndCountryFilter:
        mockAd.setCountryIds(Arrays.asList(197L)); // Must match segmentationData country
        mockAd.setAudienceCategoryId(1L); // 1 = ANDROID_ID

        // For AudienceTypeFilter:
        mockAd.setAudienceId(1L); // 1 = REGISTERED_AND_NEW_REGISTERED (will always pass filter), or set to expected

        // For PublishDateFilter:
        mockAd.setPublishDate(LocalDateTime.now().minusDays(1)); // already published
        mockAd.setExpiryDate(LocalDateTime.now().plusDays(1));   // not yet expired
        mockAd.setExecutionModeId(1); // 1 = REALTIME_ID or 2 = SCHEDULED_ID (make sure to match logic)


        List<Ad> mockAds = new ArrayList<>();
        mockAds.add(mockAd);

        // Mock API repository response
        ApiVericashApi mockApi = new ApiVericashApi();
        mockApi.setCode("TEST_CODE_123");
        mockApi.setName("Test API Service");

        // Arrange
        when(adStrategy.getAds(anyString())).thenReturn(mockAds);
        when(segmentStrategyResolver.getActiveStrategy()).thenReturn(singleSegmentStrategy);
        when(singleSegmentStrategy.filterAd(Mockito.<Stream<Ad>>any(), Mockito.<LinkedHashMap<String, Object>>any())).thenReturn(Stream.of(mockAd));
        when(apiVericashApiRepository.findByCode(any())).thenReturn(Optional.empty());

        LinkedHashMap<String, Object> segmentationData = new LinkedHashMap<>();
        segmentationData.put("beCustomerId", 123456L);
        segmentationData.put("country", 197L);
        segmentationData.put("creationDate", new TIMESTAMP(new Date(Instant.now().toEpochMilli())));
        segmentationData.put("customerTypeId", 7806531L);
        segmentationData.put("customerAccountTypeId", 0L);
        segmentationData.put("newCustomerPeriod", 30L);

        when(singleSegmentStrategy.loadSegmentationData(Mockito.<Request>any())).thenReturn(segmentationData);
        when(apiVericashApiRepository.findByCode("TEST_CODE_123")).thenReturn(Optional.of(mockApi));

        // Create request
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Payload payload = new Payload();

        LinkedHashMap<String, Object> map2 = new LinkedHashMap<>();
        map2.put("platformName", "android");

        payload.put("clientInfo", map2);

        Header header = new Header();
        header.put("walletShortCode", "2020");

        Message message = new Message();
        message.setDynamicPayload(dynamicPayload);
        message.setHeader(header);
        message.setPayload(payload);

        Request request = new Request();
        request.setMessage(message);

        // Act
        List<AdDTO> result = (List<AdDTO>) getAds.process(request);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());

        AdDTO adDto = result.get(0);
        assertEquals(1, adDto.getId());
        assertEquals("Test Ad", adDto.getName());
        assertEquals("Welcome Ad", adDto.getTitle());
        assertEquals("This is a test ad", adDto.getDescription());
        assertEquals("Learn More", adDto.getCtaName());
        assertEquals("https://example.com/cta", adDto.getCtaUrl());
    }
}
