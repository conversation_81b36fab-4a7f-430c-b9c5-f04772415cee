package com.cit.vericash.apis.gettransactionhistory;

import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.mpaymentapp.common.paymentmethod.SourcePaymentMethodDTO;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class GetTransactionHistoryRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String FROM_KEY = "from";
    private final String TRX_INFO_KEY = "transactionInfo";
    private final String TO_KEY = "to";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        try {
            Gson gson = new Gson();
            String listofpaymentmethod = gson.toJson(message.getPayload().get("sourcePaymentMethods"));
            final ObjectMapper objectMapper = new ObjectMapper();
            List<SourcePaymentMethodDTO> paymentMethodsList = objectMapper.readValue(listofpaymentmethod, new TypeReference<List<SourcePaymentMethodDTO>>() {
            });
            message.getPayload().put("sourcePaymentMethodsList", paymentMethodsList);

            String from = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(FROM_KEY));
            String to = message.getPayload().getAttributeAsString(cacheableFieldParameters.getRequestParameters(TO_KEY));
            boolean fromValue = com.cit.vericash.apis.commons.util.DateUtil.validateDate(from);
            boolean toValue = com.cit.vericash.apis.commons.util.DateUtil.validateDate(to);
            TransactionInformation transactionInformation = new TransactionInformation();

            if (toValue && fromValue) {
                Date fromDate = com.cit.vericash.apis.commons.util.DateUtil.transferDate(from);
                Date toDate = com.cit.vericash.apis.commons.util.DateUtil.transferDate(to);

                transactionInformation.setDateFrom(fromDate);
                transactionInformation.setDateTo(toDate);
                message.getPayload().put(cacheableFieldParameters.getRequestParameters(TRX_INFO_KEY), transactionInformation);
            } else {
                message.getPayload().put(cacheableFieldParameters.getRequestParameters(TRX_INFO_KEY), transactionInformation);
            }

        } catch (Exception e) {
            throw e;
        }
    }
}
