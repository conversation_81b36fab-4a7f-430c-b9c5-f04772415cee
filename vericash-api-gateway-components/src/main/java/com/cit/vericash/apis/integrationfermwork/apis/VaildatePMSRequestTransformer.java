package com.cit.vericash.apis.integrationfermwork.apis;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.api.components.impl.integrationfremwork.MessageToDynamicPaylodTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;

@Component
@Qualifier("com.cit.vericash.apis.integrationfermwork.apis.VaildatePMSRequestTransformer")
public class VaildatePMSRequestTransformer  extends MessageToDynamicPaylodTransformerImpl {

    @Autowired
    PropertyLoaderComponent propertyLoader;

    private final  String VaildateServiceName="VaildateServiceName";
    private final String  PMTypeCode="PMTypeCode";

    private final  String pmsTypeKey="pmsTypeKey";
    private final  String integration_serviceName ="integration_serviceName";
    String   serviceCode="serviceCode";
    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        String targetPmType = validateRequest(message);


        Payload payload = message.getDynamicPayload().getPayload();
        System.out.println("YA Fatma Request" + payload);

        ArrayList<String> pmsTypeKey = new ArrayList(Arrays.asList(propertyLoader.getPropertyAsString("pmsTypeKey", "application", "application").split(",")));
        for (String type : pmsTypeKey) {
            System.out.println(type);
            if (payload.containsKey(type))
                payload.remove(type);

        }

        Payload targetPayload = new Payload();
        for (var entry : payload.entrySet()) {
            System.out.println(entry.getKey() + "/" + entry.getValue());
            if (entry.getValue() instanceof LinkedHashMap)
                 targetPayload.put(entry.getKey(), ((LinkedHashMap<String, Object>) entry.getValue()).get("parameterValue"));
        }

        message.getDynamicPayload().getHeader().put( PMTypeCode, targetPmType);
        message.getDynamicPayload().getHeader().put(serviceCode,message.getHeader().get("apiCode"));

      /*  ArrayList<String> pmsTypeKey = new ArrayList(Arrays.asList(propertyLoader.getPropertyAsString("pmsTypeKey=", "application", "application").split(",")));
        for (String type : pmsTypeKey) {
            System.out.println(type);
            if (targetPayload.containsKey(type))
                targetPayload.remove(type);

        }*/


        String serviceName = propertyLoader.getPropertyAsString(VaildateServiceName);
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setPayload((Payload) targetPayload);
        dynamicPayload.setHeader(message.getDynamicPayload().getHeader());
        message.getPayload().put("integration_serviceName",serviceName);
        message.setDynamicPayload(dynamicPayload);


    }





    private String validateRequest(Message message) throws Exception {

        Boolean isSourcePM = false;
        Boolean isDestinationPM = false;

        String pmTyp = null;
        String senderIdentifyValue = message.getPayload().getAttributeAsString("senderIdentifyValue");
        String sourcePM = message.getPayload().getAttributeAsString("sourcePM");
        String destenationPM = message.getPayload().getAttributeAsString("destenationPM");


        if (senderIdentifyValue == null || senderIdentifyValue.trim().isEmpty()) {
            throw new QuickActionException("senderIdentifyValue  Must be Not Empty",
                    ErrorCode.MISSING_PARAMETER.getErrorCode());
        }

      /*  if ((sourcePM != null && !sourcePM.trim().isEmpty()) && (destenationPM != null && !destenationPM.trim().isEmpty())) {
            throw new QuickActionException("sourcePM or destenationPM Must be Found",
                    ErrorCode.MISSING_PARAMETER.getErrorCode());
        }*/

        if ((sourcePM == null || sourcePM.trim().isEmpty()) && (destenationPM == null || destenationPM.trim().isEmpty())) {


            throw new QuickActionException("sourcePM or destenationPM Must be Found",
                    ErrorCode.MISSING_PARAMETER.getErrorCode());
        }

        if (destenationPM != null && !destenationPM.trim().isEmpty()) {
            isDestinationPM = true;
            pmTyp = destenationPM;
            message.getPayload().put("vaildateMSISDN", false);
        }


        else if (sourcePM != null && !sourcePM.trim().isEmpty()) {


            isSourcePM = true;
            pmTyp = sourcePM;
            message.getPayload().put("vaildateMSISDN", true);


        }


        return pmTyp;

    }

}


