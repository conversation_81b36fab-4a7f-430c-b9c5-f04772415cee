<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cit.vericash.api.gateway</groupId>
    <artifactId>vericash-api-gateway-components</artifactId>
    <version>1.0-SNAPSHOT</version> 
    <packaging>jar</packaging> 

    <properties>
        <java.version>11</java.version>
        <spring-cloud.version>Hoxton.SR8</spring-cloud.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <pdfBox.version>3.0.3</pdfBox.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <artifactId>vericash-query-engine</artifactId>
                <groupId>com.cit.vericash-query-engine</groupId>
                <version>0.0.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.3.4.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web-services</artifactId>
                <version>2.3.4.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.cit.vericash</groupId>
                <artifactId>vericash-dynamic-payload</artifactId>
                <version>1.2.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- https://mvnrepository.com/artifact/log4j/log4j -->
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>${pdfBox.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>fontbox</artifactId>
            <version>${pdfBox.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.56</version>
        </dependency>

        <dependency>
            <groupId>com.cit.vericash.api.gateway</groupId>
            <artifactId>vericash-api-gateway-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.cit.mpaymentapp</groupId>-->
        <!--            <artifactId>rotating-savings-service</artifactId>-->
        <!--            <version>1.0.3-SNAPSHOT</version>-->
        <!--            <scope>compile</scope>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.cit.vericash</groupId>
            <artifactId>Lookup-Framework</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp</groupId>
            <artifactId>mpaymentapp-model</artifactId>
            <version>1.2.1-SNAPSHOT</version>
            <type>ejb</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.picketbox</groupId>
            <artifactId>picketbox</artifactId>
            <version>5.1.0.Final</version>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp</groupId>
            <artifactId>mpaymentapp-common</artifactId>
            <version>1.2.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp</groupId>
            <artifactId>mpaymentapp-security</artifactId>
            <version>1.2.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.cit.shared</groupId>
            <artifactId>ErrorHandler</artifactId>
            <version>1.2.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>javax.jms</groupId>-->
        <!--            <artifactId>jms-api</artifactId>-->
        <!--            <version>1.1-rev-1</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>
        <!--   <dependency>
               <groupId>com.google.code.gson</groupId>
               <artifactId>gson</artifactId>
               <version>2.8.7</version>
           </dependency>-->
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20090211</version>
        </dependency>

        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.7.0</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.mockito/mockito-core -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.mockito/mockito-junit-jupiter -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.leadpony.justify</groupId>
            <artifactId>justify-parent</artifactId>
            <version>2.1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>jakarta.json</artifactId>
            <version>1.1.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.json</groupId>
            <artifactId>jakarta.json-api</artifactId>
            <version>1.1.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.json.bind</groupId>
            <artifactId>jakarta.json.bind-api</artifactId>
            <version>1.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>19.6.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.mastercard.api</groupId>
            <artifactId>sdk-api-core</artifactId>
            <version>1.4.31</version>
            <scope>compile</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.apache.activemq</groupId>-->
        <!--            <artifactId>activemq-all</artifactId>-->
        <!--            <version>5.9.0</version>-->
        <!--            <scope>provided</scope>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>activemq-spring</artifactId>
            <version>5.14.3</version>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp</groupId>
            <artifactId>vericash-service-commons</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-jxpath</groupId>
            <artifactId>commons-jxpath</artifactId>
            <version>1.3</version>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp</groupId>
            <artifactId>vericash-commons</artifactId>
            <version>1.2.1-SNAPSHOT</version>
         </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp.model</groupId>
            <artifactId>input-parameters-entity-model</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.11</version>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp.vericash.inbox.messaging</groupId>
            <artifactId>vericash-inbox-common</artifactId>
            <version>1.0.0.0</version>
            <type>ejb</type>
            <scope>compile</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.cit.mpaymentapp</groupId>-->
        <!--            <artifactId>vericash-service-commons</artifactId>-->
        <!--            <version>1.0.3-SNAPSHOT</version>-->
        <!--            <scope>compile</scope>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.cit.mpaymentapp.vericash.self.services</groupId>
            <artifactId>vericash-self-services-common</artifactId>
            <version>1.0.0.0</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp.vericash.seatgate.ticketing</groupId>
            <artifactId>vericash-seatgate-ticketing-common</artifactId>
            <version>1.0.0.0</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp.vericash.face.recognition</groupId>
            <artifactId>vericash-face-recognition-common</artifactId>
            <version>1.0.0.0</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp.vericash.international.topup</groupId>
            <artifactId>vericash-international-topup-common</artifactId>
            <version>1.0.0.0</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp.vericash.revPay</groupId>
            <artifactId>vericash-revPay-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.picketbox</groupId>
            <artifactId>picketbox</artifactId>
            <version>5.1.0.Final</version>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp.myBankStatement</groupId>
            <artifactId>myBankStatement-common</artifactId>
            <version>1.0.0.0</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp</groupId>
            <artifactId>mpaymentapp-counters</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.4</version>
        </dependency>
        <dependency>
            <groupId>com.bazaarvoice.jolt</groupId>
            <artifactId>jolt-complete</artifactId>
            <version>0.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-oxm</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-core</artifactId>
    </dependency>

        <dependency>
            <groupId>org.jboss.jbossas</groupId>
            <artifactId>jboss-as-security</artifactId>
            <version>6.1.0.Final</version>
        </dependency>

        <!-- Servlet -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <version>2.5</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.2.132</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-jexl3</artifactId>
            <version>3.0</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.cit.vericash</groupId>-->
        <!--            <artifactId>Lookup-Framework</artifactId>-->
        <!--            <version>0.0.1-SNAPSHOT</version>-->
        <!--            <scope>compile</scope>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.4.0-b180725.0427</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>2.4.0-b180725.0644</version>
        </dependency>
        <!--<dependency>
            <groupId>jakarta.jws</groupId>
            <artifactId>jakarta.jws-api</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.soap</groupId>
            <artifactId>jakarta.xml.soap-api</artifactId>
            <version>1.4.2</version>
        </dependency>-->
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>3.0.0</version>
            <!--   <scope>runtime</scope> -->
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-ri</artifactId>
            <version>2.3.3</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.cit.vericash.jetty</groupId>
            <artifactId>jetty-http-client</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <!--        <dependency>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </dependency>-->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
<dependency>
            <groupId>com.cit.mpaymentapp.model</groupId>
            <artifactId>input-parameters-entity-model</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
<dependency>
            <groupId>com.cit.mpaymentapp.model</groupId>
            <artifactId>input-parameters-entity-model</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>


   <!--        <dependency>-->
        <!--            <groupId>javax.jms</groupId>-->
        <!--            <artifactId>javax.jms-api</artifactId>-->
        <!--            <version>2.0.1</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.cit.vericash.api.gateway</groupId>
            <artifactId>vericash-api-gateway-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cit.mpaymentapp</groupId>
            <artifactId>vericash-service-commons</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>8.13.4</version>
        </dependency>
        <dependency>
            <groupId>com.cit.vericash</groupId>
            <artifactId>vericash-dynamic-payload</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.6.0</version> <!-- or the latest version -->
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>0.8.12</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <!--  <plugin>
                  <groupId>org.springframework.boot</groupId>
                  <artifactId>spring-boot-maven-plugin</artifactId>
                  <configuration>
                      <classifier>exec</classifier>
                  </configuration>
              </plugin>
              <plugin>
                  <groupId>org.jvnet.jaxb2.maven2</groupId>
                  <artifactId>maven-jaxb2-plugin</artifactId>
                  <version>0.12.3</version>
                  <executions>
                      <execution>
                          <goals>
                              <goal>generate</goal>
                          </goals>
                      </execution>
                  </executions>
              </plugin>-->

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${jacoco.outputDirectory}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


            <plugin>
                <groupId>org.jvnet.jaxb2.maven2</groupId>
                <artifactId>maven-jaxb2-plugin</artifactId>
                <version>0.14.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <schemaLanguage>WSDL</schemaLanguage>
                    <generateDirectory>${project.basedir}/src/main/java</generateDirectory>
                    <generatePackage>com.cit.vericash.api.commons.dto.card.validation</generatePackage>
                    <schemaDirectory>${project.basedir}/src/main/resources</schemaDirectory>
                    <schemaIncludes>
                        <include>cvs.WSDL</include>
                    </schemaIncludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0</version> <!-- Use a version that supports JUnit 5 -->
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <release>11</release>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                </configuration>
            </plugin>


            <!-- <plugin>
                 <groupId>org.jvnet.jaxb2.maven2</groupId>
                 <artifactId>maven-jaxb2-plugin</artifactId>
                 <version>0.13.2</version>
                 <executions>
                     <execution>
                         <goals>
                             <goal>generate</goal>
                         </goals>
                     </execution>
                 </executions>
                 <configuration>
                     <generatePackage>com.cit.vericash.api.commons.dto.scheme.card.validation</generatePackage>
                     <generateDirectory>${project.basedir}/src/main/java</generateDirectory>
                     <schemaDirectory>${project.basedir}/src/main/resources/cvs</schemaDirectory>
                     <schemaIncludes>
                         <include>*.wsdl</include>
                     </schemaIncludes>
                 </configuration>
             </plugin>-->
            <!--       <plugin>
                       <groupId>org.codehaus.mojo</groupId>
                       <artifactId>jaxb2-maven-plugin</artifactId>
                       <version>2.3.1</version>
                       <executions>
                           <execution>
                               <id>xjc</id>
                               <goals>
                                   <goal>xjc</goal>
                               </goals>
                           </execution>
                       </executions>
                       <configuration>
                           <packageName>com.cit.vericash.api.commons.dto.scheme.card.validation</packageName>
                           <sourceType>wsdl</sourceType>
                           <sources>
                               <source>src/main/resources/cvs.WSDL</source>
                           </sources>
                           <outputDirectory>target/generated-sources/</outputDirectory>
                           <clearOutputDir>false</clearOutputDir>
                       </configuration>
                   </plugin>-->


        </plugins>
    </build>
</project>
