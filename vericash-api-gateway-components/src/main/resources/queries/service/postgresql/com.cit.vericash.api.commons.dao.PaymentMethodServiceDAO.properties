loadPaymentMethodUsingPaymentAlias=SELECT GARID,PAYMENT_METHOD_TYPE FROM CUSTOMER_GAR WHERE PAYMENT_ALIAS = ?
loadPaymentMethodUsingIsDefaultReciever=SELECT GARID,PAYMENT_METHOD_TYPE FROM CUSTOMER_GAR WHERE CUSTOMER_ID = ? AND IS_DEFAULT_RECIEVER=1
loadPaymentMethodUsingIsDefaultSender=SELECT GARID,PAYMENT_METHOD_TYPE FROM CUSTOMER_GAR WHERE CUSTOMER_ID = ? AND IS_DEFAULT_SENDER=1
loadBEParentCustomerIdForBu=SELECT  PARENT_CUSTOMER_ID from BE_CUSTOMER WHERE BE_CUSTOMER.BE_USER_TYPE_ID = 1  and BE_CUSTOMER.CORPERATE_ID = ?
loadPaymentMethodLoyaltyUsingMsisdn=SELECT GAR.GARID FROM CUSTOMER_GAR GAR LEFT JOIN CUSTOMER CUST ON GAR.CUSTOMER_ID = CUST.CUSTOMER_ID WHERE GAR.STATUS = ? AND CUST.MSISDN = ? AND GAR.PAYMENT_METHOD_TYPE=?