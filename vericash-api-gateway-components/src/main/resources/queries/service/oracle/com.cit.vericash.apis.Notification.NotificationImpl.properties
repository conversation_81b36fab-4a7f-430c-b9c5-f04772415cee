viewAllNotification=SELECT NOTIFICATION_ID,ISREADING,NOTIFICATION_DATE,TITLE,CONTENT,TYPE,EXTRA_FIELDS,REQUEST,IMAGE_URL  \
  FROM MOBILE_NOTIFICATION \
  WHERE CUSTOMER_KEY = '%1$s' AND STATUS = 1 ORDER BY NOTIFICATION_DATE DESC
viewRecentNotification=SELECT NOTIFICATION_ID,ISREADING,NOTIFICATION_DATE,TITLE,CONTENT,TYPE,EXTRA_FIELDS,REQUEST,IMAGE_URL \
  FROM MOBILE_NOTIFICATION \
  WHERE CUSTOMER_KEY = '%1$s' AND STATUS = 1 ORDER BY NOTIFICATION_DATE DESC
deleteNotification=UPDATE MOBILE_NOTIFICATION SET STATUS = 0 WHERE NOTIFICATION_ID = %1$s
readNotification=UPDATE MOBILE_NOTIFICATION SET ISREADING = 1 WHERE NOTIFICATION_ID = %1$s

viewRecentNotificationWithActions=SELECT MN.NOTIFICATION_ID,MN.ISREADING,MN.NOTIFICATION_DATE,MN.TITLE,MN.CONTENT,NA.ACTION,NA.EXPIRY_DATE,mn.EXTRA_FIELDS,mn.TYPE \
FROM MOBILE_NOTIFICATION MN \
INNER JOIN NOTIFICATION_ACTION NA ON (MN.NOTIFICATION_ID= NA.NOTIFICATION_ID) \
WHERE mn.CUSTOMER_KEY = '%1$s' AND MN.STATUS =1 ORDER BY MN.NOTIFICATION_DATE DESC

getNotificationActionById=SELECT na.ACTION, na.EXPIRY_DATE  \
  FROM NOTIFICATION_ACTION na \
  WHERE na.NOTIFICATION_ID ='%1$s'
