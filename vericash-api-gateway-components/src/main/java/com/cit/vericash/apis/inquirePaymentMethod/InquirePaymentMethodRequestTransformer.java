package com.cit.vericash.apis.inquirePaymentMethod;

import com.cit.mpaymentapp.common.paymentmethod.SourcePaymentMethodDTO;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.inquirePaymentMethod.InquirePaymentMethodRequestTransformer")
public class InquirePaymentMethodRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

        Gson gson = new Gson();
        String listofpaymentmethod = gson.toJson(message.getPayload().get("sourcePaymentMethods"));
        final ObjectMapper objectMapper = new ObjectMapper();
        List<SourcePaymentMethodDTO> paymentMethodsList = objectMapper.readValue(listofpaymentmethod, new TypeReference<List<SourcePaymentMethodDTO>>() {
        });
        message.getPayload().put("sourcePaymentMethodsList", paymentMethodsList);


    }


}

