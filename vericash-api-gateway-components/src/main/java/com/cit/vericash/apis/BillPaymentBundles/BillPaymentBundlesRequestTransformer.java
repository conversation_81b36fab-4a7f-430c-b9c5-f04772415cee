package com.cit.vericash.apis.BillPaymentBundles;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.WalletInfo;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.util.CacheableWalletInfo;
import com.cit.vericash.apis.commons.util.MsisdnFormatterImpl;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.backend.commons.dynamicinputparameters.mapping.DynamicPayloadHashMapMapping;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;


@Component
public class BillPaymentBundlesRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private static final String INVALID_MOBILE_NUMBER = "invalidMobileNumberFormatErrorCode";
    private static final PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();

    @Autowired
    DynamicPayloadHashMapMapping dynamicPayloadHashMapMapping;
    @Autowired
    private PropertyLoaderComponent propertyLoaderComponent;
    @Autowired
    private CacheableWalletInfo walletInfoCache;
    @Autowired
    MsisdnFormatterImpl msisdnFormatter;

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
        String mobileNumber = ((Payload) dynamicPayloadHashMapMapping.transform(message.getDynamicPayload())).getAttributeAsString("mobileNumber");
        String walletShortCode = message.getHeader().getAttributeAsString("walletShortCode");
        WalletInfo walletInfo = walletInfoCache.getWalletInfo(walletShortCode);
        String region = walletInfo.getCountryIso2();

        String newMsisdn = msisdnFormatter.formatMSISDN(mobileNumber, region, walletShortCode);
        Map msisdnInputParam = (LinkedHashMap) (message.getDynamicPayload().getPayload().getAttribute("293"));

        if (msisdnInputParam != null && msisdnInputParam.size() > 0)
            msisdnInputParam.put("parameterValue", newMsisdn);

        message.getDynamicPayload().getPayload().put("293", msisdnInputParam);
    }
}
