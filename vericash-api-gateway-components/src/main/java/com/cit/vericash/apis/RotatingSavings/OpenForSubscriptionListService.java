package com.cit.vericash.apis.RotatingSavings;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.commons.dto.OpenForSubscriptionRotatingSavingsDTO;
import com.cit.vericash.api.commons.dto.SimpleGenericDTO;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.OpenForSubscriptionListService")
public class OpenForSubscriptionListService {
    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    ArrayList<OpenForSubscriptionRotatingSavingsDTO> openForSubscription;

    public ArrayList<OpenForSubscriptionRotatingSavingsDTO> getOpenForSubscriptionList(boolean viewAll, Integer customerId) throws Exception {
        openForSubscription = new ArrayList<OpenForSubscriptionRotatingSavingsDTO>();
        String sqlQuery = null;
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<String>();
        fields = setFields(fields);
        Integer rotatingSavingsNum = propertyLoaderComponent.getPropertyAsInteger("rotating.savings.num", "application", "application");
        if (customerId == null) {
            String errorMessage = propertyLoaderComponent.getPropertyAsString("error.rotating.savings.missing.id", "application", "application");
            throw new GeneralFailureException(errorMessage);
        } else if (customerId != null && !viewAll) {
            sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getOpenForSubscriptionList_condition1", this.getClass(), String.valueOf(rotatingSavingsNum));

        } else if (customerId != null && viewAll) {
            sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getOpenForSubscriptionList_condition2", this.getClass());

        }
        List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters, fields);
        Record record = null;
        for (int i = 0; i < records.size(); i++) {
            record = records.get(i);
            OpenForSubscriptionRotatingSavingsDTO openForSubscriptionRotatingSavingsDTOS = buildRotatingSavings(record);
            if (openForSubscriptionRotatingSavingsDTOS != null) {
                openForSubscription.add(openForSubscriptionRotatingSavingsDTOS);
            }
        }
        return openForSubscription;
    }

    private OpenForSubscriptionRotatingSavingsDTO buildRotatingSavings(Record record) {
        OpenForSubscriptionRotatingSavingsDTO openForSubscriptionRotatingSavingsDTO = new OpenForSubscriptionRotatingSavingsDTO();
        SimpleGenericDTO simpleGenericDTO = new SimpleGenericDTO();

        if (record.getValueAsInt("ID") != null)
            openForSubscriptionRotatingSavingsDTO.setId(record.getValueAsInt("ID"));

        if (record.getValueAsDate("SUBSCRIPTION_START_DATE") != null)
            openForSubscriptionRotatingSavingsDTO.setFrom(record.getValueAsDate("SUBSCRIPTION_START_DATE"));
        if (record.getValueAsDate("END_DATE") != null)
            openForSubscriptionRotatingSavingsDTO.setTo(record.getValueAsDate("END_DATE"));
        if (record.getValueAsDate("SUBSCRIPTION_END_DATE") != null)
            openForSubscriptionRotatingSavingsDTO.setSubscriptionEndDate(record.getValueAsDate("SUBSCRIPTION_END_DATE"));
        if (record.getValueAsString("CURRENCY") != null && record.getValueAsInt("TOTAL_AMOUNT") != null)
            openForSubscriptionRotatingSavingsDTO.setTotalAmount(record.getValueAsString("CURRENCY") + " " + record.getValueAsInt("TOTAL_AMOUNT"));


        if (record.getValueAsBigDecimal("CONTRIBUTION_AMOUNT") != null && record.getValueAsString("CURRENCY") != null && record.getValueAsString("RSF_NAME") != null) {
            String contributionAmount = record.getValueAsString("CURRENCY") + " " + record.getValueAsBigDecimal("CONTRIBUTION_AMOUNT") + "/" + " " + record.getValueAsString("RSF_NAME").substring(0, record.getValueAsString("RSF_NAME").toCharArray().length - 2);
            openForSubscriptionRotatingSavingsDTO.setContributionAmount(contributionAmount);
        }
        if (record.getValueAsString("WTD_NAME") != null && record.getValueAsInt("WINNING_TURN_DECISION") != null) {
            Integer winningTurnDecisionOrdinal = record.getValueAsBigDecimal("WINNING_TURN_DECISION").intValue();
            simpleGenericDTO.setName(OpenForSubscriptionRotatingSavingsDTO.WinningTurnDecision.values()[winningTurnDecisionOrdinal].name());
            simpleGenericDTO.setId(winningTurnDecisionOrdinal);
            openForSubscriptionRotatingSavingsDTO.setWinningTurnDecision(simpleGenericDTO);
        }
        return openForSubscriptionRotatingSavingsDTO;
    }

    private List<String> setFields(List<String> fields) {
        fields.add("CURRENCY");
        fields.add("ID");
        fields.add("TOTAL_AMOUNT");
        fields.add("SUBSCRIPTION_START_DATE");
        fields.add("SUBSCRIPTION_END_DATE");
        fields.add("END_DATE");
        fields.add("CONTRIBUTION_AMOUNT");
        fields.add("CONTRIBUTION_FREQUENCY");
        fields.add("WTD_NAME");
        fields.add("RSF_NAME");
        fields.add("WINNING_TURN_DECISION");
        return fields;
    }
}
