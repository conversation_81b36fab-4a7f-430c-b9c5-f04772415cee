package com.cit.vericash.apis.salarydisbursement;

import com.cit.vericash.apis.calculatefee.GroupCalculateFeesRequestTransformer;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.dto.request.Request;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import static com.cit.vericash.apis.calculatefee.GroupCalculateFeesRequestTransformer.SERVICE_TYPE;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.RetrySalaryTransactionsRequestTransformer")
@RequiredArgsConstructor
@Slf4j
public class RetrySalaryTransactionsRequestTransformer implements VericashAction {
    public static final String SALARY_API_CODE = "salaryApiCode";
    private final GroupCalculateFeesRequestTransformer groupCalculateFeesRequestTransformer;
    private final PropertyLoaderComponent propertyLoaderComponent;
    @Override
    public Object process(Request request) throws Exception {
        log.info("Inside SalaryTransactionsRequestTransformer");
        request.getMessage().getPayload().put(SERVICE_TYPE, propertyLoaderComponent.getPropertyAsString(SALARY_API_CODE));
        groupCalculateFeesRequestTransformer.process(request);
        return request;
    }
}