package com.cit.vericash.apis.grantsuperuser;

import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.dto.response.Response;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.grantsuperuser.GrantSuperUserRequestTransformer")
public class GrantSuperUserRequestTransformer implements VericashAction {
    @Override
    public Object process(Request request) throws Exception {
        Response response = null;

        try {

        } catch (Exception e) {

        }
        return null;
    }
}
