package com.cit.vericash.apis.getministatement;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ExternalTransactionInformation;
import com.cit.mpaymentapp.common.message.TransactionReceiptInformation;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.model.MiniStatement;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.getministatement.GetMiniStatementResponseTransformer")
public class GetMiniStatementResponseTransformer implements MuleResponseTransformer {
    private final String miniStatementKey = "mini_statement";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();

        Map<String, String> result = new HashMap<String, String>();

        try {
            Gson gson = new Gson();
            List<MiniStatement> trInformationList = new ArrayList<MiniStatement>();
            List<ExternalTransactionInformation> externalTransactionInformations = businessMessage.getExternalTransactionInformations();

            if (!externalTransactionInformations.isEmpty() && externalTransactionInformations != null) {
                for (ExternalTransactionInformation externalTransactionInformation : externalTransactionInformations) {
                    TransactionReceiptInformation transactionInfo = (TransactionReceiptInformation) externalTransactionInformation;

                    MiniStatement miniStatement = new MiniStatement();

                    miniStatement.setTransactionId(transactionInfo.getTranId());
                    miniStatement.setTransactionDate(transactionInfo.getTranDate());
                    miniStatement.setAmount(transactionInfo.getTransactionTotalAmount());
                    miniStatement.setFrom(transactionInfo.getSenderAccountNumber());
                    miniStatement.setReceiver(transactionInfo.getReceiverAccountNumber());
                    miniStatement.setTransactionType(transactionInfo.getTranSubtype());
                    miniStatement.setTransferType(transactionInfo.getTransferOption());
                    miniStatement.setOwnerType(transactionInfo.getSenderName());
                    //  you could get it from transactionInfo.setNarration .
                    // miniStatement.setComments("");

                    if (transactionInfo.getFeeAmount() != null) {
                        miniStatement.setFee(transactionInfo.getFeeAmount().toString());
                    }
                    trInformationList.add(miniStatement);
                    //result.put("Comments if found","transactionInfo");
                }//setTransferOption
                result.put(cacheableFieldParameters.getResponseParameters(miniStatementKey), gson.toJson(trInformationList));
            }
//			else {
//				result.put(cacheableFieldParameters.getResponseParameters(miniStatementKey), "No Transaction History For This Period");
//			}

        } catch (Exception e) {
            throw e;
        }

        return result;
    }
}