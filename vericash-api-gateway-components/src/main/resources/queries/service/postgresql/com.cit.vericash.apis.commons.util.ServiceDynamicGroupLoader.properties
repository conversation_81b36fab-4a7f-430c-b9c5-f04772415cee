loadDynamicServiceGroup=select  GD.GROUP_CODE,SD.SERVICE_TYPE_ID,GD.ID,GT.NAME \"GroupType\", GT.ID \"GroupTypeId\",GD.NAME,GD.IS_CONTAINER \
  ,VT.VALIDATION_TYPE ,VT.ID \"ValidationTypeId\",GV.CUSTOM_VALIDATION_BEAN \
  FROM  INPUT_PARAM_GROUP_DEF  GD \
  JOIN INPUT_PARAM_GROUP_WALLET  GW ON GW.GROUP_DEF_ID = GD.ID \
  JOIN INPUT_PARAM_SERVICE_GROUP SG ON SG.GROUP_WALLET_ID = GW.ID \
  JOIN INPUT_PARAM_SERVICE_DEF SD ON SD.ID = SG.SERVICE_DEF_ID \
  JOIN INPUT_PARAM_GROUP_VALIDATION GV ON SG.ID =  GV.INPUT_PARAM_SERVICE_GROUP_ID \
  JOIN INPUT_PARAM_GROUP_TYPE GT ON GT.ID = GD.GROUP_TYPE_ID \
  JOIN INPUT_PARAM_GROUP_VAL_TYPE VT ON VT.ID = GV.GROUP_VALIDATION_TYPE \
  WHERE SD.SERVICE_TYPE_ID = ? 
ifCondition= AND SD.WALLET_SHORT_CODE = ? AND GW.WALLET_SHORT_CODE = ? 
else_1= AND SD.WALLET_SHORT_CODE is null AND GW.WALLET_SHORT_CODE is null
