<!-- Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is 
	JAX-WS RI 2.2.6b21 svn-revision#12959. -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           version="1.0" targetNamespace="http://ws.entrustplugin.expertedge.com/">
    <xs:complexType name="tokenAuthDTO">
        <xs:sequence>
            <xs:element name="appCode" type="xs:string" minOccurs="0"/>
            <xs:element name="appDesc" type="xs:string" minOccurs="0"/>
            <xs:element name="group" type="xs:string" minOccurs="0"/>
            <xs:element name="tokenPin" type="xs:string" minOccurs="0"/>
            <xs:element name="userName" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="authResponseDTO">
        <xs:sequence>
            <xs:element name="authenticationSuccessful" type="xs:boolean"/>
            <xs:element name="respCode" type="xs:int" minOccurs="0"/>
            <xs:element name="respMessage" type="xs:string" minOccurs="0"/>
            <xs:element name="respMessageCode" type="xs:string"
                        minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>