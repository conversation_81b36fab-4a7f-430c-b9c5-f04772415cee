listOfTransaction_if=SELECT DISTINCT SCHEDULE_RECURRING.ID,SCHEDULE_RECURRING.NAME,SCHEDULER_TYPE.NAME as TYPE,SCHEDULE_RECURRING.END_TIME,SCHEDULE_RECURRING.REPEAT_COUNT,SCHEDULE_RECURRING.START_TIME,\
                      SCHEDULER_RECURRING_PATTERN.NAME as FREQUENCY,SCHEDULER_STATUS.NAME as STATUS,SCHEDULE_RECURRING.LAST_OCCURRENCE_STATUS,SCHEDULE_RECURRING_OCCURRENCE.DETAILS FROM SCHEDULE_RECURRING  \
                      INNER JOIN  SCHEDULER_TYPE ON SCHEDULE_RECURRING.TYPE=SCHEDULER_TYPE.ID  \
                      INNER JOIN  SCHEDULER_RECURRING_PATTERN ON SCHEDULE_RECURRING.RECURRING_PATTERN=SCHEDULER_RECURRING_PATTERN.ID  \
                      INNER JOIN  SCHEDULER_STATUS ON SCHEDULE_RECURRING.STATUS=SCHEDULER_STATUS.ID  \
                      INNER JOIN SCHEDULE_RECURRING_OCCURRENCE ON SCHEDULE_RECURRING_OCCURRENCE.SCHEDULER_ID = SCHEDULE_RECURRING.ID    \
                      WHERE CUSTOMER_ID= %1$s

listOfTransaction_else=SELECT DISTINCT SCHEDULE_RECURRING.ID,SCHEDULE_RECURRING.NAME,SCHEDULER_TYPE.NAME as TYPE,SCHEDULE_RECURRING.END_TIME,SCHEDULE_RECURRING.REPEAT_COUNT,SCHEDULE_RECURRING.START_TIME,  \
  SCHEDULER_RECURRING_PATTERN.NAME as FREQUENCY,SCHEDULER_STATUS.NAME as STATUS,SCHEDULE_RECURRING.LAST_OCCURRENCE_STATUS,SCHEDULE_RECURRING_OCCURRENCE.DETAILS  FROM SCHEDULE_RECURRING  \
  INNER JOIN  SCHEDULER_TYPE ON SCHEDULE_RECURRING.TYPE=SCHEDULER_TYPE.ID  \
  INNER JOIN  SCHEDULER_RECURRING_PATTERN ON SCHEDULE_RECURRING.RECURRING_PATTERN=SCHEDULER_RECURRING_PATTERN.ID  \
  INNER JOIN  SCHEDULER_STATUS ON SCHEDULE_RECURRING.STATUS=SCHEDULER_STATUS.ID  \
  INNER JOIN SCHEDULE_RECURRING_OCCURRENCE ON SCHEDULE_RECURRING_OCCURRENCE.SCHEDULER_ID = SCHEDULE_RECURRING.ID  \
  WHERE CUSTOMER_ID='%1$s' \
  AND START_TIME >= TO_DATE('%2$s 02:00:00','YYYY-MM-DD HH24:MI:SS') AND START_TIME <= TO_DATE('%3$s 02:00:00','YYYY-MM-DD HH24:MI:SS') ORDER BY ID DESC

listOfTransaction_elseif=SELECT DISTINCT SCHEDULE_RECURRING.ID,SCHEDULE_RECURRING.NAME,SCHEDULER_TYPE.NAME as TYPE,SCHEDULE_RECURRING.END_TIME,SCHEDULE_RECURRING.REPEAT_COUNT,SCHEDULE_RECURRING.START_TIME,  \
  SCHEDULER_RECURRING_PATTERN.NAME as FREQUENCY,SCHEDULER_STATUS.NAME as STATUS,SCHEDULE_RECURRING.LAST_OCCURRENCE_STATUS,SCHEDULE_RECURRING_OCCURRENCE.DETAILS  FROM SCHEDULE_RECURRING  \
  INNER JOIN  SCHEDULER_TYPE ON SCHEDULE_RECURRING.TYPE=SCHEDULER_TYPE.ID  \
  INNER JOIN  SCHEDULER_RECURRING_PATTERN ON SCHEDULE_RECURRING.RECURRING_PATTERN=SCHEDULER_RECURRING_PATTERN.ID  \
  INNER JOIN  SCHEDULER_STATUS ON SCHEDULE_RECURRING.STATUS=SCHEDULER_STATUS.ID  \
  INNER JOIN SCHEDULE_RECURRING_OCCURRENCE ON SCHEDULE_RECURRING_OCCURRENCE.SCHEDULER_ID = SCHEDULE_RECURRING.ID  \
  WHERE CUSTOMER_ID='%1$s' \
  AND START_TIME >= TO_DATE('%2$s 02:00:00','YYYY-MM-DD HH24:MI:SS')

deleteRecurring_query=UPDATE SCHEDULE_RECURRING SET STATUS=1 WHERE ID = %1$s

deleteRecurring_queryOccur=UPDATE SCHEDULE_RECURRING_OCCURRENCE SET STATUS=3 WHERE SCHEDULER_ID = %1$s AND STATUS=0

cancelRecurring=UPDATE SCHEDULE_RECURRING_OCCURRENCE SET STATUS = 3 WHERE ID =  %1$s

hasScheduledStatus=SELECT ID,STATUS FROM SCHEDULE_RECURRING_OCCURRENCE where ID= %1$s and STATUS= 0

hasNexOccurrenceQuery = SELECT CASE \
  WHEN s.RECURRING_PATTERN = 1 AND TRUNC(s.END_TIME, 'DD') > TRUNC(sc.DATE_TIME, 'DD') THEN 1 \
  WHEN s.RECURRING_PATTERN = 2 AND TRUNC(s.END_TIME, 'DD') >= TRUNC(sc.DATE_TIME + INTERVAL '7' DAY, 'DD') THEN 1 \
  WHEN s.RECURRING_PATTERN = 3 AND TRUNC(s.END_TIME, 'DD') >= TRUNC(ADD_MONTHS(sc.DATE_TIME, 1), 'DD') THEN 1 \
  WHEN s.RECURRING_PATTERN = 4 AND TRUNC(s.END_TIME, 'DD') >= TRUNC(ADD_MONTHS(sc.DATE_TIME, 3), 'DD') THEN 1 \
  WHEN s.RECURRING_PATTERN = 5 AND TRUNC(s.END_TIME, 'DD') >= TRUNC(ADD_MONTHS(sc.DATE_TIME, 12), 'DD') THEN 1 \
  ELSE 0 \ END AS has_next_occurrence\ FROM SCHEDULE_RECURRING_OCCURRENCE sc \
  JOIN SCHEDULER s \
  ON sc.SCHEDULER_ID = s.id \
  WHERE sc.ID= %1$s AND sc.STATUS = 0