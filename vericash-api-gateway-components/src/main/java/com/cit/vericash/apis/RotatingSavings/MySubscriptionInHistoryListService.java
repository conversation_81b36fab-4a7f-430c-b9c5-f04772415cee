package com.cit.vericash.apis.RotatingSavings;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.commons.dto.MySubscriptionInRotatingSavingsDTO;
import com.cit.vericash.api.commons.dto.SimpleGenericDTO;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.MySubscriptionInHistoryListService")
public class MySubscriptionInHistoryListService {
    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    @Autowired
    MySubscriptionsInAllowedOptionsService mySubscriptionsInAllowedOptionsService;

    ArrayList<MySubscriptionInRotatingSavingsDTO> mySubscriptionInRotatingSavingsDTOArrayList;
    Integer status;
    Integer memberStatus;
    Date subscriptionStartDate;
    Date subscriptionEndDate;

    public ArrayList<MySubscriptionInRotatingSavingsDTO> getMySubscriptionInList(Integer customerId) throws Exception {
        mySubscriptionInRotatingSavingsDTOArrayList = new ArrayList<MySubscriptionInRotatingSavingsDTO>();
        String sqlQuery = null;
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<String>();

        fields = setFields(fields);
        if (customerId == null) {
            String errorMessage = propertyLoaderComponent.getPropertyAsString("error.rotating.savings.missing.id", "application", "application");
            throw new GeneralFailureException(errorMessage);
        }
        if (customerId != null) {
            sqlQuery = ServiceQueryEngine.getQueryStringToExecute("MySubscriptionInHistoryListService", this.getClass(), String.valueOf(customerId));

        }
        List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters, fields);
        Record record = null;
        for (int i = 0; i < records.size(); i++) {
            record = records.get(i);
            MySubscriptionInRotatingSavingsDTO mySubscriptionInRotatingSavingsDTO = buildRotatingSavings(record);
            if (mySubscriptionInRotatingSavingsDTO != null) {
                mySubscriptionInRotatingSavingsDTOArrayList.add(mySubscriptionInRotatingSavingsDTO);
            }
        }
        return mySubscriptionInRotatingSavingsDTOArrayList;
    }

    private MySubscriptionInRotatingSavingsDTO buildRotatingSavings(Record record) throws Exception {
        MySubscriptionInRotatingSavingsDTO mySubscriptionInRotatingSavingsDTO = new MySubscriptionInRotatingSavingsDTO();
        SimpleGenericDTO rotatingSavingsSimpleGenericDTO = new SimpleGenericDTO();
        SimpleGenericDTO memberStatusSimpleGenericDTO = new SimpleGenericDTO();
        if (record.getValueAsBigDecimal("ID") != null)
            mySubscriptionInRotatingSavingsDTO.setId(record.getValueAsBigDecimal("ID").intValue());
        if (record.getValueAsString("CURRENCY") != null && record.getValueAsInt("TOTAL_AMOUNT") != null)
            mySubscriptionInRotatingSavingsDTO.setTotalAmount(record.getValueAsString("CURRENCY") + " " + record.getValueAsInt("TOTAL_AMOUNT"));

        if (record.getValueAsDate("SUBSCRIPTION_START_DATE") != null) {
            subscriptionStartDate = record.getValueAsDate("SUBSCRIPTION_START_DATE");
            mySubscriptionInRotatingSavingsDTO.setFrom(subscriptionStartDate);
        }
        if (record.getValueAsDate("SUBSCRIPTION_END_DATE") != null) {
            subscriptionEndDate = record.getValueAsDate("SUBSCRIPTION_END_DATE");
            mySubscriptionInRotatingSavingsDTO.setTo(subscriptionEndDate);
        }

        if (record.getValueAsBigDecimal("TURN_NUMBER") != null)
            mySubscriptionInRotatingSavingsDTO.setMyTurn(record.getValueAsBigDecimal("TURN_NUMBER").intValue());

        if (record.getValueAsBigDecimal("CONTRIBUTION_AMOUNT") != null && record.getValueAsString("CURRENCY") != null && record.getValueAsString("RSF_NAME") != null) {
            String contributionAmount = record.getValueAsString("CURRENCY") + " " + record.getValueAsBigDecimal("CONTRIBUTION_AMOUNT") + "/" + " " + record.getValueAsString("RSF_NAME").substring(0, record.getValueAsString("RSF_NAME").toCharArray().length - 2);
            mySubscriptionInRotatingSavingsDTO.setContributionAmount(contributionAmount);
        }
        if (record.getValueAsBigDecimal("STATUS") != null) {
            status = record.getValueAsBigDecimal("STATUS").intValue();
            rotatingSavingsSimpleGenericDTO.setName(MySubscriptionInRotatingSavingsDTO.RotatingSavingStatus.values()[status].name());
            rotatingSavingsSimpleGenericDTO.setId(status);
            mySubscriptionInRotatingSavingsDTO.setRotatingSavingStatus(rotatingSavingsSimpleGenericDTO);
        }
        if (record.getValueAsBigDecimal("MEM_STATUS") != null) {
            memberStatus = record.getValueAsBigDecimal("MEM_STATUS").intValue();
            memberStatusSimpleGenericDTO.setName(MySubscriptionInRotatingSavingsDTO.RotatingSavingMemStatus.values()[memberStatus].name());
            memberStatusSimpleGenericDTO.setId(memberStatus);
            mySubscriptionInRotatingSavingsDTO.setRotatingSavingMemStatus(memberStatusSimpleGenericDTO);
        }
        List<SimpleGenericDTO> allowedOptionsList = mySubscriptionsInAllowedOptionsService.getAllowedOptionsList(status, memberStatus, subscriptionStartDate, subscriptionEndDate);
        mySubscriptionInRotatingSavingsDTO.setAllowedOptions(allowedOptionsList);
        return mySubscriptionInRotatingSavingsDTO;
    }


    private List<String> setFields(List<String> fields) {
        fields.add("ID");
        fields.add("STATUS");
        fields.add("TOTAL_AMOUNT");
        fields.add("CURRENCY");
        fields.add("SUBSCRIPTION_START_DATE");
        fields.add("SUBSCRIPTION_END_DATE");
        fields.add("CONTRIBUTION_AMOUNT");
        fields.add("CONTRIBUTION_FREQUENCY");
        fields.add("RSF_NAME");
        fields.add("TURN_NUMBER");
        fields.add("MEM_STATUS");
        return fields;
    }
}
