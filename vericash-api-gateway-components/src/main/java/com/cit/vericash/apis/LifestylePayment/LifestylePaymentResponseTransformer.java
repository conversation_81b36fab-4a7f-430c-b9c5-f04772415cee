package com.cit.vericash.apis.LifestylePayment;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.LifestylePayment.LifestylePaymentResponseTransformer")
public class LifestylePaymentResponseTransformer implements MuleResponseTransformer {

    @Autowired
    private CachableVericashApi vericashApiCache;

    public Object transform(BusinessMessage businessMessage) throws Exception {
        TransactionInformation transactionInfo = businessMessage.getTransactionInfo();
        Map<String, Object> softFields = businessMessage.getSoftFields();
        Map<String, Object> TRANS_EXEC_SUMMARY = (Map<String, Object>) softFields.get("TRANS_EXEC_SUMMARY");
        String refNo = String.valueOf(TRANS_EXEC_SUMMARY.get("transactionExecutionSummaryID"));
        Map<String,String> result = new HashMap<String,String>();

        result.put("amount", String.valueOf(transactionInfo.getTransactionAmount()));
        result.put("fee", String.valueOf(transactionInfo.getTransactionFeeAmount()));
        result.put("refNo", refNo);

        return result;
    }

}
