ViewQueryForAllServicesForSmeCustomer_query=SELECT PM.NAME AS \"paymentMethodName\", SPM.ID \"paymentMethodType\", PMT.NAME \"paymentMethodTypeName\", CG.GARID,\
  SPMS.API_CODE , SPMS.USER_TYPE ,SPMS.VAILDATE_PM,PM.PAYMETN_METHOD_ORDER \
  FROM CUSTOMER C \
  JOIN CUSTOMER_GAR CG ON C.CUSTOMER_ID = CG.CUSTOMER_ID JOIN PAYMENT_METHOD PM ON PM.ID = CG.PAYMENT_METHOD_TYPE \
  JOIN SOURCE_PAYMENT_METHOD SPM ON SPM.ID = PM.ID LEFT JOIN PAYMENT_METHOD_TYPE PMT ON PMT.ID =  PM.TYPE \
  LEFT JOIN BE_ROLE_PAYMENT_METHODS BEPMS  ON PMT.ID = BEPMS.PAYMENT_METHOD_TYPE \
  left join SERVICES_PAYMENTMETHOD  SPMS on  SPM.ID = SPMS.PAYMENMETHODTYPE    and SPMS.GAR_STATUS IS NOT null and \
  CG.STATUS in  SPMS.GAR_STATUS \
  LEFT JOIN BUSINESS_ENTITY_HIERARCHY BEH ON BEH.BUSINESS_ENTITY_ID = PM.BUSINESS_ENTITY_ID \
  WHERE  C.CUSTOMER_ID = ( select  BE.MAIN_CUSTOMER_ID   From  BE_CUSTOMER  BE  LEFT JOIN CUSTOMER C \
  ON C.BE_CUSTOMER_ID = BE.ID   where BE.ROLE IN  (select BEPMS.ROLE_ID  From    BE_ROLE_PAYMENT_METHODS BEPMS     \
  LEFT JOIN BE_CUSTOMER BE ON BEPMS.ROLE_ID = BE. ROLE \
  Left join  BE_ROLE   BER   ON  BER.ID=  BE.ROLE  AND   BER.PAYMENT_METHOD_ACCESS =1 \
  LEFT JOIN PAYMENT_METHOD_TYPE PMT ON PMT.ID = BEPMS.PAYMENT_METHOD_TYPE  ) \
  \ AND C.USER_ID = ?  ) \
  AND   SPMS.LOOKUP_ENUM = ?  AND  SPMS.USER_TYPE = ?
ViewQueryForAllServicesForSmeCustomer_if_apiCode=AND   SPMS.API_CODE = ?
ViewQueryForAllServicesForSmeCustomer_elseif_apiCode=AND   SPMS.API_CODE in ( 
ViewQueryForAllServicesForSmeCustomer_forLoop_1=? , 
ViewQueryForAllServicesForSmeCustomer=? ) 



