package com.cit.vericash.dynamicrequest.transformer;

import com.cit.vericash.api.commons.dao.PaymentMethodServiceDAO;
import com.cit.vericash.api.components.impl.RequestTransformerFactoryImpl;
import com.cit.vericash.api.transformerfactory.RequestTransformer;
import com.cit.vericash.backend.commons.dynamicmodelmapper.RequestType;
import com.cit.vericash.backend.commons.dynamicinputparameters.DynamicRequestModelMapperInitializerImpl;
import com.cit.vericash.backend.commons.dynamicmodelmapper.*;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.*;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicmodelmapper.MapWrapper;
import com.cit.vericash.backend.commons.dynamicpayload.*;
import org.apache.commons.jxpath.JXPathContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import com.cit.vericash.backend.commons.dynamicinputparameters.DynamicRequestModelMapperInitializerImpl;
import com.cit.vericash.backend.commons.dynamicmodelmapper.DynamicModelMapper;
import com.cit.vericash.backend.commons.dynamicmodelmapper.Field;
import com.cit.vericash.backend.commons.dynamicmodelmapper.MapWrapper;
import com.cit.vericash.backend.commons.dynamicmodelmapper.ObjectSchema;
import com.cit.vericash.backend.commons.dynamicmodelmapper.*;


@Component
public class DynamicMessageToMessageTransformerImpl implements RequestTransformer {
    private final DynamicRequestModelMapperInitializerImpl dynamicRequestModelMapperInitializer;

    @Autowired
    private CachableVericashApi vericashApiCache;

    @Autowired
    PaymentMethodServiceDAO paymentMethodServiceDAO;

    @Autowired
    PaymentMethodDynamicGroupTransformer paymentMethodDynamicGroupTransformer;


    @Autowired
    RequestTransformerFactoryImpl requestTransformerFactory;


    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;


    @Autowired
    HashMapMessageToMessageTransformerImpl hashMapMessageToMessageTransformer;

    public DynamicMessageToMessageTransformerImpl() {

        this.dynamicRequestModelMapperInitializer = new DynamicRequestModelMapperInitializerImpl();
    }

    @Override
    public Message transform(Message message) throws Exception {

        // call transformer of Dynamic Payload
        hashMapMessageToMessageTransformer.transform(message);


        Message targetMessage = null;
        //      message = loadCustomApiParamters(message);

        String apiCode = message.getHeader().getAttributeAsString("apiCode");
        VericashAPI vericashAPI = vericashApiCache.getVericashAPI(Long.valueOf(apiCode));
        RequestType requestType = vericashAPI.getRequestType();
        DynamicModelMapper modelMapper = dynamicRequestModelMapperInitializer.getModelMapper(apiCode,requestType);
        if (modelMapper == null)
            return message;
        List<Field> fields = modelMapper.getFields();
        List<ObjectSchema> objectSchemas = modelMapper.getObjectSchemas();
//        List<MapWrapper> maps = modelMapper.getMaps();
//        Dto dto = modelMapper.getDto();

        String dtoClassPath = vericashAPI.getDtoClassPath();
        targetMessage = new Message();
        if (message.getDynamicPayload()!=null){
            targetMessage.setDynamicPayload(message.getDynamicPayload());
        }
//        if(dto != null){
        if (dtoClassPath != null) {
            ObjectSchema objectSchema = new ObjectSchema();
            String[] classPathSplited = dtoClassPath.split("[.]", 0);
            String string = classPathSplited[classPathSplited.length - 1];
            string = Character.toLowerCase(string.charAt(0)) + string.substring(1);
            targetMessage.getPayload().put("dtoName", string);
            objectSchema.setObject(string);
            objectSchema.setType("1");
            objectSchemas.add(objectSchema);
        }
        if (objectSchemas != null) {
            initObjects(objectSchemas, targetMessage);
        }


        if (fields != null) {
            List<Map<String, Object>> originalDynamicGroup = (List<Map<String, Object>>) message.getPayload().get("dynamicGroup");

            Map<String, Object> modifiedInputParameters = modifyDynamicGroup(originalDynamicGroup);
            message.getPayload().put("dynamicGroup", new HashMap<>());
            ((HashMap<String, Object>) message.getPayload().get("dynamicGroup")).put("inputParameters", modifiedInputParameters);
            List<Map<String, Object>> tmpDynamicGroup = originalDynamicGroup;
            JXPathContext sourceObject = JXPathContext.newContext(message);
            targetMessage.getPayload().put("classPath", dtoClassPath);
            mapValuesToObjects(modelMapper, sourceObject, targetMessage);
            if (dtoClassPath != null) {
                String adapterSourcePaymentMethod = propertyLoaderComponent.getPropertyAsString("adapterSourcePaymentMethod", "application", "application");
                String adapterDestinationPaymentMethod = propertyLoaderComponent.getPropertyAsString("adapterDestinationPaymentMethod", "application", "application");
                String[] paymentMethodParametersCodes = {adapterDestinationPaymentMethod, adapterSourcePaymentMethod};
                for (String code :
                        paymentMethodParametersCodes) {
                    String type;
                    if (code .equals(adapterSourcePaymentMethod))
                        type = "source";
                    else
                        type = "destination";
                    HashMap<String, Object> source = (HashMap<String, Object>) modifiedInputParameters.get(code);
                    if (source == null)
                        continue;
                    HashMap<String, Object> values = (HashMap<String, Object>) source.get("parameterValue");
                    Map<String, Object> paymentMethod = new HashMap<>();
                    if (values.get("paymentMethodCode") != null) {
                        int paymentMethodCode = (int) values.get("paymentMethodCode");
                        paymentMethod.put("paymentMethodCode", paymentMethodCode);
                    }
                    if (values.get("paymentMethodName") != null) {
                        String paymentMethodName = (String) values.get("paymentMethodName");
                        paymentMethod.put("paymentMethodName", paymentMethodName);
                    }
                    if (values.get("paymentMethodType") != null) {
                        String paymentMethodType = (String) values.get("paymentMethodType");
                        paymentMethod.put("paymentMethodType", paymentMethodType);
                    }
                    targetMessage.getPayload().put(type + "PaymentMethod", paymentMethod);

                }
            }
            targetMessage.getPayload().put("tmpDynamicGroup", tmpDynamicGroup);
            paymentMethodDynamicGroupTransformer.transform(targetMessage, originalDynamicGroup);
        }

        return targetMessage;
    }

    private void initObjects(List<ObjectSchema> objectSchemas, Message targetMessage) {
        for (ObjectSchema objectSchema : objectSchemas) {
            String objectPath = objectSchema.getObject();
            String type = objectSchema.getType();
            checkTargetObjAndInit(objectPath, targetMessage, DynamicObjectTypeEnum.getObjectType(type));
        }
    }

    @Override
    public void mapValuesToObjects(DynamicModelMapper modelMapper, JXPathContext sourceObject, Message targetMessage) {
        List<Field> mapperFields = modelMapper.getFields();
        List<MapWrapper> maps = modelMapper.getMaps();
        String classPath = targetMessage.getPayload().getAttributeAsString("classPath");
        JXPathContext targetObject = JXPathContext.newContext(targetMessage);
        String path = "";
        if (classPath != null && !classPath.equals("")) {
            path = "payload/" + targetMessage.getPayload().getAttributeAsString("dtoName");
        }
        Map<String, Object> container = new HashMap<>();
        if (maps != null && !maps.isEmpty()) {
            for (MapWrapper map : maps) {
                String keySource = map.getKey();
                String sourceField = map.getValue();
                Object key = sourceObject.getValue(keySource);
                Object value = sourceObject.getValue(sourceField);
                container.put(key.toString(), value);
                String targetField = path + map.getTarget();
                targetObject.setValue(targetField, container);
            }
        }
        if (mapperFields != null) {
            for (Field field : mapperFields) {
                String sourceField = field.getSource();
                String targetField = field.getTarget();
                if (sourceField.contains("inputParameters") && !path.equals("")) {
                    targetField = path + field.getTarget();
                }

                try {
                    Object sourceValue = sourceObject.getValue(sourceField);
                    targetObject.setValue(targetField, sourceValue);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }


            }

        }

    }

    private Map<String, Object> modifyDynamicGroup(List<Map<String, Object>> dynamicGroupList) {
        Map<String, Object> inputParametersMap = new HashMap<>();
        for (Map<String, Object> dynamicGroupMap : dynamicGroupList) {
            inputParametersMap.putAll((Map<String, Object>) dynamicGroupMap.get("inputParameters"));
        }
        return inputParametersMap;
    }

    private void checkTargetObjAndInit(String targetPath, Message message, Object type) {

        Map<String, Object> targetMap = message.getPayload();
        String[] splitPath = targetPath.split("/");
        for (String object : splitPath) {
            if (object.isEmpty() || object.equals("payload")) {
                continue;

            }
            if (targetMap.containsKey(object)) {
                targetMap = (Map<String, Object>) targetMap.get(object);

            } else {
                targetMap.put(object, type);

            }
        }
    }

   /* private Message loadCustomApiParamters (Message message){
      if (message.getHeader().getAttributeAsString("apiCode").equals("18787889361")){
          SourcePaymentMethodDTO sourcePaymentMethodDTO = new SourcePaymentMethodDTO();
          sourcePaymentMethodDTO.setPaymentMethodType(new Long("852"));
          message.getPayload().put("sourcePaymentMethod",sourcePaymentMethodDTO);
      }
      return message ;
      }*/

}
