package com.cit.vericash.apis.addbeneficiary;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.addbeneficiary.AddBeneficiaryRquestTransformer")
public class AddBeneficiaryRquestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {
//        ObjectMapper mapperSource = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
//                false);
//        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
//        Object beneficiaryObject = message.getPayload().getAttribute("beneficiary");
//        BeneficiaryDTO beneficiaryDTO = message.getPayload().getMapper()
//                .readValue(ow.writeValueAsString(beneficiaryObject), BeneficiaryDTO.class);
//
//
//
//        if (beneficiaryDTO == null ) {
//            throw new APIException("Missing Configuration in Beneficiary",
//                    ErrorCode.INVALID_BENEFICIARY_CONFIGURATON.getErrorCode());
//        }
//
//
//        message.getPayload().put("beneficiary", beneficiaryDTO);

    }

}
