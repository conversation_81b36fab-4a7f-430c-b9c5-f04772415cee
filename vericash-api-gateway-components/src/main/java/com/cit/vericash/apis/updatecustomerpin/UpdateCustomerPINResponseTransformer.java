package com.cit.vericash.apis.updatecustomerpin;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.updatecustomerpin.UpdateCustomerPINResponseTransformer")
public class UpdateCustomerPINResponseTransformer implements MuleResponseTransformer {
    private final String successMsgKey = "success_msg";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();

        Map<String, String> result = new HashMap<String, String>();

        try {

            result.put(cacheableFieldParameters.getResponseParameters(successMsgKey), "Customer PIN has been updated successfuly");

        } catch (Exception e) {
            throw e;
        }

        return result;
    }
}