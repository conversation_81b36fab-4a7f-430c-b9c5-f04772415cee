package com.cit.vericash.apis.RotatingSavings;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.ConfirmUpdate")
public class ConfirmUpdate {
    @Autowired
    ConnectionUtil connectionUtil;

    public void OldStatusRevert(Request request) {
        Long rotatingSavingsId = request.getMessage().getPayload().getAttributeAsLong("rotatingSavingsId");
        List<Parameter> parameters = new ArrayList<>();
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("OldStatusRevert" , this.getClass() , String.valueOf(rotatingSavingsId));
        connectionUtil.executeUpdate(sqlQuery, parameters);
    }
}
