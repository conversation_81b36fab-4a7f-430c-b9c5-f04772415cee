package com.cit.vericash.apis.receiver_approval;

import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.dto.request.Request;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.receiver_approval.ViewReceiverApprovalRequestTransformer")
@RequiredArgsConstructor
public class ViewReceiverApprovalRequestTransformer implements VericashAction {
    private final ReceiverApprovalService receiverApprovalService;
    @Override
    public Object process(Request request) throws Exception {
        return receiverApprovalService.getReceiverRequestApproval(request);
    }
}
