package com.cit.vericash.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
@RunWith(MockitoJUnitRunner.class)
class PdfGeneratorTest {

    @InjectMocks
    PdfGenerator pdfGenerator;
    @Mock
    ConfigFileResolver configFileResolver = Mockito.mock(ConfigFileResolver.class);
    @BeforeEach
    void setUp() {
        pdfGenerator = new PdfGenerator(configFileResolver);
        when(configFileResolver.getVericashRootFolder()).thenReturn("D:\\dev\\work\\projects\\vericash-apis-config\\vericash-config");
    }

    @Test
    void createPdfFromText() throws Exception {
//        Map<String, Object> data = new HashMap<>();
//        data.put("Service", "Purchase E-Voucher");
//        data.put("Partner", "UBA");
//        data.put("Status", "Succeeded");
//        data.put("Name", "1q");
//        data.put("Buy Price", "50");
//        data.put("Expire Date","22-OCT-2024");
//
//
//        PDDocument pdDocument = pdfGenerator.appendTextToPDF(data, Path.of(configFileResolver.getVericashRootFolder(), TEMPLATES, RECEIPT_PDF).toString());
//
//        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//        pdDocument.save(byteArrayOutputStream);
//        byte[] bytes = byteArrayOutputStream.toByteArray();
//        System.out.println(Base64.getEncoder().encodeToString(bytes));
//        pdDocument.close();
    }
}