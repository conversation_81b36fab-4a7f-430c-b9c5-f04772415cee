package com.cit.vericash.apis.customerDevices;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import com.cit.query.engine.impl.ServiceQueryEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.cit.mpaymentapp.model.Enums.DeviceStatus;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;

@Component
@Qualifier("com.cit.vericash.apis.customerDevices.GetCustomerDevicesAction")
public class GetCustomerDevicesAction implements VericashAction{
	
	@Autowired
    ConnectionUtil connectionUtil;

	@Override
	public Object process(Request request) throws Exception {
		// TODO Auto-generated method stub
		List<Map<String,Object>> customerDevices = new ArrayList<Map<String,Object>>();
		
		Long customerId = request.getMessage().getPayload().getAttributeAsLong("customerId");
		Integer deviceStatus = request.getMessage().getPayload().getAttributeAsInteger("deviceStatus");

		StringBuilder sqlQuery = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("devicesQuerySb" , this.getClass()));
		/*StringBuffer devicesQuerySb = new StringBuffer("SELECT CUSTOMER_DEVICE_ID")
				.append(", CUSTOMER_DEVICES.NAME AS DEVICE_NAME")
				.append(", CUSTOMER_DEVICES.STATUS")
				.append(", DEVICE_STATUS.NAME AS STATUS_STR")
				.append(", LAST_ACCESS")
				.append(", APP_VERSION")
				.append(", IMEI")
				.append(", DEVICE_MANUFACTURER")
				.append(", MODEL, PLATFORM")
				.append(", DEVICE_VDI")
				.append(", DEVICE_ODI")
				.append(", REGISTRATION_ID")
				.append(" FROM CUSTOMER_DEVICES LEFT JOIN DEVICE_STATUS ON CUSTOMER_DEVICES.STATUS = DEVICE_STATUS.ID")
				.append(" WHERE CUSTOMER_ID = ?");*/
		if(deviceStatus != null) {
			sqlQuery.append(ServiceQueryEngine.getQueryStringToExecute("devicesQuerySb_condition" , this.getClass()));
		}
		else {
			sqlQuery.append(ServiceQueryEngine.getQueryStringToExecute("devicesQuerySb_condition_2" , this.getClass()));
		}
		
		List<Parameter> parameters = new ArrayList<Parameter>();
		parameters.add(new Parameter(1, customerId));
		if(deviceStatus != null) {
			parameters.add(new Parameter(2, deviceStatus));
		}
		else {
			parameters.add(new Parameter(2, DeviceStatus.DELETED.ordinal()));
		}
		
		List<String> fields = new ArrayList<String>();
		fields.add("CUSTOMER_DEVICE_ID");
		fields.add("DEVICE_NAME");
		fields.add("STATUS");
		fields.add("STATUS_STR");
		fields.add("LAST_ACCESS");
		fields.add("APP_VERSION");
		fields.add("IMEI");
		fields.add("DEVICE_MANUFACTURER");
		fields.add("MODEL");
		fields.add("PLATFORM");
		fields.add("DEVICE_VDI");
		fields.add("DEVICE_ODI");
		fields.add("REGISTRATION_ID");
		
		List<Record> deviceRecords = connectionUtil.executeSelect(sqlQuery.toString(), parameters, fields);
		
		if(deviceRecords != null && !deviceRecords.isEmpty()) {
			Map<Integer,ArrayList<String>> allowedActionsPerDeviceStatus = getAllowedActions();
			for (Record record : deviceRecords) {
				Map<String, Object> customerDevice = buildCustomerDeviceDTO(allowedActionsPerDeviceStatus, record);
				customerDevices.add(customerDevice);
			}	
		}
		return customerDevices;
	}

	private Map<String, Object> buildCustomerDeviceDTO(Map<Integer, ArrayList<String>> allowedActionsPerDeviceStatus,
			Record deviceRecord) {
		Long id = deviceRecord.getValueAsLong("CUSTOMER_DEVICE_ID");
		String name = deviceRecord.getValueAsString("DEVICE_NAME");
		int status = deviceRecord.getValueAsInt("STATUS");
		String statusStr = deviceRecord.getValueAsString("STATUS_STR");
		Date lastLoginAccess = deviceRecord.getValueAsDate("LAST_ACCESS");
		String appVersion = deviceRecord.getValueAsString("APP_VERSION");
		String imei = deviceRecord.getValueAsString("IMEI");
		String manufacturer = deviceRecord.getValueAsString("DEVICE_MANUFACTURER");
		String model = deviceRecord.getValueAsString("MODEL");
		String platformVersion = deviceRecord.getValueAsString("PLATFORM");
		String vdi = deviceRecord.getValueAsString("DEVICE_VDI");
		String odi = deviceRecord.getValueAsString("DEVICE_ODI");
		String registrationId = deviceRecord.getValueAsString("REGISTRATION_ID");
		ArrayList<String> allowedActions = allowedActionsPerDeviceStatus.get(status);

		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String lastAccessLogin = simpleDateFormat
				.format(lastLoginAccess);

		Map<String,Object> customerDevice = new HashMap<String,Object>();
		customerDevice.put("id", id);
		customerDevice.put("name", name);
		customerDevice.put("status", statusStr);
		customerDevice.put("lastLoginAccess", lastAccessLogin);
		customerDevice.put("appVersion", appVersion);
		customerDevice.put("imei", imei);
		customerDevice.put("manufacturer", manufacturer);
		customerDevice.put("model", model);
		customerDevice.put("platformVersion", platformVersion);
		customerDevice.put("deviceIdentifier", buildDeviceIdentifierDTO(vdi,odi));
		customerDevice.put("topicsToken", buildTopicsTokenDTO(registrationId,null));
		customerDevice.put("allowedActions", allowedActions);
		return customerDevice;
	}

	private Map<String, String> buildTopicsTokenDTO(String registrationId, String registrationType) {
		// TODO Auto-generated method stub
		Map<String, String> topicsToken = new HashMap<String, String>();
		topicsToken.put("registrationId", registrationId);
		topicsToken.put("registrationType", registrationType);
		return topicsToken;
	}

	private Map<String, String> buildDeviceIdentifierDTO(String vdi, String odi) {
		// TODO Auto-generated method stub
		Map<String, String> deviceIdentifier = new HashMap<String, String>();
		deviceIdentifier.put("VDI", vdi);
		deviceIdentifier.put("ODI", odi);
		return deviceIdentifier;
	}

	private Map<Integer, ArrayList<String>> getAllowedActions() {
		// TODO Auto-generated method stub
		Map<Integer, ArrayList<String>> allowedActions = new HashMap<Integer, ArrayList<String>>();
/*
		String query = "SELECT DEVICE_STATUS,\"ACTION\" FROM DEVICE_ALLOWED_ACTIONS ";
*/
		String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getAllowedActions" , this.getClass());
		List<String> fields = new ArrayList<String>();
		fields.add("DEVICE_STATUS");
		fields.add("ACTION");
		
		List<Record> allowedActionsRecords = connectionUtil.executeSelect(sqlQuery, new ArrayList<Parameter>(), fields);
		for (Record record : allowedActionsRecords) {
			int status = record.getValueAsInt("DEVICE_STATUS");
			String action = record.getValueAsString("ACTION");
			
			boolean isStatusInserted = allowedActions.containsKey(status);
			if(isStatusInserted) {
				allowedActions.get(status).add(action);
			}
			else {
				allowedActions.put(status, new ArrayList<>(Arrays.asList(action)));
			}
		}
		return allowedActions;
	}
}
