package com.cit.vericash.apis.openBankAccount;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.when;

import com.cit.vericash.api.entity.customer.Customer;
import com.cit.vericash.api.repository.customer.CustomerRepository;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.AdditionalData;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.EchoData;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import org.junit.jupiter.api.DisplayName;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Optional;

@RunWith(MockitoJUnitRunner.class)
public class ValidateOpenBankAccountDataTest {
    @Mock
    CustomerRepository customerRepository;
    @InjectMocks
    ValidateOpenBankAccountData validateOpenBankAccountData;


    @Test
    @DisplayName("Test process(Request); given Payload() 'bankAccountMsisdn' is empty string")
    public void testProcess_givenPayloadBankAccountMsisdnIsEmptyString() throws Exception {
        // Arrange
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Payload payload = new Payload();
        payload.put("bankAccountMsisdn", "");
        payload.put("birthDate", "2003-11-06");

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(payload);

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        // Act and Assert
        assertThrows(QuickActionException.class, () -> validateOpenBankAccountData.process(request));
    }

    @Test
    @DisplayName("Test process(Request); given Payload() 'bankAccountMsisdn' is valid ")
    public void testProcess_givenPayloadValidAccountMsisdnIsValidString() throws Exception {
        // Arrange
        Long customerId = 123L;

        // Set up the header with customerId
        Header header = new Header();
        header.put("customerId", customerId);

        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(header);
        dynamicPayload.setPayload(new Payload());

        Payload payload = new Payload();
        payload.put("bankAccountMsisdn", "+***********");
        payload.put("birthDate", "2003-11-06");

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(header);
        message.setPayload(payload);

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        Customer customer = new Customer();
        customer.setMsisdn("+***********");

        when(customerRepository.findByCustomerId(anyLong()))
                .thenReturn(Optional.of(customer));

        Object response = validateOpenBankAccountData.process(request);

        // Act and Assert
        assertNull(response);
    }


    @Test
    @DisplayName("Test process(Request); given Payload() 'birthDate' is '42'; then throw QuickActionException")
    public void testProcess_givenPayloadBirthDateIs42_thenThrowQuickActionException() throws Exception {
        // Arrange
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Payload payload = new Payload();
        payload.put("birthDate", "42");

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(payload);

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        // Act and Assert
        assertThrows(QuickActionException.class, () -> validateOpenBankAccountData.process(request));
    }

    @Test
    @DisplayName("Test process(Request); given Payload() 'birthDate' is empty string; then throw QuickActionException")
    public void testProcess_givenPayloadBirthDateIsEmptyString_thenThrowQuickActionException() throws Exception {
        // Arrange
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Payload payload = new Payload();
        payload.put("birthDate", "");

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(payload);

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        // Act and Assert
        assertThrows(QuickActionException.class, () -> validateOpenBankAccountData.process(request));
    }


    @Test
    @DisplayName("Test process(Request); then throw QuickActionException")
    public void testProcess_thenThrowQuickActionException() throws Exception {
        // Arrange
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(new Payload());

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        // Act and Assert
        assertThrows(QuickActionException.class, () -> validateOpenBankAccountData.process(request));
    }

    @Test
    @DisplayName("Test process(Request); given Payload() 'bankAccountMsisdn' is valid and 'birthDate' is less than 18; throw exception")
    public void testProcess_givenPayloadValidAccountMsisdnIsValidSAndBirthdateLessThan18() throws Exception {
        // Arrange
        DynamicPayload dynamicPayload = new DynamicPayload();
        dynamicPayload.setHeader(new Header());
        dynamicPayload.setPayload(new Payload());

        Payload payload = new Payload();
        payload.put("bankAccountMsisdn", "+***********");
        payload.put("birthDate", "2024-11-19");

        Message message = new Message();
        message.setAdditionalData(new AdditionalData());
        message.setDynamicPayload(dynamicPayload);
        message.setEchoData(new EchoData());
        message.setHeader(new Header());
        message.setPayload(payload);

        Request request = new Request();
        request.setEncyptedMessage("Encypted Message");
        request.setMessage(message);
        request.setSignature("Signature");

        Customer customer = new Customer();
        customer.setMsisdn("+***********");

        // Act and Assert
        assertThrows(QuickActionException.class, () -> validateOpenBankAccountData.process(request));
    }
}
