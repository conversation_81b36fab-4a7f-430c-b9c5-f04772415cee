package com.cit.vericash.printpdf;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.receipt.PaymentMethodAccountIdentifier;
import jdk.jfr.Description;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.runners.MockitoJUnitRunner;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.cit.vericash.printpdf.AliasResolver.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
class AliasResolverTest {
    private static final Record record = new Record();
    private static final Record garParametersRecord1 = new Record();
    private static final Record garParametersRecord2 = new Record();
    private static final Record garParametersRecord3 = new Record();
    private static final Record garParametersRecord4 = new Record();
    private static final List<Record> garParameters = new ArrayList<>();
    public static final String DEFAULT_SERVICE_NAME = "default service name";
    public static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    public static final String BILL_PAYMENT = "Bill payment";
    public static final long SENDER_ACCOUNT_VALUE = 77025L;
    public static final long SOURCE_PAYMENTMETHOD_ID_VALUE = 871L;

    static ConnectionUtil connectionUtil = mock(ConnectionUtil.class);
    static PaymentMethodAccountIdentifier paymentMethodAccountIdentifier = mock(PaymentMethodAccountIdentifier.class);
    static AliasResolver aliasResolver;
    @BeforeAll
    static void initClass() {
        aliasResolver = new AliasResolver(paymentMethodAccountIdentifier, connectionUtil);
        aliasResolver.init();
    }
    @BeforeEach
    void setUp() throws ParseException {
        record.put(SERVICE_NAME, BILL_PAYMENT);
        record.put("TRANSACTION_AMOUNT", 50);
        record.put(TRANSACTION_STATUS, 1);
        record.put("TRANSACTION_DATE", SIMPLE_DATE_FORMAT.parse("2020-01-01 10:00:00.0"));
        record.put("TRNAS_EXEC_SUMMARY_ID", 11111L);
        record.put(SENDER_ACCOUNT, SENDER_ACCOUNT_VALUE);
        record.put(SOURCE_PAYMENTMETHOD_ID, SOURCE_PAYMENTMETHOD_ID_VALUE);
        record.put(SENDER_MSISDN, "+***********");
        record.put(SOURCE_PM, "{\"330\":\"khamis\",\"331\":\"*********\",\"332\":\"**********\",\"126\":\"saving\"}");
        record.put(RECEIEVER_ACCOUNT, null);
        record.put(DESTINATION_PAYMENTMETHOD_ID, 848);
        record.put(RECEIVER_MSISDN, null);
        record.put(DESTINATION_PM, "{\"100\":\"4568\",\"101\":\"********\"}");

        garParametersRecord1.put("PAYMENT_METHOD_TYPE",871L);
        garParametersRecord1.put("PARAMETER_CODE","330");
        garParametersRecord1.put("PARAMETER_VALUE","khamis");
        garParametersRecord1.put("MSISDN","+***********");

        garParametersRecord2.put("PAYMENT_METHOD_TYPE",871L);
        garParametersRecord2.put("PARAMETER_CODE","331");
        garParametersRecord2.put("PARAMETER_VALUE","*********");
        garParametersRecord2.put("MSISDN","+***********");

        garParametersRecord3.put("PAYMENT_METHOD_TYPE",871L);
        garParametersRecord3.put("PARAMETER_CODE","332");
        garParametersRecord3.put("PARAMETER_VALUE","**********");
        garParametersRecord3.put("MSISDN","+***********");

        garParametersRecord4.put("PAYMENT_METHOD_TYPE",871L);
        garParametersRecord4.put("PARAMETER_CODE","126");
        garParametersRecord4.put("PARAMETER_VALUE","saving");
        garParametersRecord4.put("MSISDN","+***********");

        garParameters.add(garParametersRecord1);
        garParameters.add(garParametersRecord2);
        garParameters.add(garParametersRecord3);
        garParameters.add(garParametersRecord4);

    }

    @Test
    void init() {
        assertThat(aliasResolver).isNotNull();
        assertThat(connectionUtil).isNotNull();
        assertThat(paymentMethodAccountIdentifier).isNotNull();
    }

    @Test
    @Description("tests resolveAlias with Service Name Alias And record's service name is null it should" +
            "get the default sent service name that is sent with it")
    void resolveServiceNameAlias1() {
        record.remove(SERVICE_NAME);
        assertThat(aliasResolver.resolveAlias(record, DEFAULT_SERVICE_NAME, SERVICE_NAME, null)).isEqualTo(DEFAULT_SERVICE_NAME);
    }
    @Test
    @Description("tests resolveAlias with Service Name Alias And record's service name is not null it should" +
            "get the service name from record")
    void resolveServiceNameAlias2() {
        assertThat(aliasResolver.resolveAlias(record, DEFAULT_SERVICE_NAME, SERVICE_NAME, null)).isEqualTo(BILL_PAYMENT);
    }
    @Test
    @Description("tests resolveAlias with Service Name Alias when both names null !")
    void resolveServiceNameAlias3() {
        record.remove(SERVICE_NAME);
        assertThat(aliasResolver.resolveAlias(record, null, SERVICE_NAME, null)).isNull();
    }
    @Test
    @Description("tests resolveAlias with transaction status Alias And record's service name 0 it should be INITIATED ")
    void resolveTransactionStatusAlias0() {
        record.put(TRANSACTION_STATUS, 0);
        assertThat(aliasResolver.resolveAlias(record, DEFAULT_SERVICE_NAME, TRANSACTION_STATUS, null)).isEqualTo("Initiated");
    }
    @Test
    @Description("tests resolveAlias with transaction status Alias And record's service name is null it should be null")
    void resolveTransactionStatusAlias1() {
        record.remove(TRANSACTION_STATUS);
        assertThat(aliasResolver.resolveAlias(record, DEFAULT_SERVICE_NAME, TRANSACTION_STATUS, null)).isNull();
    }
    @Test
    @Description("tests resolveAlias with transaction status Alias And record's service name 1 it should be Succeeded ")
    void resolveTransactionStatusAlias2() {
        assertThat(aliasResolver.resolveAlias(record, DEFAULT_SERVICE_NAME, TRANSACTION_STATUS, null)).isEqualTo("Succeeded");
    }
    @Test
    @Description("tests resolveAlias with transaction status Alias when value is 2 it should be Failed !")
    void resolveTransactionStatusAlias3() {
        record.put(TRANSACTION_STATUS, 2);
        assertThat(aliasResolver.resolveAlias(record, null, TRANSACTION_STATUS, null)).isEqualTo("Failed");
    }
    @Test
    @Description("tests resolveAlias with transaction status Alias when value is less than zero")
    void resolveTransactionStatusAlias4() {
        record.put(TRANSACTION_STATUS, -200000);
        assertThat(aliasResolver.resolveAlias(record, null, TRANSACTION_STATUS, null)).isNull();
    }
    @Test
    @Description("tests resolveAlias with transaction status Alias when value is more than normal")
    void resolveTransactionStatusAlias5() {
        record.put(TRANSACTION_STATUS, 200000);
        assertThat(aliasResolver.resolveAlias(record, null, TRANSACTION_STATUS, null)).isNull();
    }

    @Test
    @Description("tests resolveAlias with transaction status Alias when value other type!")
    void resolveTransactionStatusAlias6() {
        record.put(TRANSACTION_STATUS, "hello");
        assertThat(aliasResolver.resolveAlias(record, null, TRANSACTION_STATUS, null)).isNull();
    }
    @Test
    @Description("tests resolveAlias with SOURCE_PM Alias when value other type!")
    void resolveSOURCE_PMAlias1() {
        record.put(SOURCE_PM, 123);
        assertThat(aliasResolver.resolveAlias(record, null, SOURCE_PM, null)).isNull();
    }
    @Test
    @Description("tests resolveAlias with SOURCE_PM Alias when value other is null!")
    void resolveSOURCE_PMAlias2() {
        record.remove(SOURCE_PM);
        assertThat(aliasResolver.resolveAlias(record, null, SOURCE_PM, null)).isNull();
    }
    @Test
    @Description("tests resolveAlias with SOURCE_PM Alias when value is valid!")
    void resolveSOURCE_PMAlias3() {
        ArgumentCaptor<Map> mapCaptor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<Long> longCaptor = ArgumentCaptor.forClass(Long.class);
        when(paymentMethodAccountIdentifier.getPaymentMethodAccountNumber(
                /*eq(SOURCE_PAYMENTMETHOD_ID_VALUE),*/
                longCaptor.capture(),
                mapCaptor.capture(),
                eq(SENDER_ACCOUNT_VALUE),
                any()
        )).thenReturn("4568");

        String result = aliasResolver.resolveAlias(record, null, SOURCE_PM, null);

        assertThat(result).isEqualTo("4568");

//        verify(paymentMethodAccountIdentifier, times(1)).getPaymentMethodAccountNumber(
//                longCaptor.capture(),
//                mapCaptor.capture(),
//                eq(SENDER_ACCOUNT_VALUE),
//                any()
//        );

        Map<String, String> capturedMap = mapCaptor.getValue();
        long val = longCaptor.getValue();
        System.out.println(val);
        assertThat(val).isEqualTo(SOURCE_PAYMENTMETHOD_ID_VALUE);
        System.out.println(capturedMap);
        assertThat(capturedMap).isNotNull();
        assertThat(capturedMap.get("330")).isEqualTo("khamis");
        assertThat(capturedMap.get("331")).isEqualTo("*********");
        assertThat(capturedMap.get("332")).isEqualTo("**********");
        assertThat(capturedMap.get("126")).isEqualTo("saving");
        assertThat(capturedMap.get("MSISDN")).isEqualTo("+***********");
    }
    @Test
    @Description("tests resolveAlias with SOURCE_PM Alias when value is valid but no gar parameters and account id is null!" +
            "should return null")
    void resolveSOURCE_PMAlias4() {
        record.remove(SOURCE_PM);
        record.remove(SENDER_ACCOUNT);
        when(paymentMethodAccountIdentifier.getPaymentMethodAccountNumber(
                eq(SOURCE_PAYMENTMETHOD_ID_VALUE),
                any(),
                eq(SENDER_ACCOUNT_VALUE),
                any()
        )).thenReturn(null);

        String result = aliasResolver.resolveAlias(record, null, SOURCE_PM, null);

        assertThat(result).isNull();

    }

    @Test
    @Description("tests resolveAlias with SOURCE_PM Alias when value is valid but no gar parameters and account id is not null!" +
            "should return null")
    void resolveSOURCE_PMAlias5() {

        when(paymentMethodAccountIdentifier.getPaymentMethodAccountNumber(
                eq(SOURCE_PAYMENTMETHOD_ID_VALUE),
                any(),
                eq(SENDER_ACCOUNT_VALUE),
                any()
        )).thenReturn(null);
        when(connectionUtil.executeSelect(any(String.class), anyList()))
                .thenReturn(garParameters);


        String result = aliasResolver.resolveAlias(record, null, SOURCE_PM, null);
        assertThat(result).isNull();

    }

    @Test
    @Description("tests resolveAlias with SOURCE_PM Alias when value is valid but no gar parameters and account id is not null!" +
            "should return value from the fetched map")
    void resolveSOURCE_PMAlias6() {
        ArgumentCaptor<Map> mapCaptor = ArgumentCaptor.forClass(Map.class);
        record.remove(SOURCE_PM);
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getGarParametersByCustomerGarAccountID",
                AliasResolver.class);
        when(paymentMethodAccountIdentifier.getPaymentMethodAccountNumber(
                eq(SOURCE_PAYMENTMETHOD_ID_VALUE),
                mapCaptor.capture(),
                eq(SENDER_ACCOUNT_VALUE),
                any()
        )).thenReturn("**********");
        when(connectionUtil.executeSelect(eq(sqlQuery), anyList()))
                .thenReturn(garParameters);

        String result = aliasResolver.resolveAlias(record, null, SOURCE_PM, null);
        System.out.println(mapCaptor.getValue());
        assertThat(result).isEqualTo("**********");

    }
}