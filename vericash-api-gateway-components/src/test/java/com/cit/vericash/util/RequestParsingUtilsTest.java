package com.cit.vericash.util;

import com.cit.vericash.api.commons.dto.DynamicParametersDTOValidator;
import com.cit.vericash.api.transformerfactory.TransformerFactory;
import com.cit.vericash.apis.commons.model.VericashAPI;
import com.cit.vericash.apis.commons.util.CachableVericashApi;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.dynamicrequest.transformer.DynamicMessageToMessageTransformerImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
//@RunWith(SpringExtension.class)
@RunWith(MockitoJUnitRunner.class)
public class RequestParsingUtilsTest {
    @Mock
    private CachableVericashApi vericashApiCache;
    @Mock
    private TransformerFactory transformerFactory;
    @InjectMocks
    RequestParsingUtils requestParsingUtils;
    @Mock
    DynamicMessageToMessageTransformerImpl t;
    private final String OCCURENCE_REQUEST =  "{\"signature\":\"123123123\",\"message\":{\"header\":{\"apiCode\":\"17777771\",\"timestamp\":1671448757084,\"sessionId\":\"\",\"walletShortCode\":\"2020\",\"channelCode\":\"test_1\"},\"payload\":{\"dynamicGroup\":[{\"groupId\":24,\"groupName\":\"ownTransfer\",\"inputParameters\":{\"106\":{\"parameterCode\":\"106\",\"parameterLabel\":\"amount\",\"parameterValue\":23},\"136\":{\"parameterCode\":\"136\",\"parameterLabel\":\"narration\",\"parameterValue\":\"aaaaaaaaaaaaaa\"},\"202\":{\"parameterCode\":\"202\",\"parameterLabel\":\"transferFrom\",\"parameterValue\":{\"paymentMethodName\":\"Mkento Wallet Source\",\"paymentMethodCode\":103944,\"paymentMethodType\":\"842\"}},\"210\":{\"parameterCode\":\"210\",\"parameterLabel\":\"transferTo\",\"parameterValue\":{\"paymentMethodName\":\"Virtual Card Source\",\"paymentMethodCode\":105982,\"paymentMethodType\":\"851\"}}}}],\"senderOwnerType\":\"1\",\"language\":\"en\",\"authParams\":[{\"authCode\":\"3\",\"authValue\":\"Password_9632\",\"authOrder\":\"1\"}],\"senderIdentifyValue\":\"Arwa10\"},\"additionalData\":{\"userType\":\"retal\"},\"echoData\":{\"Sample\":\"Sample\"}}}";

    Request request = new Request();
    String []keys;
    String []inpParamsCodes;
    Message message;
    @Before
    public void setUp() throws Exception {
        List<Map<String, Object>> dynamicGroup = new ArrayList<>();
        Map<String, Object> dynamicParameterGroup1 = new LinkedHashMap<>();
        dynamicParameterGroup1.put("groupId", 651);
        dynamicParameterGroup1.put("groupName", "View Recurring Occurrences");
        dynamicParameterGroup1.put("inputParameters", getInputParameters());
        dynamicGroup.add(dynamicParameterGroup1);
        Header header = new Header();
        header.put("apiCode", 2267);
        Payload payload = new Payload();
//        payload.put("dynamicGroup", dynamicParameterGroups);
        payload.put("dynamicGroup", dynamicGroup);
        payload.put("customerId", 7496);
        payload.put("recurringId", 231231);
        payload.put("pageNumber", 1);
        payload.put("dateFrom", "2023-06-22 19:26:37");
        payload.put("dateTo", "2023-10-23 19:26:37");
        payload.put("status", "0");
        keys = new String[]{
                "customerId",
                "recurringId",
                "pageNumber",
                "dateFrom",
                "dateTo",
                "status"
        };
        inpParamsCodes = new String[]{
                "101",
                "368",
                "315",
                "110",
                "367",
                "370"
        };
        message = new Message();
        message.setHeader(header);
        message.setPayload(payload);
        request.setMessage(message);
        request.setSignature("123");
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void testNotChangedOriginalRequest() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        String originalRequestStr = objectMapper.writeValueAsString(request);

        Request originalRequest = new Request();
        originalRequest.setSignature(request.getSignature());
        originalRequest.setEncyptedMessage(request.getEncyptedMessage());
        originalRequest.setMessage(request.getMessage());

        requestParsingUtils.parseRequestPayloadForGivenKeys(request, inpParamsCodes, keys);

        String requestStr = objectMapper.writeValueAsString(request);

        assertEquals(requestStr, originalRequestStr);
        assertEquals(originalRequest.getSignature(), request.getSignature());
        assertEquals(originalRequest.getMessage(), request.getMessage());
        assertEquals(originalRequest.getEncyptedMessage(), request.getEncyptedMessage());
    }
    @Test
    public void testNotChangedOriginalRequestWhenString() throws Exception {
        String originalRequest = new String(OCCURENCE_REQUEST);
        requestParsingUtils.parseRequestPayloadForGivenKeys(OCCURENCE_REQUEST, inpParamsCodes, keys);
        assertEquals(originalRequest, OCCURENCE_REQUEST);
    }
    @Test
    public void parseRequestPayloadForGivenKeys() throws Exception {
        VericashAPI vericashAPI = new VericashAPI();
        vericashAPI.setRequestType(1);
        when(vericashApiCache.getVericashAPI(any())).thenReturn(vericashAPI);
        when(transformerFactory.getTransformerFactory(any())).thenReturn(t);
        when(t.transform(any())).thenReturn(message);
        Map<Object, Object> objectMap = requestParsingUtils.parseRequestPayloadForGivenKeys(request, inpParamsCodes, keys);
        for (String key : keys){
            assertNotNull(objectMap.get(key));
        }
        for (String code : inpParamsCodes){
            assertNotNull(objectMap.get(code));
        }
    }
    private static LinkedHashMap<String, DynamicParametersDTOValidator> getInputParameters() {
        LinkedHashMap<String , DynamicParametersDTOValidator> inputParametersGroup1 = new LinkedHashMap<>();

        DynamicParametersDTOValidator dynamicParametersDTOValidator1 = new DynamicParametersDTOValidator();
        dynamicParametersDTOValidator1.setParameterLabel("customerId");
        dynamicParametersDTOValidator1.setParameterCode(101L);
        dynamicParametersDTOValidator1.setParameterValue(120320);

        DynamicParametersDTOValidator dynamicParametersDTOValidator2 = new DynamicParametersDTOValidator();
        dynamicParametersDTOValidator1.setParameterLabel("recurringId");
        dynamicParametersDTOValidator1.setParameterCode(368L);
        dynamicParametersDTOValidator1.setParameterValue("231231");

        DynamicParametersDTOValidator dynamicParametersDTOValidator3 = new DynamicParametersDTOValidator();
        dynamicParametersDTOValidator1.setParameterLabel("requested_Page");
        dynamicParametersDTOValidator1.setParameterCode(315L);
        dynamicParametersDTOValidator1.setParameterValue(1);

        DynamicParametersDTOValidator dynamicParametersDTOValidator4 = new DynamicParametersDTOValidator();
        dynamicParametersDTOValidator1.setParameterLabel("dateFrom");
        dynamicParametersDTOValidator1.setParameterCode(110L);
        dynamicParametersDTOValidator1.setParameterValue("2023-06-30");

        DynamicParametersDTOValidator dynamicParametersDTOValidator5 = new DynamicParametersDTOValidator();
        dynamicParametersDTOValidator1.setParameterLabel("customerId");
        dynamicParametersDTOValidator1.setParameterCode(367L);
        dynamicParametersDTOValidator1.setParameterValue("2024-01-31");

        DynamicParametersDTOValidator dynamicParametersDTOValidator6 = new DynamicParametersDTOValidator();
        dynamicParametersDTOValidator1.setParameterLabel("dateTo");
        dynamicParametersDTOValidator1.setParameterCode(370L);
        dynamicParametersDTOValidator1.setParameterValue(0);


        inputParametersGroup1.put("101", dynamicParametersDTOValidator1);
        inputParametersGroup1.put("368", dynamicParametersDTOValidator2);
        inputParametersGroup1.put("315", dynamicParametersDTOValidator3);
        inputParametersGroup1.put("110", dynamicParametersDTOValidator4);
        inputParametersGroup1.put("367", dynamicParametersDTOValidator5);
        inputParametersGroup1.put("370", dynamicParametersDTOValidator6);
        return inputParametersGroup1;
    }

    @Test
    public void getJsonNode()  {
        JsonNode jsonNode = requestParsingUtils.getJsonNode(request, new String[]{"message", "payload", "customerId"});
        assertEquals(7496, jsonNode.asInt());
    }
}