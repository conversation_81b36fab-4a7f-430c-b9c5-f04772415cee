package com.cit.vericash.apis.salarydisbursement;

import com.cit.mpaymentapp.common.group.transfers.GroupTransferHistoryDTO;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;

import static java.util.Arrays.asList;

@Component
@Qualifier("com.cit.vericash.apis.salarydisbursement.SalaryHistoryRequestTransformer")
public class SalaryHistoryRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {

        GroupTransferHistoryDTO groupTransferHistoryDTO = new GroupTransferHistoryDTO();
        groupTransferHistoryDTO.setId(message.getPayload().getAttributeAsLong("groupHistoryId"));
        groupTransferHistoryDTO.setStartDate(message.getPayload().getAttributeAsString("startDate"));
        groupTransferHistoryDTO.setEndDate(message.getPayload().getAttributeAsString("endDate"));
        List<GroupTransferHistoryDTO> groupTransferHistory = asList(
                groupTransferHistoryDTO
        );
        message.getPayload().put("groupTransferHistory", groupTransferHistory);
    }
}
