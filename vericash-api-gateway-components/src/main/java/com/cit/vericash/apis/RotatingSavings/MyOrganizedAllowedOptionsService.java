package com.cit.vericash.apis.RotatingSavings;

import com.cit.vericash.api.commons.dto.SimpleGenericDTO;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.RotatingSavings.MyOrganizedAllowedOptionsService")
public class MyOrganizedAllowedOptionsService {
    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    PropertyLoaderComponent propertyLoaderComponent;

    List<SimpleGenericDTO> allowedOptionsList;
    List<SimpleGenericDTO> allowedOptionsFullList;

    public void processAllowedOptionsJson() throws JsonProcessingException {
        String json = propertyLoaderComponent.getPropertyAsString("my.organized.options", "application", "application");
        ObjectMapper mapper = new ObjectMapper();
        mapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
        this.allowedOptionsFullList = mapper.readValue(json, new TypeReference<List<SimpleGenericDTO>>() {
        });
    }

    public List<SimpleGenericDTO> getAllowedOptionsList(Integer status, Date subscriptionStartDate, Date subscriptionEndDate) throws Exception {
        processAllowedOptionsJson();
        handleAllowedOptions(status, subscriptionStartDate, subscriptionEndDate);
        return this.allowedOptionsList;
    }

    private void handleAllowedOptions(Integer status, Date subscriptionStartDate, Date subscriptionEndDate) {
        this.allowedOptionsList = new ArrayList<>();
        checkCancel(status);
        checkMarkReady(status);
        checkReturnToNew(status);
        checkReviewAndUpdate(status);
        checkSubscribe(status, subscriptionStartDate, subscriptionEndDate);
        checkViewStatus(status);
        checkSettle(status);
    }

    private void checkCancel(Integer status) {
        if (status == 0 || status == 1 || status == 2) {
            this.allowedOptionsList.add(allowedOptionsFullList.get(0));
        }
    }

    private void checkMarkReady(Integer status) {
        if (status == 1) {
            this.allowedOptionsList.add(allowedOptionsFullList.get(1));
        }
    }

    private void checkReturnToNew(Integer status) {
        if (status == 2) {
            this.allowedOptionsList.add(allowedOptionsFullList.get(2));
        }
    }

    private void checkReviewAndUpdate(Integer status) {
        if (status == 0 || status == 1) {
            this.allowedOptionsList.add(allowedOptionsFullList.get(3));
        }
    }

    private void checkSubscribe(Integer status, Date subscriptionStartDate, Date subscriptionEndDate) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
        Date currentDate = java.util.Calendar.getInstance().getTime();
        if (status == 0 && subscriptionStartDate.before(currentDate) && subscriptionEndDate.after(currentDate)) {
            this.allowedOptionsList.add(allowedOptionsFullList.get(4));
        }
    }

    private void checkViewStatus(Integer status) {
        this.allowedOptionsList.add(allowedOptionsFullList.get(5));

    }

    private void checkSettle(Integer status) {
        if (status == 6) {
            this.allowedOptionsList.add(allowedOptionsFullList.get(6));
        }
    }
}
