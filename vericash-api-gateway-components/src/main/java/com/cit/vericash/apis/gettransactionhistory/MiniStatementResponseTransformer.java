package com.cit.vericash.apis.gettransactionhistory;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ExternalTransactionInformation;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.model.MiniStatement;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class MiniStatementResponseTransformer implements MuleResponseTransformer {
    private final String miniStatementKey = "mini_statement";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, String> result = new HashMap<String, String>();

        try {

            List<List<ExternalTransactionInformation>> externalTransactionInformationsListByGarIds = businessMessage.getExternalTransactionInformationsListByGarIds();
            Gson gson = new Gson();
            List<ExternalTransactionInformation> externalTransactionInformations;
            if (externalTransactionInformationsListByGarIds != null && externalTransactionInformationsListByGarIds.size() > 0) {
                ListIterator<List<ExternalTransactionInformation>> listItr = externalTransactionInformationsListByGarIds.listIterator();

                while (listItr.hasNext()) {
                    List<MiniStatement> trInformationList = new ArrayList<MiniStatement>();
                    externalTransactionInformations = listItr.next();
                    if (!externalTransactionInformations.isEmpty() && externalTransactionInformations != null) {
                        Long paymentMethodCode = null;
                        for (ExternalTransactionInformation externalTransactionInformation : externalTransactionInformations) {
                            //  TransactionReceiptInformation transactionInfo = (TransactionReceiptInformation) externalTransactionInformation;

                            MiniStatement miniStatement = new MiniStatement();

                            miniStatement.setTransactionId(externalTransactionInformation.getTranId());
                            miniStatement.setTransactionDate(externalTransactionInformation.getTranDate());
                            miniStatement.setAmount(externalTransactionInformation.getTransactionTotalAmount());
                            miniStatement.setFrom(externalTransactionInformation.getTranSenderAccountNumber());
                            miniStatement.setReceiver(externalTransactionInformation.getTranReceiverAccountNumber());
                            miniStatement.setTransactionType(externalTransactionInformation.getTranSubtype());
                            miniStatement.setTransferType(externalTransactionInformation.getTranTransferOption());
                            miniStatement.setOwnerType(externalTransactionInformation.getTranSenderName());
                            miniStatement.setSourcePaymentMethodTypeID(externalTransactionInformation.getTranSourcePaymentMethodTypeID());
                            miniStatement.setDestinationPaymentMethodTypeID(externalTransactionInformation.getTranDestinationPaymentMethodTypeID());
                            miniStatement.setSourceDynamicParameters(externalTransactionInformation.getTranSourceDynamicParameters());
                            miniStatement.setDestinationDynamicParameters(externalTransactionInformation.getTranDestinationDynamicParameters());

//                            if (externalTransactionInformation.getTranFeeAmount() != null) {
//                                miniStatement.setFee(externalTransactionInformation.getTranFeeAmount().toString());
//                            }
                            trInformationList.add(miniStatement);
                            paymentMethodCode = externalTransactionInformation.getTranGarId();
                        }
                        result.put("PaymentmethodCode_" + paymentMethodCode, gson.toJson(trInformationList));
                    }

                }
            }
        } catch (Exception e) {
            throw e;
        }

        return result;
    }
}