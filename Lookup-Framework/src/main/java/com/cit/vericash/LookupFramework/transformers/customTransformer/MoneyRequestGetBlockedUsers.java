package com.cit.vericash.LookupFramework.transformers.customTransformer;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.LookupFramework.common.interfaces.Transformer;
import com.cit.vericash.LookupFramework.common.util.LookupDTO;
import com.cit.vericash.LookupFramework.common.util.Record;
import com.cit.vericash.LookupFramework.common.util.enums;
import com.cit.vericash.LookupFramework.common.util.response.LookupResponse;
import com.cit.vericash.LookupFramework.transformers.DTO.paymentMetods.PaymentMethodContainerDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.util.*;

@Component
@Qualifier("MoneyRequestGetBlockedUsers")
public class MoneyRequestGetBlockedUsers implements Transformer {
    @SneakyThrows
    @Override
    public void transform(LookupDTO lookupDTO, Object response) {
        ObjectMapper mapper = new ObjectMapper();
        LookupResponse lookupResponse = (LookupResponse) response;
        Object lookupResponseObj =  lookupResponse.getAttribute(lookupDTO.getLookupName());
        HashMap<String, Object> map = mapper.convertValue(lookupResponseObj, HashMap.class);

        ArrayList resultRecords= (ArrayList) map.get("records");
        ArrayList <Map> lookupResult=new ArrayList<>();
        for(int i=0;i<resultRecords.size();i++){
            Map record= (Map) resultRecords.get(i);
            String customerId=record.get("C.CUSTOMER_ID").toString();
            String blockRecordId=record.get("MRBS.ID").toString();

            String customerFirstName=record.get("C.FIRST_NAME").toString();
            String customerMIDDLE_NAME=record.get("C.MIDDLE_NAME").toString();
            String customerLAST_NAME=record.get("C.LAST_NAME").toString();

            String customerName=customerFirstName+" "+customerMIDDLE_NAME+" "+customerLAST_NAME;
            String customerMobileNumber=record.get("C.MSISDN").toString();
            String requestType=record.get("MRBS.TYPE").toString();
            Map moneyRequestTypeInfo=new HashMap();
            moneyRequestTypeInfo.put("customerId",customerId);
            moneyRequestTypeInfo.put("blockRecordId",blockRecordId);
            moneyRequestTypeInfo.put("customerName",customerName.trim());
            moneyRequestTypeInfo.put("customerMobileNumber",customerMobileNumber);
            moneyRequestTypeInfo.put("moneyRequestType",requestType);
            lookupResult.add(moneyRequestTypeInfo);
        }
        lookupResponse.setAttribute("moneyRequest.viewBlockedUsers",lookupResult);
        System.out.println("");
    }
}