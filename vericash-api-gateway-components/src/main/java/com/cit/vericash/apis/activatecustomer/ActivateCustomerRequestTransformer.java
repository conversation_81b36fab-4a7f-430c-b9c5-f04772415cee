package com.cit.vericash.apis.activatecustomer;

import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.dto.response.*;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
@Qualifier("com.cit.vericash.apis.activatecustomer.ActivateCustomerRequestTransformer")
public class ActivateCustomerRequestTransformer extends MessageToBusinessMessageTransformerImpl {
    private final String OWNERTYPE = "sender-OwnerType";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public static void main(String[] args) {
        HashMap<String, MenuItem> menuItemHashMap = new HashMap<>();
        List<MenuItem> menuItems = new ArrayList<>();
        List<MenuItem> CardsMenumenuItems = new ArrayList<>();
        CardsMenumenuItems.add(new MenuItem(2, "Virtual Cards", null));
        CardsMenumenuItems.add(new MenuItem(3, "Manage Cards ", null));
        CardsMenumenuItems.add(new MenuItem(4, "Credit Card Repayment", null));
        CardsMenumenuItems.add(new MenuItem(5, "Prepaid Card Top-Up", null));
        MenuItem CardsMenu = new MenuItem(1, "Cards Menu", CardsMenumenuItems);
        menuItems.add(CardsMenu);
        List<MenuItem> transferSubMenu = new ArrayList<>();
        transferSubMenu.add(new MenuItem(25, "Own Accounts", null));
        transferSubMenu.add(new MenuItem(26, "Ecobank Domestic Accounts", null));
        transferSubMenu.add(new MenuItem(27, "Other Local Banks Accounts", null));
        transferSubMenu.add(new MenuItem(28, "Mobile Money Accounts", null));
        transferSubMenu.add(new MenuItem(29, "International Transfer", null));

        List<MenuItem> paymentsSubMenu = new ArrayList<>();
        paymentsSubMenu.add(new MenuItem(30, "Transportation", null));
        paymentsSubMenu.add(new MenuItem(31, "Bill Payment", null));
        paymentsSubMenu.add(new MenuItem(32, "Split Payment", null));
        paymentsSubMenu.add(new MenuItem(33, "Bulk Payments", null));
        paymentsSubMenu.add(new MenuItem(34, "Salary Payments", null));
        paymentsSubMenu.add(new MenuItem(35, "Merchant Payment", null));


        List<MenuItem> moreSubMenu = new ArrayList<>();
        List<MenuItem> servicesSubMenu = new ArrayList<>();
        servicesSubMenu.add(new MenuItem(15, "Transfer", transferSubMenu));
        servicesSubMenu.add(new MenuItem(16, "Payments", paymentsSubMenu));
        servicesSubMenu.add(new MenuItem(17, "Buy Airtime", null));
        servicesSubMenu.add(new MenuItem(18, "M’Kento Cash", null));
        servicesSubMenu.add(new MenuItem(19, "M’Kento Pay", null));
        servicesSubMenu.add(new MenuItem(20, "M’Kento Loans", null));
        servicesSubMenu.add(new MenuItem(21, "Recurring Transactions", null));
        servicesSubMenu.add(new MenuItem(22, "Transaction History", null));
        servicesSubMenu.add(new MenuItem(23, "Likelemba", null));
        servicesSubMenu.add(new MenuItem(24, "Travel Notification", null));

        List<MenuItem> settingsSubMenu = new ArrayList<>();
        settingsSubMenu.add(new MenuItem(36, "Beneficiary List", null));
        settingsSubMenu.add(new MenuItem(37, "Send Limits", null));
        settingsSubMenu.add(new MenuItem(38, "Change Password", null));
        settingsSubMenu.add(new MenuItem(39, "Change Username", null));
        settingsSubMenu.add(new MenuItem(40, "Change PIN", null));
        settingsSubMenu.add(new MenuItem(41, "Forget PIN", null));
        settingsSubMenu.add(new MenuItem(42, "My Devices", null));

        MenuItem servicesMenu = new MenuItem(7, "Services", servicesSubMenu);
        MenuItem settingsMenu = new MenuItem(8, "Settings", settingsSubMenu);
        MenuItem notificationMenu = new MenuItem(9, "Notification", null);
        MenuItem app_appearanceMenu = new MenuItem(10, "App Appearance", null);
        MenuItem mkentoEducate = new MenuItem(11, "M’Kento Educate", null);
        MenuItem about = new MenuItem(12, "About", null);
        MenuItem tour = new MenuItem(13, "Tour", null);
        MenuItem logout = new MenuItem(14, "Logout", null);
        moreSubMenu.add(servicesMenu);
        moreSubMenu.add(settingsMenu);
        moreSubMenu.add(notificationMenu);
        moreSubMenu.add(app_appearanceMenu);
        moreSubMenu.add(mkentoEducate);
        moreSubMenu.add(about);
        moreSubMenu.add(tour);
        moreSubMenu.add(logout);

        MenuItem MoreMenu = new MenuItem(6, "More Menu", moreSubMenu);

        menuItems.add(MoreMenu);

        menuItemHashMap.put(menuItems.get(0).name, menuItems.get(0));
        menuItemHashMap.put(menuItems.get(1).name, menuItems.get(1));
        Response response = new Response();
        response.setSignature(null);
        ResponseMessage responseMessage = new ResponseMessage();
        responseMessage.setApiCode("2020");
        responseMessage.setChannelCode("test_1");
        responseMessage.setResponseStatus(ResponseStatus.Succeeded);
        responseMessage.setSessionId("5468651551");
        responseMessage.setTimestamp("1665710262988");
        responseMessage.setResponseStatus(ResponseStatus.Succeeded);
        ResponseError responseError = new ResponseError();
        responseError.setErrorCategory(ErrorCategory.BusinessError);
        responseMessage.setResult(menuItemHashMap);
        response.setResponse(responseMessage);
        System.out.println(new Gson().toJson(response));
    }

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception {


    }

    static class MenuItem {
        Integer id;
        String name;
        List<MenuItem> subMenuItems;

        public MenuItem(Integer id, String name, List<MenuItem> subMenuItems) {
            this.id = id;
            this.name = name;
            this.subMenuItems = subMenuItems;
        }

    }

}
