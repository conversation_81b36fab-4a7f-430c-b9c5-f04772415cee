GetSupporeDestinationPaymnetMethod=SELECT PM. NAME \"paymentMethodName\",DPM. ID \"paymentMethodType\",PM.\"TYPE\",PMT. NAME,DPM.DESTINATION_GROUP_ID,DG.DESTINATION_GROUP_NAME,\
  IPD.CODE ,IPGD. NAME \"groupName\",IPGW.ID AS  \"GROUPID\",PM.CURRENCY_OR_UNIT  , \
  IPGD.GROUP_CODE , IPV.DATATYPE, IPV.IS_UPDATEABLE,  IPV.IS_VIEWABLE , IPVD.DATATYPE  AS DATATYPE_DV , IPVD.IS_UPDATEABLE  AS  IS_UPDATEABLE_DV  ,  IPVD.IS_VIEWABLE AS   IS_VIEWABLE_DV , IPG.INPUT_PARAM_ORDER  ,IPD.NAME \"parametre_Name\" , IPD.DEFAULT_VALIDATION_ID , IP.VALIDATION_ID , PM.PAYMETN_METHOD_ORDER  FROM CUSTOMER C \
  JOIN DEST_PAYMENT_METHOD_CUST_PROF DPMC ON C.CUSTOMER_TYPE_ID = DPMC.CUSTOMER_TYPE_ID \
  JOIN PAYMENT_METHOD PM ON PM.ID = DPMC.PAYMENT_METHOD_ID \
  JOIN DESTINATION_PAYMENT_METHOD DPM ON DPM.ID = PM.ID \
  LEFT JOIN DESTINATION_GROUP DG ON DG.DESTINATION_GROUP_ID = DPM.DESTINATION_GROUP_ID \
  LEFT JOIN PAYMENT_METHOD_TYPE PMT ON PMT.ID = PM.\"TYPE\" \
  LEFT JOIN INPUT_PARAM_GROUP_WALLET IPGW ON IPGW.ID = PM.GROUP_ID \
  LEFT JOIN INPUT_PARAM_GROUP_DEF IPGD ON IPGD.ID = IPGW.GROUP_DEF_ID \
  LEFT JOIN INPUT_PARAMS_GROUPS IPG ON IPG.GROUP_WALLET_ID = IPGW.ID  \
  LEFT JOIN INPUT_PARAMS IP ON IP.ID = IPG.INPUT_PARAM_ID \
  LEFT JOIN INPUT_PARAM_DICTIONARY IPD ON IPD.ID = IP.DICTIONARY_ID \
  LEFT JOIN INPUT_PARAM_VALIDATION IPV ON IPV.ID = IP.VALIDATION_ID \
  LEFT JOIN INPUT_PARAM_VALIDATION IPVD ON IPVD.ID = IPD.DEFAULT_VALIDATION_ID \
  where C.CUSTOMER_ID = ( select  BE.MAIN_CUSTOMER_ID  From  BE_CUSTOMER  BE \
  LEFT JOIN CUSTOMER C  ON C.BE_CUSTOMER_ID = BE.ID   where C.USER_ID= ? ) \
  ORDER BY IPG.INPUT_PARAM_ORDER
GetSupporeDestinationPaymnetMethod_Condition=SELECT PM. NAME \"paymentMethodName\",DPM. ID \"paymentMethodType\",PM.\"TYPE\",\
  PMT. NAME,DPM.DESTINATION_GROUP_ID,DG.DESTINATION_GROUP_NAME, \
  IPD.CODE,IPGD. NAME \"groupName\",IPGW.ID  \"GROUPID\" ,PM.CURRENCY_OR_UNIT , \
  IPGD.GROUP_CODE , IPV.DATATYPE, IPV.IS_UPDATEABLE,  IPV.IS_VIEWABLE , IPVD.DATATYPE  AS DATATYPE_DV , \
  IPVD.IS_UPDATEABLE  AS  IS_UPDATEABLE_DV  ,  IPVD.IS_VIEWABLE AS   IS_VIEWABLE_DV , \
  IPG.INPUT_PARAM_ORDER  ,IPD.NAME \"parametre_Name\" , IPD.DEFAULT_VALIDATION_ID , \
  IP.VALIDATION_ID , PM.PAYMETN_METHOD_ORDER  FROM CUSTOMER C \
  JOIN DEST_PAYMENT_METHOD_CUST_PROF DPMC ON C.CUSTOMER_TYPE_ID = DPMC.CUSTOMER_TYPE_ID \
  JOIN PAYMENT_METHOD PM ON PM. ID = DPMC.PAYMENT_METHOD_ID \
  JOIN DESTINATION_PAYMENT_METHOD DPM ON DPM. ID = PM. ID \
  LEFT JOIN DESTINATION_GROUP DG ON DG.DESTINATION_GROUP_ID = DPM.DESTINATION_GROUP_ID \
  LEFT JOIN PAYMENT_METHOD_TYPE PMT ON PMT. ID = PM.\"TYPE\" \
  LEFT JOIN INPUT_PARAM_GROUP_WALLET IPGW ON IPGW. ID = PM. GROUP_ID \
  LEFT JOIN INPUT_PARAM_GROUP_DEF IPGD ON IPGD. ID = IPGW.GROUP_DEF_ID \
  LEFT JOIN INPUT_PARAMS_GROUPS IPG ON IPG.GROUP_WALLET_ID = IPGW. ID \
  LEFT JOIN INPUT_PARAMS IP ON IP. ID = IPG.INPUT_PARAM_ID \
  LEFT JOIN INPUT_PARAM_DICTIONARY IPD ON IPD. ID = IP.DICTIONARY_ID \
  LEFT JOIN INPUT_PARAM_VALIDATION IPV ON IPV.ID = IP.VALIDATION_ID \
  LEFT JOIN INPUT_PARAM_VALIDATION IPVD ON IPVD.ID = IPD.DEFAULT_VALIDATION_ID \
  WHERE  C.USER_ID = ? \
  ORDER BY IPG.INPUT_PARAM_ORDER 