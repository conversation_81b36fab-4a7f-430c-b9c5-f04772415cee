<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

<!--     ChangeSet for inserting data into SERVICE_CONFIG_MAP -->
    <changeSet id="1" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="154"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From VC To Prepaied Card GTP by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="154"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="153"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Wallet to wallet"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="153"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="152"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From VC To Debit Card by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="152"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="151"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From VC To CIB by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="151"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="150"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Prepaid To Debit Card by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="150"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="149"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Prepaid To CIB by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="149"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="148"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Wallet To Debit Card by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="148"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="147"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Wallet To CIB by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="147"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="146"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Debit Card To Debit Card by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="146"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="145"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Debit Card To Prepaid by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="145"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="144"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Debit Card To Wallet by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="144"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="143"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Debit Card To VC by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="143"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="142"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Debit Card To CIB by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="142"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="141"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From CIB To Debit Card by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="141"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="140"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From CIB To VC by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="140"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="139"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From CIB To Prepaid by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="139"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="138"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From CIB To Wallet by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="138"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="137"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From CIB To CIB by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="137"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="136"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From VC To Wallet by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="136"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="135"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From VC To VC by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="135"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="134"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Wallet to VC"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="134"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="133"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Internal Transfer - From Prepaid To VC by Customer"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="133"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="132"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Prepaid Card GTP To Prepaid Card GTP"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="132"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="131"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Prepaid Card GTP To Wallet"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="131"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" value="130"/>
            <column name="IS_GENERIC_SERVICE" value="0"/>
            <column name="SERVICE_TYPE_NAME" value="Wallet to Prepaid Card GTP"/>
            <column name="SERVICE_MODE" value="0"/>
            <column name="SERVICE_CODE" value="130"/>
            <column name="IS_INDEMNITY" value="1"/>
            <column name="SEPARATE_QUEUE" value="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" value="0"/>
            <column name="HAS_WORKFLOW" value="0"/>
            <column name="SERVICE_CATEGORY" value="0"/>
            <column name="ENABLED_PROFILES" value="1"/>
            <column name="PARENT_SERVICE_CODE" value="99555"/>
            <column name="ROLE_BASED" value="0"/>
        </insert>

    </changeSet>



</databaseChangeLog>
