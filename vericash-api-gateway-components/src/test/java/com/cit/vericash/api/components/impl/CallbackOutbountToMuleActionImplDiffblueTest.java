package com.cit.vericash.api.components.impl;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

import java.util.*;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.api.components.impl.CallbackOutbountToMuleActionImpl;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

public class CallbackOutbountToMuleActionImplDiffblueTest {
	@Mock
	private ConnectionUtil connectionUtil;

	@InjectMocks
	private CallbackOutbountToMuleActionImpl callbackOutbountToMuleActionImpl;
	@Mock
	private ServiceQueryEngine serviceQueryEngine;
	@Captor
	private ArgumentCaptor<List<Parameter>> parameterCaptor;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testGetMoneyRequest() throws Exception {
		// Prepare test data
		Long transactionId = 10435440L;
		String query = "SELECT mr.ID,c.CUSTOMER_ID,c.MSISDN FROM MONEY_REQUEST mr INNER JOIN CUSTOMER c ON mr.SENDER_USER_ID = c.CUSTOMER_ID WHERE mr.TRANSACTION_ID =? AND mr.\"TYPE\" = 3";
		List<String> fields = Arrays.asList("ID", "CUSTOMER_ID", "MSISDN");

		Record mockRecord = new Record();
		mockRecord.put("ID", "10129844");
		mockRecord.put("CUSTOMER_ID", "106513");
		mockRecord.put("MSISDN", "+27831336976");

		List<Record> mockRecords = Collections.singletonList(mockRecord);

		// Mock the behavior of connectionUtil.executeSelect
		when(connectionUtil.executeSelect(eq(query), parameterCaptor.capture(), eq(fields))).thenReturn(mockRecords);

		// Execute the method to be tested
		HashMap<String, Object> result = callbackOutbountToMuleActionImpl.getMoneyRequest(transactionId);

		// Verify the results
		assertNotNull(result);
		assertEquals(true, result.get("isSplitRequest"));
		assertEquals("106513", result.get("CUSTOMER_ID"));
		assertEquals("+27831336976", result.get("MSISDN"));

		// Verify that connectionUtil.executeSelect was called with correct parameters
		verify(connectionUtil, times(1)).executeSelect(eq(query), parameterCaptor.capture(), eq(fields));

		// Validate the captured parameters
		List<Parameter> capturedParameters = parameterCaptor.getValue();
		assertEquals(1, capturedParameters.size());
		assertEquals(transactionId, capturedParameters.get(0).getValue());
	}


	@Test
	public void testGetMoneyRequest_ExceptionThrown() throws Exception {
		// Prepare test data
		Long transactionId = 10435440L;
		String query = "SELECT mr.ID,c.CUSTOMER_ID,c.MSISDN FROM MONEY_REQUEST mr INNER JOIN CUSTOMER c ON mr.SENDER_USER_ID = c.CUSTOMER_ID WHERE mr.TRANSACTION_ID =? AND mr.\"TYPE\" = 3";
		List<String> fields = Arrays.asList("ID", "CUSTOMER_ID", "MSISDN");

		// Mock the behavior of connectionUtil.executeSelect to throw an exception
		when(connectionUtil.executeSelect(eq(query), parameterCaptor.capture(), eq(fields))).thenThrow(new RuntimeException("Database error"));

		// Execute the method to be tested
		HashMap<String, Object> result = callbackOutbountToMuleActionImpl.getMoneyRequest(transactionId);

		// Verify the results
		assertNotNull(result);
		assertTrue("The result map should be empty when an exception is thrown", result.isEmpty());

		// Verify that connectionUtil.executeSelect was called with correct parameters
		verify(connectionUtil, times(1)).executeSelect(eq(query), parameterCaptor.capture(), eq(fields));

		// Validate the captured parameters
		List<Parameter> capturedParameters = parameterCaptor.getValue();
		assertEquals(1, capturedParameters.size());
		assertEquals(transactionId, capturedParameters.get(0).getValue());
	}

	@Test
	public void testGetMoneyRequest_NoRecords() throws Exception {
		// Prepare test data
		Long transactionId = 10435440L;
		String query = "SELECT mr.ID,c.CUSTOMER_ID,c.MSISDN FROM MONEY_REQUEST mr INNER JOIN CUSTOMER c ON mr.SENDER_USER_ID = c.CUSTOMER_ID WHERE mr.TRANSACTION_ID =? AND mr.\"TYPE\" = 3";
		List<String> fields = Arrays.asList("ID", "CUSTOMER_ID", "MSISDN");

		List<Record> emptyRecords = Collections.emptyList();

		// Mock the behavior of connectionUtil.executeSelect to return no records
		when(connectionUtil.executeSelect(eq(query), parameterCaptor.capture(), eq(fields))).thenReturn(emptyRecords);

		// Execute the method to be tested
		HashMap<String, Object> result = callbackOutbountToMuleActionImpl.getMoneyRequest(transactionId);

		// Verify the results
		assertNotNull(result);
		assertTrue("The result map should be empty when no records are returned", result.isEmpty());

		// Verify that connectionUtil.executeSelect was called with correct parameters
		verify(connectionUtil, times(1)).executeSelect(eq(query), parameterCaptor.capture(), eq(fields));

		// Validate the captured parameters
		List<Parameter> capturedParameters = parameterCaptor.getValue();
		assertEquals(1, capturedParameters.size());
		assertEquals(transactionId, capturedParameters.get(0).getValue());
	}
}
