SupportedDistinationQueryForAllServiceForRetalCustomer_query=SELECT PM. NAME \"paymentMethodName\",\
  DPM. ID \"paymentMethodType\", PM.\"TYPE\",PMT. NAME ,SPMS.API_CODE, SPMS.USER_TYPE \, \
  PM.PAYMETN_METHOD_ORDER , SPMS.VAILDATE_PM  FROM CUSTOMER C \
  JOIN DEST_PAYMENT_METHOD_CUST_PROF DPMC ON C.CUSTOMER_TYPE_ID = DPMC.CUSTOMER_TYPE_ID \
  JOIN PAYMENT_METHOD PM ON PM. ID = DPMC.PAYMENT_METHOD_ID \
  JOIN DESTINATION_PAYMENT_METHOD DPM ON DPM. ID = PM. ID \
  left join SERVICES_PAYMENTMETHOD  SPMS on  DPM.ID =SPMS.PAYMENMETHODTYPE \
  LEFT JOIN PAYMENT_METHOD_TYPE PMT ON PMT. ID = PM.\"TYPE\" \
  WHERE  C.USER_ID = ? \
  AND   SPMS.LOOKUP_ENUM = ?  AND  SPMS.USER_TYPE = ? 
SupportedDistinationQueryForAllServiceForRetalCustomer_condition1=AND   SPMS.API_CODE = ?
SupportedDistinationQueryForAllServiceForRetalCustomer_condition2=AND   SPMS.API_CODE in ( 
SupportedDistinationQueryForAllServiceForRetalCustomer_forLoop=? , 
SupportedDistinationQueryForAllServiceForRetalCustomer=? ) 






