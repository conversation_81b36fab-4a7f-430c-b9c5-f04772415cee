getBECustomerInfo=select ID ,CORPERATE_ID,PARENT_CUSTOMER_ID,MAIN_CUSTOMER_ID,ROLE \
  ,BE_CUSTOMER.STATUS from BE_CUSTOMER \
  LEFT JOIN CUSTOMER on (CUSTOMER.BE_CUSTOMER_ID = BE_CUSTOMER.ID) \
  WHERE CUSTOMER.USER_ID = ?
getRoleInfoAndAllowedService=select BEC.ROLE   ,br.NAME  ,API.CODE  from BE_CUSTOMER BEC \
  LEFT JOIN CUSTOMER  CU on (CU.BE_CUSTOMER_ID = BEC.ID) \
  LEFT JOIN  BE_ROLE br on (br.ID = BEC.ROLE)  AND  br.ROLE_TYPE= 0  AND br.WALLET_SHORT_CODE = ? \
  LEFT JOIN  BE_ROLE_ALLOWED_SERVICES ba ON   ba.ROLE_ID = br.ID \
  LEFT JOIN  SERVICE_CONFIG_MAP SC  ON   SC.SERVICE_TYPE_ID = ba.SERVICE_TYPE_ID \
  LEFT JOIN  API_VERICASH_APIS  API  ON    SC.SERVICE_CODE = API.SERVICE_CODE \
  WHERE CU.USER_ID =  ?    AND ba.ROLE_ID = BEC.ROLE 
getRoleInfoAndAllowedService_ifcondition1=and  ba.SERVICE_TYPE_ID = ?
getRoleInfoAndAllowedService_ifcondition2=AND   ba.SERVICE_TYPE_ID  in ( 
getRoleInfoAndAllowedService_forLoop=? , 
getRoleInfoAndAllowedService_forLoop2=?  ) 



