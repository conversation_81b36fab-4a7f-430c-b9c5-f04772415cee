<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

   <!-- Create PURCHASE_TYPE table--> 
    <changeSet id="create-purchase-type-table" author="fatma">
        <comment>Create PURCHASE_TYPE table with ID and NAME columns</comment>
        
        <createTable tableName="PURCHASE_TYPE" >
            <column name="ID" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="NAME" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!--Insert initial PURCHASE_TYPE data -->
    <changeSet id="insert-purchase-type-data" author="fatma">
        <comment>Insert initial PURCHASE_TYPE records</comment>
        
        <insert tableName="PURCHASE_TYPE" >
            <column name="ID" value="0"/>
            <column name="NAME" value="default currency"/>
        </insert>
        
        <insert tableName="PURCHASE_TYPE" >
            <column name="ID" value="1"/>
            <column name="NAME" value="points"/>
        </insert>
        
        <insert tableName="PURCHASE_TYPE" >
            <column name="ID" value="2"/>
            <column name="NAME" value="both"/>
        </insert>
    </changeSet>

    <!-- Add PURCHASE_TYPE_ID column to EVOUCHER_DEFINITION table -->
    <changeSet id="add-purchase-type-id-column" author="fatma">


        <addColumn tableName="EVOUCHER_DEFINITION">
            <column name="PURCHASE_TYPE_ID" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

	<changeSet id="update-evoucher-definition-purchase-type" author="fatma">
    <preConditions>
        <tableExists tableName="EVOUCHER_DEFINITION"/>
        <columnExists tableName="EVOUCHER_DEFINITION" columnName="PURCHASE_TYPE_ID"/>
    </preConditions>
    <comment>Set PURCHASE_TYPE_ID to 0 where it is null</comment>
    <update tableName="EVOUCHER_DEFINITION">
        <column name="PURCHASE_TYPE_ID" value="0"/>
        <where>PURCHASE_TYPE_ID IS NULL</where>
    </update>
</changeSet>


    <changeSet id="update-evoucher-types-query" author="fatma">
        <update tableName="PORTAL_LOOKUPS_CONFIG">
            <column name="LOOKUP_QUERY"
                    value="SELECT DISTINCT et.ID, et.NAME, CONCAT(TO_CHAR((SELECT LOOKUP_VALUE FROM GENERAL_LOOKUPS WHERE UPPER(LOOKUP_KEY) = 'Evoucher_Type_IMG_Url')), IMAGE_URL) FROM EVOUCHER_TYPE et JOIN EVOUCHER_DEFINITION ed ON ed.EVOUCHER_TYPE_CODE = et.ID WHERE ed.PURCHASE_TYPE_ID = ${filters.purchaseType} OR ed.PURCHASE_TYPE_ID = 2"/>
            <where>LOOKUP_KEY = 'get.evoucher.types'</where>
        </update>
    </changeSet>


	
	<changeSet id="insert-business-service-step-calculate-exchange-rate" author="fatma">

    
    <insert tableName="BUSINESS_SERVICE_STEP" schemaName="PRO_MI_NO_C">
        <column name="ID" valueComputed="(
            SELECT COALESCE(MAX(ID), 0) + 1 
            FROM PRO_MI_NO_C.BUSINESS_SERVICE_STEP
        )"/>
        <column name="DESCRIPTION" value="CalculateExchangeRate"/>
        <column name="IS_MIGRATED" valueNumeric="1"/>
        <column name="STEP_ORDER" valueNumeric="6"/>
        <column name="TYPE" valueNumeric="1"/>
        <column name="BUSINESS_SERVICE_CONFIG_ID" valueNumeric="1371978430"/>
        <column name="SERVICE" valueComputed="(
            SELECT ID 
            FROM PRO_MI_NO_C.SERVICE 
            WHERE SERVICE_STACK_ID='CalculateExchangeRate'
        )"/>
        <column name="TRANSACTION_DEF" value="NULL"/>
    </insert>
	</changeSet>
   
   
<changeSet id="update-BUSINESS_ENTITY_ID-for-points-trxs" author="fatma">

    <update tableName="TRANS_DEF_SUMMARY">
        <column name="BUSINESS_ENTITY_ID" value="2422"/>
        <where>TRANSACTION_NAME in ('LoyaltySVA_To_LoyaltyWallet', 'BusinessEntity_to_walletPoint', 'loyaltySva_to_businessEntity', 'POINTS_TO_POINTS')</where>
    </update>
</changeSet>




<changeSet id="update-purchase-type-names" author="fatmadarwish">
    <update tableName="PURCHASE_TYPE" >
        <column name="NAME" value="Money"/>
        <where>ID = 0</where>
    </update>
    
    <update tableName="PURCHASE_TYPE" >
        <column name="NAME" value="Points"/>
        <where>ID = 1</where>
    </update>
    
    <update tableName="PURCHASE_TYPE" >
        <column name="NAME" value="Both"/>
        <where>ID = 2</where>
    </update>
</changeSet>


<changeSet id="insert-business-service-step-calacfees" author="fatmadarwish">
    <insert tableName="BUSINESS_SERVICE_STEP" schemaName="PRO_MI_NO_C">
        <column name="ID" valueComputed="(
            SELECT COALESCE(MAX(ID), 0) + 1 
            FROM PRO_MI_NO_C.BUSINESS_SERVICE_STEP
        )"/>
        <column name="DESCRIPTION" value="Calculate Commission"/>
        <column name="IS_MIGRATED" valueNumeric="1"/>
        <column name="STEP_ORDER" valueNumeric="5"/>
        <column name="TYPE" valueNumeric="1"/>
        <column name="BUSINESS_SERVICE_CONFIG_ID" valueNumeric="1371978430"/>
        <column name="SERVICE" valueComputed="(
            SELECT ID 
            FROM PRO_MI_NO_C.SERVICE 
            WHERE SERVICE_STACK_ID='Calculate Commission'
        )"/>
        <column name="TRANSACTION_DEF" value="NULL"/>
    </insert>
</changeSet>


<changeSet id="insert-success-message-8082" author="fatmadarwish">
    <insert tableName="VERICASH_SUCCESS_MESSAGES">
        <column name="SERVICE_CODE" value="8082"/>
        <column name="SUCCESS_MESSAGE_EN" value="Voucher successfully  redeemed!"/>
        <column name="SUCCESS_MESSAGE_FR" value="Bon utilisé avec succès !"/>
        <column name="SUCCESS_MESSAGE_PT" value="Cupom resgatado com sucesso!"/>
    </insert>
</changeSet>
	
</databaseChangeLog>




