<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <xsd:complexType name="cardvalidatnreq">
        <xsd:sequence>
            <xsd:element name="AcctNum" type="xsd:string"/>
            <xsd:element name="CardNum" type="xsd:string"/>
            <xsd:element name="CardPIN" type="xsd:string"/>
            <xsd:element name="ExpiryDate" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:element xmlns:ibmSchExtn="http://www.ibm.com/schema/extensions" ibmSchExtn:docRoot="true" name="CardValidationRequest"
                 type="cardvalidatnreq">
        <xsd:annotation>
            <xsd:appinfo source="WMQI_APPINFO">
                <MRMessage messageDefinition="/0/CardValidationRequest;XSDElementDeclaration$MRObject"/>
            </xsd:appinfo>
        </xsd:annotation>
    </xsd:element>
    <xsd:complexType name="cardvalidatnres">
        <xsd:sequence>
            <xsd:element name="ResponseCode" type="xsd:string"/>
            <xsd:element name="AcctNum" type="xsd:string"/>
            <xsd:element name="CardNum" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="complexType">
        <xsd:sequence/>
    </xsd:complexType>
    <xsd:element xmlns:ibmSchExtn="http://www.ibm.com/schema/extensions" ibmSchExtn:docRoot="true" name="CardValidationResponse"
                 type="cardvalidatnres">
        <xsd:annotation>
            <xsd:appinfo source="WMQI_APPINFO">
                <MRMessage messageDefinition="/0/CardValidationResponse;XSDElementDeclaration=1$MRObject"/>
            </xsd:appinfo>
        </xsd:annotation>
    </xsd:element>
</xsd:schema>