package com.cit.vericash.apis.addvirtualcard;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.paymentmethod.SourcePaymentMethodDTO;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.addvirtualcard.AddVirtualCardResponseTransformer")
public class AddVirtualCardResponseTransformer implements MuleResponseTransformer {


    Map<String, Object> result = new HashMap<String, Object>();
    ObjectMapper mapper = new ObjectMapper();

    public Object transform(BusinessMessage businessMessage) throws Exception {

        SourcePaymentMethodDTO sourcePaymentMethod = businessMessage.getSourcePaymentMethod();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        sourcePaymentMethod.setDynamicGroup(null);
        //sourcePaymentMethod.setIntegrationType(Enums.IntegrationType.EXTERNAL);
        result.put("sourcePaymentMethod", sourcePaymentMethod);

     /*   String balance = businessMessage.getSourcePaymentMethod().getPaymentBalance();
        result.put("paymentBalance", balance);*/

        return result;
    }

}