package com.cit.vericash.config;

import com.cit.vericash.api.handler.VericashExceptionHandler;
import com.cit.vericash.apis.commons.model.SecurityParameters;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.context.annotation.*;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import com.cit.vericash.apis.security.crypto.EncryptionDecryptionUtil;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.web.reactive.result.view.ViewResolver;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Configuration
@ComponentScan(basePackages = {"com.cit.vericash.apis.integrationfremwork.logger","com.cit.vericash.apis.integrationfremwork.service"})
@EnableJpaRepositories(basePackages = {"com.cit.vericash.api.repository","com.cit.vericash.apis.integrationfremwork.repository"})
@EntityScan(basePackages = {"com.cit.vericash.api.entity","com.cit.vericash.apis.integrationfremwork.entity"})
public class ApiConfigurationComponent {


//	@Bean
//	public CacheableChannelConfig channelConfigCache() {
//		return new CacheableChannelConfigImpl();
//	}

//	@Bean
//	public CachableVericashApi vericashApiCache() {
//		return new CacheableVericashAPiImpl(new VericashApiLoader());
//	}
	
//	@Bean
//	public EndToEndComponent endToEndComponent() {
//		return new EndToEndComponent(new PropertyLoader(new ProjectConfigLoaderImpl()));
//
//	}
	
//	@Bean
//	public PropertyLoader propertyLoader() {
//		return new PropertyLoader(new ProjectConfigLoaderImpl());
//	}

	
//	@Bean
//	public ApiDocumentationService apiDocumentationService() {
//		return new ApiDocumentationService(new CacheableVericashAPiImpl(new VericashApiLoader()), new ProjectConfigLoaderImpl());
//		     //
//	}
//
//	@Bean
//	public CachableApiError cachableApiError() {
//		return new CacheableAPIErrorImpl();
//	}
    @Bean
    public Function<SecurityParameters, EncryptionDecryptionUtil> beanFactory() {
        return securityParameters -> createEncryptionDecryptionUtil(securityParameters);
    }

    @Bean
    @Scope(value = "prototype")
    public EncryptionDecryptionUtil createEncryptionDecryptionUtil(SecurityParameters securityParameters) {
        return new EncryptionDecryptionUtil(securityParameters);
    }

    @Primary
    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    public ErrorWebExceptionHandler errorWebExceptionHandler(ObjectProvider<List<ViewResolver>> viewResolversProvider,
                                                             ServerCodecConfigurer serverCodecConfigurer) {
        VericashExceptionHandler jsonExceptionHandler = new VericashExceptionHandler();
        jsonExceptionHandler.setViewResolvers(viewResolversProvider.getIfAvailable(Collections::emptyList));
        jsonExceptionHandler.setMessageWriters(serverCodecConfigurer.getWriters());
        jsonExceptionHandler.setMessageReaders(serverCodecConfigurer.getReaders());
        return jsonExceptionHandler;
    }
}
