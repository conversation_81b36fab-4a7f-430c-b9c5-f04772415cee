getOpenForSubscriptionList_condition1=SELECT rs.ID,rs.TOTAL_AMOUNT,rs.CURRENCY,\
  rs.SUBSCRIPTION_START_DATE,rs.SUBSCRIPTION_END_DATE, rs.CONTRIBUTION_AMOUNT,\
  rs.CONTRIBUTION_FREQUENCY,rsf.NAME AS RSF_NAME, rs.WINNING_TURN_DECISION,\
  wtd.NAME AS WTD_NAME,rs.END_DATE \
  FROM ROTATING_SAVINGS rs \
  INNER JOIN ROTATING_SAVINGS_FREQUENCY rsf ON rs.CONTRIBUTION_FREQUENCY = rsf.ID \
  INNER JOIN WINNING_TURN_DECISION wtd ON rs.WINNING_TURN_DECISION = wtd.ID \
  WHERE rs.STATUS=0 AND rs.SUBSCRIPTION_START_DATE <= CURRENT_TIMESTAMP \
  AND rs.SUBSCRIPTION_END_DATE >= CURRENT_TIMESTAMP AND ROWNUM <= %1$s \
  ORDER BY rs.SUBSCRIPTION_END_DATE ASC
getOpenForSubscriptionList_condition2=SELECT rs.ID,rs.TOTAL_AMOUNT,rs.CURRENCY,rs.SUBSCRIPTION_START_DATE,\
  rs.SUBSCRIPTION_END_DATE, rs.CONTRIBUTION_AMOUNT, rs.CONTRIBUTION_FREQUENCY,rsf.NAME AS RSF_NAME, \
  rs.WINNING_TURN_DECISION, wtd.NAME AS WTD_NAME, rs.END_DATE \
  FROM ROTATING_SAVINGS rs \
  INNER JOIN ROTATING_SAVINGS_FREQUENCY rsf ON rs.CONTRIBUTION_FREQUENCY = rsf.ID \
  INNER JOIN WINNING_TURN_DECISION wtd ON rs.WINNING_TURN_DECISION = wtd.ID \
  WHERE rs.STATUS=0 AND rs.SUBSCRIPTION_START_DATE <= CURRENT_TIMESTAMP AND \
  rs.SUBSCRIPTION_END_DATE >= CURRENT_TIMESTAMP ORDER BY rs.SUBSCRIPTION_END_DATE ASC