<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

   <changeSet id="add_massage for errors to test" author="fatma">
        <sql>



INSERT INTO "STATUS_CODES"  
("CODE","LANGUAGE","DESCRIPTION","EMAIL_ALERT","LOCAL_DESCRIPTION","SMS_ALERT","SHORT_DESCRIPTION","SOURCE","DISPLAY_HINT","WALLET_SHORT_CODE")

VALUES('VAL1567','en','testfatma','0','testfatma','0','testfatma','CustomerException','0','2020');





        </sql>
    </changeSet>
	
	
   <changeSet id="add_massage for errors to test gatway" author="fatma">
        <sql>



INSERT INTO "STATUS_CODES"  
("CODE","LANGUAGE","DESCRIPTION","EMAIL_ALERT","LOCAL_DESCRIPTION","SMS_ALERT","SHORT_DESCRIPTION","SOURCE","DISPLAY_HINT","WALLET_SHORT_CODE")

VALUES('VAL1564','en','testgetWay','0','testgetWay','0','testgetWay','CustomerException','0','2020');





        </sql>
    </changeSet>
</databaseChangeLog>