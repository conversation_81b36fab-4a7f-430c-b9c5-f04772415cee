package com.cit.vericash.apis.balanceinquiry;

import com.cit.mpaymentapp.common.customer.message.BalanceEnquiry;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.balanceinquiry.BalanceInquiryResponseTransformer")
public class BalanceInquiryResponseTransformer implements MuleResponseTransformer {
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    private String availableBalanceKey = "available_balance";
    private String numericBalanceKey = "numeric_balance";
    private String ledgerBalanceKey = "ledger_balance";
    private String numericLedgerBalanceKey = "numeric_ledger_balance";
    private String balanceCurrencyKey = "balance_currency";

    public Object transform(BusinessMessage businessMessage) throws Exception {
//	    cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();

        Map<String, Object> result = new HashMap<String, Object>();

        try {
            BalanceEnquiry balancInquiry = businessMessage.getPrimarySenderInfo().getPaymentMethod().getBalanceEnquiry();
            result.put(cacheableFieldParameters.getResponseParameters(availableBalanceKey), balancInquiry.getAvailableBalance());
            result.put(cacheableFieldParameters.getResponseParameters(numericBalanceKey), balancInquiry.getNumericBalance());
            result.put(cacheableFieldParameters.getResponseParameters(ledgerBalanceKey), balancInquiry.getLedgerBalance());
            result.put(cacheableFieldParameters.getResponseParameters(numericLedgerBalanceKey), balancInquiry.getNumericLedgerBalance());
            result.put(cacheableFieldParameters.getResponseParameters(balanceCurrencyKey), balancInquiry.getBalanceCurrency());

        } catch (Exception e) {
            throw e;
        }

        return result;
    }

}
