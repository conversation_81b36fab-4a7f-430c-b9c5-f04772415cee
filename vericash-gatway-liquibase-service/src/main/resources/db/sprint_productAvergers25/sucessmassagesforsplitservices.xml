<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet  author="fatma" id="add sucess massages">
       <insert tableName="VERICASH_SUCCESS_MESSAGES" schemaName="PRO_MI_NO_C">
        <column name="SERVICE_CODE" value="1993561"/>
        <column name="SUCCESS_MESSAGE_EN" value="Data retrieved successfully"/>
        <column name="SUCCESS_MESSAGE_FR" value="Données récupérées avec succès"/>
        <column name="SUCCESS_MESSAGE_PR" value="Dados recuperados com sucesso"/>
    </insert>
	
	     <insert tableName="VERICASH_SUCCESS_MESSAGES" schemaName="PRO_MI_NO_C">
        <column name="SERVICE_CODE" value="878899985"/>
        <column name="SUCCESS_MESSAGE_EN" value="The request is approved successfully"/>
        <column name="SUCCESS_MESSAGE_FR" value="La demande est approuvée avec succès"/>
        <column name="SUCCESS_MESSAGE_PR" value="A solicitação foi aprovada com sucesso"/>
    </insert>
	
	     <insert tableName="VERICASH_SUCCESS_MESSAGES" schemaName="PRO_MI_NO_C">
        <column name="SERVICE_CODE" value="23931"/>
        <column name="SUCCESS_MESSAGE_EN" value="your request is approved"/>
        <column name="SUCCESS_MESSAGE_FR" value="votre demande est approuvée"/>
        <column name="SUCCESS_MESSAGE_PR" value="sua solicitação foi aprovada"/>
    </insert>
	
	
</changeSet>


</databaseChangeLog>