getDynamicServiceGroupQuery=SELECT SD.SERVICE_TYPE_ID,GD.ID,GT.NAME,GD.NAME,GD.IS_CONTAINER ,GV.GROUP_VALIDATION_TYPE ,GV.CUSTOM_VALIDATION_BEAN \
FROM  INPUT_PARAM_GROUP_DEF  GD JOIN INPUT_PARAM GROUP_WALLET  GW ON GW.GROUP_DEF_ID = GD.ID JOIN INPUT_PARAM_SERVICE_GROUP SG ON SG.GROUP_WALLET_ID = GW.ID INPUT_PARAM_SERVICE_DEF SD \
  ON SD.ID = SG.SERVICE_DEF_ID JOIN INPUT_PARAM_GROUP_VALIDATION GV ON SG.ID =  GV.INPUT_PARAM_SERVICE_GROUP_ID  INPUT_PARAM_GROUP_TYPE GT \
  ON GT.ID = GD.GROUP_TYPE_ID \
  WHERE SD.SERVICE_TYPE_ID = %1$s AND  SD.WALLET_SHORT_CODE = %2$s AND GW.WALLET_SHORT_CODE = %2$s

