package com.cit.vericash.apis.inquirePaymentMethod;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.paymentmethod.SourcePaymentMethodDTO;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.inquirePaymentMethod.InquirePaymentMethodResponseTransformer")
public class InquirePaymentMethodResponseTransformer implements MuleResponseTransformer {
    private final String successMsgKey = "success_msg";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, String> result = new HashMap<String, String>();

        try {

            // result.put(cacheableFieldParameters.getResponseParameters(successMsgKey), "Success");
            //String cardbalance=businessMessage.getSoftFields().get('cardbalanceresponse');

            List<SourcePaymentMethodDTO> paymentMethodsList = businessMessage.getSourcePaymentMethods();
            ObjectMapper objectMapper = new ObjectMapper();

            String json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(paymentMethodsList);
            result.put("sourcePaymentMethodList", json);
            //result.put("cardbalance",cardbalance);
        } catch (Exception e) {
            throw e;
        }

        return result;
    }
}