package com.cit.vericash.apis.appRating;

import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertThrows;

public class AppRatingRequestTransformerTest {


    // Successfully calls appRating.submitAppRating with the provided request
    @Test
    public void test_process_calls_submit_app_rating_with_request() throws Exception {
        // Arrange
        AppRatingRequestTransformer transformer = new AppRatingRequestTransformer();
    
        AppRating mockAppRating = Mockito.mock(AppRating.class);
        ReflectionTestUtils.setField(transformer, "appRating", mockAppRating);
    
        Request request = new Request();
        Message message = new Message();
        request.setMessage(message);
    
        // Act
        transformer.process(request);
    
        // Assert
        Mockito.verify(mockAppRating).submitAppRating(request);
    }

}