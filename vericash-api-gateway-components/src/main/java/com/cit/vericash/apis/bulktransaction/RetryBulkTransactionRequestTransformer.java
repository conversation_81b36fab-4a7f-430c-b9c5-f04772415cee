package com.cit.vericash.apis.bulktransaction;

import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.calculatefee.GroupCalculateFeesRequestTransformer;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import static com.cit.vericash.apis.calculatefee.GroupCalculateFeesRequestTransformer.SERVICE_TYPE;

@Component
@Qualifier("com.cit.vericash.apis.bulktransaction.RetryBulkTransactionRequestTransformer")
@RequiredArgsConstructor
@Slf4j
public class RetryBulkTransactionRequestTransformer implements VericashAction {
    public static final String BULK_API_CODE = "bulkApiCode";
    private final GroupCalculateFeesRequestTransformer groupCalculateFeesRequestTransformer;
    private final PropertyLoaderComponent propertyLoaderComponent;

    @Override
    public Object process(Request request) throws Exception {
        log.info("Inside BulkTransactionRequestTransformer");
        request.getMessage().getPayload().put(SERVICE_TYPE, propertyLoaderComponent.getPropertyAsString(BULK_API_CODE));
        groupCalculateFeesRequestTransformer.process(request);
        return request;
    }
}