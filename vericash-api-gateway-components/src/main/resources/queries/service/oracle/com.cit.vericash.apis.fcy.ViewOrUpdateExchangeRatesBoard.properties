getAllCurrenciesConfig=SELECT c.ID,c.CODE,cc.BASE,cc.STATUS FROM CURRENCY c JOIN CURRENCY_CONFIG cc ON c.ID = cc.CURRENCY_ID WHERE c.CODE IS NOT NULL AND cc.BASE != 0 OR cc.STATUS != 0
truncateCurrencyRatesTable=Truncate TABLE CURRENCY_RATES
insertRatesintoCurrencyRatesTableStarter=INSERT ALL
getBaseCurrencyCode=SELECT CODE FROM CURRENCY c WHERE c.ID=?
getCustomerFavorites=SELECT CURRENCY_ID FROM FCY_CUSTOMER_FAVORITES WHERE CUSTOMER_ID=? ORDER BY ID ASC
getIsCurrencyAllowedBase=SELECT BASE FROM CURRENCY_CONFIG cc WHERE cc.CURRENCY_ID=?
deleteCustomerFavorites=DELETE FROM FCY_CUSTOMER_FAVORITES WHERE CUSTOMER_ID = ? AND CURRENCY_ID IN