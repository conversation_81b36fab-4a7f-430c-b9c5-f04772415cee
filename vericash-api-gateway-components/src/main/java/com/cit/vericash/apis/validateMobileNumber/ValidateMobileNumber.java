package com.cit.vericash.apis.validateMobileNumber;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.commons.util.pupulateSenderInfoUtil;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.validateMobileNumber.ValidateMobileNumber")
public class ValidateMobileNumber implements VericashAction {
    private final String MOBILE_NUMBER = "mobileNumber";
    private final String WALLET_CODE = "walletShortCode";
    @Autowired
    ConnectionUtil connectionUtil;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    @Autowired
    private pupulateSenderInfoUtil populateSenderInfoUtil;
    @Value("${validate.mobile.number.Error}")
    private String errorMessage;

    public Object process(Request request) throws Exception {
        Message message = request.getMessage();
//        message = populateSenderInfoUtil.getUserInformation(message);

        Map<String, String> result = new HashMap<String, String>();
        String mobileNumber = message.getPayload().getAttributeAsString(MOBILE_NUMBER);
        String walletShortCode = message.getHeader().getAttributeAsString(WALLET_CODE);
        if (mobileNumber == null || walletShortCode == null) {
            throw new Exception("Mobile Number and wallet short code cannot be empty");
        }


        String query = ServiceQueryEngine.getQueryStringToExecute("ValidateMobileNumber_process", this.getClass());
        List<Parameter> parameters = new ArrayList<Parameter>();
        List<String> fields = new ArrayList<String>();

        parameters.add(new Parameter(1, mobileNumber));
        parameters.add(new Parameter(2, walletShortCode));

        List<Record> records = connectionUtil.executeSelect(query, parameters, fields);
        if (records.size() > 0) {
            throw new Exception(errorMessage);
        }
        return result;
    }
}