package com.cit.vericash.apis.receiver_approval;

import com.cit.mpaymentapp.model.customer.approval.RECEIVER_REQUEST_APPROVAL_STATUS;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.api.components.impl.MuleMessageSenderImpl;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.util.RequestParsingUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReceiverApprovalService {
    public static final String REQUEST_ID = "REQUEST_ID";
    public static final String REQUEST_DATE = "REQUEST_DATE";
    public static final String EXPIRY_DATE = "EXPIRY_DATE";
    public static final String AMOUNT = "AMOUNT";
    public static final String FULL_NAME = "FULL_NAME";
    public static final String SERVICE_NAME = "SERVICE_NAME";
    public static final String RECEIVER_REQUEST_MESSAGE = "RECEIVER_REQUEST_MESSAGE";
    public static final String RECEIVER_REQUEST_TITLE = "RECEIVER_REQUEST_TITLE";
    public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss[.SSS]";
    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);
    private static final List<String> fields = new ArrayList<>();
    public static final String CUSTOMER_ID = "customerId";
    public static final String APPROVAL_REQUEST_ID = "approvalRequestId";
    public static final String APPROVE_OR_REJECT = "approveOrReject";
    public static final String MSISDN = "MSISDN";
    public static final String API_CODE = "API_CODE";
    public static final String STATUS_NAME = "STATUS_NAME";
    public static final String STATUS_CODE = "STATUS_CODE";
    public static final String DOT = ".";
    public static final String CORRELATION_ID = "CORRELATION_ID";
    public static final String OUTBOUND_QUEUE ="cashOut.approval.outbound.queue";
    private final ConnectionUtil connectionUtil;
    private final PropertyLoaderComponent propertyLoaderComponent;
    private final MuleMessageSenderImpl muleMessageSender;

    @PostConstruct
    public void init(){
        fields.add(REQUEST_ID);
        fields.add(REQUEST_DATE);
        fields.add(EXPIRY_DATE);
        fields.add(AMOUNT);
        fields.add(API_CODE);
        fields.add(STATUS_CODE);
        fields.add(STATUS_NAME);
        fields.add(MSISDN);
        fields.add(FULL_NAME);
        fields.add(CORRELATION_ID);
    }
    public ReceiverRequestApprovalDto getReceiverRequestApproval(Request request) throws Exception{
        Record record = loadRequestApproval(request);
        LocalDateTime expiryDate = checkExpiredAndUpdateStatus(record);
        String apiCode = record.get(API_CODE).toString();
        return ReceiverRequestApprovalDto.builder()
                .approvalRequestId(record.getValueAsLong(REQUEST_ID))
                .amount(record.getValueAsBigDecimal(AMOUNT))
                .serviceName(propertyLoaderComponent.getPropertyAsString(apiCode + DOT + SERVICE_NAME))
                .senderFullName(record.getValueAsString(FULL_NAME))
                .mobileNumber(record.getValueAsString(MSISDN))
                .message(propertyLoaderComponent.getPropertyAsString(apiCode + DOT + RECEIVER_REQUEST_MESSAGE))
                .title(propertyLoaderComponent.getPropertyAsString(apiCode + DOT + RECEIVER_REQUEST_TITLE))
                .statusCode(record.getValueAsBigDecimal(STATUS_CODE))
                .statusName(record.getValueAsString(STATUS_NAME))
                .creationDate(RequestParsingUtils.getValidDateTime(record.get(REQUEST_DATE).toString(), dateTimeFormatter, false))
                .expiryDate(expiryDate)
                .correlationId(record.getValueAsString(CORRELATION_ID))
                .build();

    }

    public Map<String, Object> updateReceiverRequestApproval(Request request) throws Exception{
        Record record = loadRequestApproval(request);
        checkExpiredAndUpdateStatus(record);
        boolean approveRequest = request.getMessage().getPayload().getAttributeAsBoolean(APPROVE_OR_REJECT);
        String approvalRequestId = request.getMessage().getPayload().getAttributeAsString(APPROVAL_REQUEST_ID);
        List<Parameter> parameters = new ArrayList<>();
        String baseQuery = ServiceQueryEngine.getQueryStringToExecute("respondToRequest", getClass());
        parameters.add(new Parameter(1, approveRequest ? RECEIVER_REQUEST_APPROVAL_STATUS.Approved.getId() : RECEIVER_REQUEST_APPROVAL_STATUS.Rejected.getId()));
        parameters.add(new Parameter(2, approvalRequestId));
        connectionUtil.executeUpdate(baseQuery, parameters);

        muleMessageSender.respondToQueue(approveRequest,record.get(CORRELATION_ID).toString(),propertyLoaderComponent.getPropertyAsString(OUTBOUND_QUEUE));
        return Map.of(APPROVAL_REQUEST_ID, record.getValueAsLong(REQUEST_ID));
    }

    public Record loadRequestApproval(Request request) {
        String approvalRequestId = request.getMessage().getPayload().getAttributeAsString(APPROVAL_REQUEST_ID);
        List<Record> records = connectionUtil.executeSelect(ServiceQueryEngine.getQueryStringToExecute("getReceiverRequestApproval", this.getClass()),
                List.of(new Parameter(1, approvalRequestId),
                        new Parameter(2, request.getMessage().getPayload().getAttributeAsLong(CUSTOMER_ID)),
                        new Parameter(3, RECEIVER_REQUEST_APPROVAL_STATUS.Initiated.getId())),
                fields);
        if (records == null || records.isEmpty()) {
            throw new APIException("Invalid approvalRequestId", ErrorCode.WRONG_APPROVAL_REQUEST_ID.getErrorCode());
        }
        return records.get(0);
    }

    public LocalDateTime checkExpiredAndUpdateStatus(Record record) {
        Object date = record.get(EXPIRY_DATE);
        if (date == null) {
            throw new APIException("Invalid date", ErrorCode.INVALID_Date.getErrorCode());
        }
        LocalDateTime expiryDate = RequestParsingUtils.getValidDateTime(date.toString(), dateTimeFormatter, false);
        if (expiryDate.isBefore(LocalDateTime.now())) {
            connectionUtil.executeUpdate(ServiceQueryEngine.getQueryStringToExecute("updateStatus", getClass()),
                    List.of(new Parameter(1, RECEIVER_REQUEST_APPROVAL_STATUS.Expired.getId()),
                            new Parameter(2, record.getValueAsLong(REQUEST_ID))));
            throw new APIException("Expired request", ErrorCode.REQUEST_EXPIRED.getErrorCode());
        }
        return expiryDate;
    }
}