groupName_if_condition=SELECT GROUP_TRANSFER_SETUP_ID,CUSTOMER_ID,IN_PROGRESS\
  ,NUM_OF_RECIPIENTS,TOTAL_AMOUNT,GROUP_NAME,CREATION_DATE,LAST_EXECUTION_DATE,\
  HAS_RECURRING,RECURRING_NAME,SERVICE_CODE,VAL<PERSON>ATION_STATUS FROM GROUP_TRANSFER_SETUP WHERE IS_DELETED != 1 AND CUSTOMER_ID= '%1$s' AND BULK_TYPE= '%2$s' ORDER BY GROUP_TRANSFER_SETUP_ID DESC 

groupName_else_condition=SELECT GROUP_TRANSFER_SETUP_ID,CUSTOMER_ID,IN_PROGRESS\
  ,NUM_OF_RECIPIENTS,TOTAL_AMOUNT,GROUP_NAME,CREATION_DATE,LAST_EXECUTION_DATE,\
  HAS_RECURRING,RECURRING_NAME,SERVICE_CODE,VALIDATION_STATUS FROM GROUP_TRANSFER_SETUP \
   WHERE  IS_DELETED != 1 AND CUSTOMER_ID= '%1$s' AND  GROUP_NAME LIKE %'%2$s'%\
   WHERE  IS_DELETED != 1 AND CUSTOMER_ID= '%3$s' AND BULK_TYPE= '%4$s' AND  GROUP_NAME LIKE %'%5$s'%