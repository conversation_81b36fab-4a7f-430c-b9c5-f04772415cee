package com.cit.vericash.apis.topupOperators;

import com.cit.mpaymentapp.common.lifestyle.message.LifestyleInfo;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.mpaymentapp.international.airtime.common.AirtimeProduct;
import com.cit.mpaymentapp.international.airtime.common.InternationalTopup;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.api.components.impl.MessageToBusinessMessageTransformerImpl;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;

@Component
@Qualifier("com.cit.vericash.apis.topupOperators.AirTimeTopupRequestTransformer")
public class AirTimeTopupRequestTransformer extends MessageToBusinessMessageTransformerImpl {

    @Override
    protected void enrichCustomerMessageWithCustomValues(Message message) throws Exception{
        Gson gson = new Gson();
        String airtimeProductString = gson.toJson(message.getPayload().getAttribute("airtimeProduct"));
        AirtimeProduct airtimeProductObj = gson.fromJson(airtimeProductString, AirtimeProduct.class);
        BigDecimal openRangeMin=(airtimeProductObj!=null &&airtimeProductObj.getOpenRangeMin()!=null)?airtimeProductObj.getOpenRangeMin():new BigDecimal(0);
        BigDecimal openRangeMax=(airtimeProductObj!=null&&airtimeProductObj.getOpenRangeMax()!=null)?airtimeProductObj.getOpenRangeMax():new BigDecimal(0);
        Object amountObj=message.getPayload().getAttribute("amount");
        BigDecimal amount=message.getPayload().getAttributeAsBigDecimal("amount");

        if (amountObj!=null&&amount!=null&&(
                (openRangeMin.intValue()!=0&&(amount.compareTo(openRangeMin) < 0)) ||(openRangeMax.intValue()!=0&&amount.compareTo(openRangeMax) > 0))
        ) {
            //throw new GeneralFailureException(GeneralFailureException.INVALID_RANGE_AMOUNT);
            throw new APIException("INVALID RANGE AMOUNT : ", ErrorCode.INVALID_RANGE_AMOUNT.getErrorCode());

        }

    }
}
