<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
		xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
		xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
   http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">


   <changeSet failOnError="true" author="Mohamed Orabi" id="edit error message for pin authentication">
    <sql>
        -- Auto-generated SQL script #202402240005
        UPDATE API_ERROR x
        SET x.ERROR_DESCRIPTION='Invalid PIN'
        WHERE x.ERROR_CODE='API_0550';

    </sql>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="edit error message for pin receiver authentication">
        <sql>
            UPDATE API_ERROR x
            SET x.ERROR_DESCRIPTION='Invalid Receiver PIN'
            WHERE x.ERROR_CODE='API_0555';

        </sql>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="insert error message for otp receiver authentication">
        <sql>
            -- Auto-generated SQL script #202402251513
            INSERT INTO API_ERROR (ERROR_CODE,ERROR_DESCRIPTION)
            VALUES ('API_0556','Invalid Receiver OTP Authentication');


        </sql>
    </changeSet>
	

	
	
	
</databaseChangeLog>
	
