package com.cit.vericash.apis.rejectMoneyRequest;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;


@Component
@Qualifier("com.cit.vericash.apis.rejectMoneyRequest.RejectMoneyRequestResponseTransformer")
public class RejectMoneyRequestResponseTransformer implements MuleResponseTransformer {


    public static final String REJECT_OR_BLOCK_MESSAGE = "rejectOrBlockMessage";
    public static final String USER_BLOCKED_SUCCESSFULLY = "User Blocked Successfully";
    public static final String REJECTED_REQUEST_SUCCESSFULLY = "rejected request Successfully";
    public static final String BLOCK_AND_REJECT = "blockAndReject";

    public Object transform(BusinessMessage businessMessage) throws Exception {
        //${businessMessage.getSoftFields().get("rejectOrBlockMessage")} put this in the success message in the DB
        String result;
        Integer isBlock = (Integer) businessMessage.getParameters().get(BLOCK_AND_REJECT);
        if (isBlock == 1){
            businessMessage.getSoftFields().put(REJECT_OR_BLOCK_MESSAGE, USER_BLOCKED_SUCCESSFULLY);
            result = USER_BLOCKED_SUCCESSFULLY;
        }else {
            businessMessage.getSoftFields().put(REJECT_OR_BLOCK_MESSAGE, REJECTED_REQUEST_SUCCESSFULLY);
            result = REJECTED_REQUEST_SUCCESSFULLY;
        }
        return result;
    }


}