package com.cit.vericash.billpayment;

import com.cit.mpaymentapp.mongo.beans.Bill;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;


public class BillerPerCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    private String billerId;
    private String categoryId;
    private Set<Bill> billsList;
    private String billerName;
    private Map<String, String> attributes;
    private String iconBiller;

    public String getBillerId() {
        return billerId;
    }

    public void setBillerId(String billerId) {
        this.billerId = billerId;
    }

    public String getBillerName() {
        return billerName;
    }

    public void setBillerName(String billerName) {
        this.billerName = billerName;
    }

    public Map<String, String> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<String, String> attributes) {
        this.attributes = attributes;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public Set<Bill> getBillsList() {
        return billsList;
    }

    public void setBillsList(Set<Bill> billsList) {
        this.billsList = billsList;
    }

    public String getIconBiller() {
        return iconBiller;
    }

    public void setIconBiller(String iconBiller) {
        this.iconBiller = iconBiller;
    }

    @Override
    public String toString() {
        return "BillerPerCategory [billerId=" + billerId + ", categoryId=" + categoryId + ", billerName=" + billerName
                + ", attributes=" + attributes + ", iconBiller=" + iconBiller + "]";
    }
}
