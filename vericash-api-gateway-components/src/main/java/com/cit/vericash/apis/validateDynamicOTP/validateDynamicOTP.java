package com.cit.vericash.apis.validateDynamicOTP;

import com.cit.vericash.api.components.multiple.authentication.OTPAuthentication;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.PropertyLoaderComponent;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.security.model.AuthenticationParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Qualifier("com.cit.vericash.apis.validateDynamicOTP.validateDynamicOTP")
public class validateDynamicOTP implements VericashAction {
    @Autowired
    OTPAuthentication otpAuthentication;

    @Autowired
    ConnectionUtil connectionUtil;

    @Autowired
    PropertyLoaderComponent propertyLoader;

    @Override
    public Object process(Request request) throws Exception {
        String otp = request.getMessage().getPayload().getAttributeAsString("otp");
        String msidin = request.getMessage().getPayload().getAttributeAsString("msisdnToValidate");
        Long apiCode = request.getMessage().getPayload().getAttributeAsLong("apiCodeOTP");
        request.getMessage().getPayload().setAttribute("senderMSISDN", msidin);
        request.getMessage().getHeader().setAttribute("apiCode", apiCode);
        AuthenticationParam authenticationParam = getAuthenticationParam(otp);
        boolean valid = getEncOTP(request, authenticationParam);
        if (!valid) {
            throw new Exception(ErrorCode.INVALID_OTP.getErrorCode());
        }
        return null;
    }

    private Boolean getEncOTP(Request request, AuthenticationParam authenticationParam) throws Exception {
        try {
            Message message = request.getMessage();
            return otpAuthentication.validate(message, authenticationParam);
        } catch (APIException e) {
            throw new Exception(ErrorCode.INVALID_OTP.getErrorCode());
        }
    }

    private AuthenticationParam getAuthenticationParam(String otp) {
        AuthenticationParam authenticationParam = new AuthenticationParam();
        authenticationParam.setAuthCode("0");
        authenticationParam.setAuthOrder(1);
        authenticationParam.setAuthValue(Integer.parseInt(otp));
        return authenticationParam;
    }


}

