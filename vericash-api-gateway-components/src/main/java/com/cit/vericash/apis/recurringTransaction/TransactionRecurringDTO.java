package com.cit.vericash.apis.recurringTransaction;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.recurringTransaction.TransactionRecurringDTO")
public class TransactionRecurringDTO {
    private Long id;
    private String title;
    private String type;
    private String expiry;
    private String startDate;
    private Object expiryValue;
    private String frequency;
    private String status;
    private Map<Object, Object> details;
    private String[] allowedActions;


    public TransactionRecurringDTO() {
    }


    @Override
    public String toString() {
        return "TransactionRecurringDTO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", type='" + type + '\'' +
                ", expiry='" + expiry + '\'' +
                ", startDate='" + startDate + '\'' +
                ", expiryValue=" + expiryValue +
                ", frequency='" + frequency + '\'' +
                ", status='" + status + '\'' +
                ", allowedActions=" + Arrays.toString(allowedActions) +
                '}';
    }

    public Map<Object, Object> getDetails() {
        return details;
    }

    public void setDetails(Map<Object, Object> details) {
        this.details = details;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String[] getAllowedActions() {
        return allowedActions;
    }

    public void setAllowedActions(String[] allowedActions) {
        this.allowedActions = allowedActions;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getExpiry() {
        return expiry;
    }

    public void setExpiry(String expiry) {
        this.expiry = expiry;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public Object getExpiryValue() {
        return expiryValue;
    }

    public void setExpiryValue(Object expiryValue) {
        this.expiryValue = expiryValue;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}