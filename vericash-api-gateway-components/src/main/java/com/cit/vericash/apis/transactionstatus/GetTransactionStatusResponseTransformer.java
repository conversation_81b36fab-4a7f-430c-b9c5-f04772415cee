package com.cit.vericash.apis.transactionstatus;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.transactionstatus.GetTransactionStatusResponseTransformer")
public class GetTransactionStatusResponseTransformer implements MuleResponseTransformer {
    private final String TRANSACTION_STATUS = "transaction-status";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
        String transactionStatusKey = cacheableFieldParameters.getResponseParameters(TRANSACTION_STATUS);

        Map<String, String> result = new HashMap<String, String>();
        result.put(transactionStatusKey, businessMessage.getSoftFields().get("trxStatus").toString());
        return result;
    }


}
