package com.cit.vericash.apis.updateCardAlias;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.TreeMap;

@Component
@Qualifier("com.cit.vericash.apis.updateCardAlias.UpdateCardAliasResponseTransformer")
public class UpdateCardAliasResponseTransformer implements MuleResponseTransformer {


    public Object transform(BusinessMessage businessMessage) throws Exception {

        Map<String, Object> result = new TreeMap<String, Object>();

        return result;
    }
}


