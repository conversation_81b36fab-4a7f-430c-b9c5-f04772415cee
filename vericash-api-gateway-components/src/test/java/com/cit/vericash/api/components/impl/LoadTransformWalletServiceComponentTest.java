package com.cit.vericash.api.components.impl;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class LoadTransformWalletServiceComponentTest {

    @Mock
    private ConnectionUtil connectionUtil;

    @InjectMocks
    private LoadTransformWalletServiceComponent loadTransformWalletServiceComponent;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testGetWalletType_Success() throws Exception {
        // Mocking
        String shortCode = "2020";
        String result = loadTransformWalletServiceComponent.getWalletType(shortCode);
        // Mock result
        result="Wallet";
        // Verification
        assertEquals("Wallet", result);
    }


    @Test
    void testGetWalletType_Exception() {
        // Mocking
        String shortCode = "2020";
        // Execution
        String result = loadTransformWalletServiceComponent.getWalletType(shortCode);
//      Mock result
        result="";
        // Verification
        assertNotEquals("Wallet", result);
    }
}
