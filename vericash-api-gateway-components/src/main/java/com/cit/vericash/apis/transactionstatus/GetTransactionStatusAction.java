package com.cit.vericash.apis.transactionstatus;

import com.cit.mpaymentapp.model.transaction.TransactionStatus;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.exceptions.QuickActionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.transactionstatus.GetTransactionStatusAction")
public class GetTransactionStatusAction implements VericashAction {
    @Autowired
    ConnectionUtil connectionUtil;

    public Object process(Request request) throws Exception {
        Map<String, String> result = new HashMap<String, String>();
        Long transactionId = request.getMessage().getPayload().getAttributeAsLong("transactionId");
        String transactionStatusAsString = null;
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getTransactionAction", this.getClass());

        List<Parameter> parameters = new ArrayList<Parameter>();
        List<String> fields = new ArrayList<String>();
        fields.add("TRANSACTION_STATUS");
        parameters.add(new Parameter(1, transactionId));

        List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters, fields);
        if (records.size() != 0) {
            int transactionStatus = records.get(0).getValueAsBigDecimal("TRANSACTION_STATUS").intValue();
            transactionStatusAsString = TransactionStatus.values()[transactionStatus].toString();
            result.put("transactionStatus", transactionStatusAsString);
        } else {
            throw new QuickActionException("Invalid Transaction ID", ErrorCode.INVALID_TRANSACTION_ID.getErrorCode());
        }
        return result;
    }


}
