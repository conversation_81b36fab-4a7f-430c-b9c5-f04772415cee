package com.cit.vericash.apis.loadPMsAccordingToSelectedPM;

import com.cit.service.commons.codeMapping.*;
import com.cit.vericash.api.entity.ApiVericashApi;
import com.cit.vericash.api.repository.ApiVericashApiRepository;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.dto.request.Request;
import org.apache.commons.jxpath.JXPathContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

@Component
@Qualifier("com.cit.vericash.apis.loadPMsAccordingToSelectedPM.loadPMsAccordingToSelectedPMRequestTransformer")
public class loadPMsAccordingToSelectedPMRequestTransformer  implements VericashAction {

    @Autowired
    ServiceCodeMappingCache serviceCodeMappingCache;

    private final String SOURCE_PM_XML_PATH="/payload/sourcePaymentMethod/paymentMethodType";
    private final String DESTINATION_PM_XML_PATH="/payload/destinationPaymentMethod/paymentMethodType";

    @Autowired
    ApiVericashApiRepository apiVericashApiRepository;

    @Override
    public Object process(Request request) throws Exception {
        String xmlPath=GetXmlPath(request);
        if(!xmlPath.isEmpty()){
            String apiCode=request.getMessage().getPayload().get("serviceCode").toString();
            String serviceCode= getServiceCodeFromAPICode(apiCode);
            String PmCode=request.getMessage().getPayload().get("pmCode").toString();
            String targetXmlPath=request.getMessage().getPayload().get("targetXmlPath").toString();
            List<Parameters> combinations= LoadServiceCodeMapping(serviceCode,xmlPath,PmCode);
            return getPMCombination(combinations,targetXmlPath);
        }
        else {
            throw new APIException("MISSING MANDATORY PARAMETER", ErrorCode.PARAMETER_VALUE_IS_MANDATORY);
        }
    }
    private String getServiceCodeFromAPICode(String apiCode) throws Exception {
        if(apiCode.isEmpty())
            throw new APIException("MISSING MANDATORY PARAMETER", ErrorCode.PARAMETER_VALUE_IS_MANDATORY);

        Optional<ApiVericashApi> apiVericashApi=apiVericashApiRepository.findByCode(apiCode);
        return apiVericashApi.orElseThrow(() -> new APIException
                ("Invalid Api Code", ErrorCode.INVALID_API_CODE.getErrorCode())).getServiceCode();
    }

    private String GetXmlPath(Request request){
        Object sourcePaymentMethod =request.getMessage().getPayload().get("sourcePaymentMethod");
        if(sourcePaymentMethod!=null && ((HashMap) sourcePaymentMethod).containsKey("paymentMethodType")&& ((HashMap) sourcePaymentMethod).get("paymentMethodType")!=null){
            String sourcePaymentMethodCode= ((HashMap) sourcePaymentMethod).get("paymentMethodType").toString();
            request.getMessage().getPayload().setAttribute("pmCode",sourcePaymentMethodCode);
            request.getMessage().getPayload().setAttribute("targetXmlPath",DESTINATION_PM_XML_PATH);
            return SOURCE_PM_XML_PATH;
        }
        Object destinationPaymentMethod =request.getMessage().getPayload().get("destinationPaymentMethod");
        if(destinationPaymentMethod!=null && ((HashMap) destinationPaymentMethod).containsKey("paymentMethodType")&& ((HashMap) destinationPaymentMethod).get("paymentMethodType")!=null){
            String destinationPaymentMethodCode= ((HashMap) destinationPaymentMethod).get("paymentMethodType").toString();
            request.getMessage().getPayload().setAttribute("pmCode",destinationPaymentMethodCode);
            request.getMessage().getPayload().setAttribute("targetXmlPath",SOURCE_PM_XML_PATH);
            return DESTINATION_PM_XML_PATH;
        }
        return "";
    }

    public List<Parameters> LoadServiceCodeMapping(String serviceCode, String xmlPath, String PmCode) throws Exception {
        String filePath = this.serviceCodeMappingCache.getPath();
        String fileName = filePath + File.separator + serviceCode + ".xml";
        File oldFile = new File(fileName);
        if (!oldFile.exists()) {
            return null;
        } else {
            Long lastModified = oldFile.lastModified();
            ServiceCodeMappingCache var10000 = this.serviceCodeMappingCache;
            Long currentLastModified = (Long)ServiceCodeMappingCache.currentLastModifiedHM.get(serviceCode);
            if (!lastModified.equals(currentLastModified) || serviceCode.isEmpty() || serviceCode == null) {
                this.serviceCodeMappingCache.unmarshalXmlFile(serviceCode);
                var10000 = this.serviceCodeMappingCache;
                ServiceCodeMappingCache.currentLastModifiedHM.put(serviceCode, lastModified);
            }

            var10000 = this.serviceCodeMappingCache;
            if (ServiceCodeMappingCache.serviceCodeMap.get(serviceCode) == null) {
                return new ArrayList<>();
            } else {
                var10000 = this.serviceCodeMappingCache;
                ServiceCodeMapping serviceCodeMapping = (ServiceCodeMapping)ServiceCodeMappingCache.serviceCodeMap.get(serviceCode);
                List<Parameters> combinations = this.GetAllParametersCombination(serviceCodeMapping, xmlPath,PmCode);
                return combinations;
            }
        }
    }
    private List<Parameters> GetAllParametersCombination(ServiceCodeMapping serviceCodeMapping, String xmlPath, String messageValue) {
        List<Parameters> parametersListResult=new ArrayList<>();
        JXPathContext serviceCodeMappingObject = JXPathContext.newContext(serviceCodeMapping);
        List<Parameters> parametersList = (List)serviceCodeMappingObject.getValue("parameters");
        boolean matched = true;
        boolean parameterValueMatched = true;

        for(int i = 0; i < parametersList.size(); ++i) {
            matched = true;
            parameterValueMatched = true;
            Parameters parameters = (Parameters)parametersList.get(i);

            for(int j = 0; j < parameters.getParam().size(); ++j) {
                Param param = (Param)parameters.getParam().get(j);
                if(!param.getPath().equals(xmlPath)){
                    continue;
                }
                String xmlValue = param.getValue();
                if (param.getNotNull()) {
                    if (messageValue == null) {
                        parameterValueMatched = false;
                    }
                } else {
                    if (!xmlValue.equals(messageValue)) {
                        parameterValueMatched = false;
                    }
                }
                matched = matched && parameterValueMatched;
            }
            if (matched) {
                parametersListResult.add(parametersList.get(i));
            }
        }

        return parametersListResult;
    }

    public ArrayList<String> getPMCombination(List<Parameters> combinations,String targetXmlPath){
        Set<String> PMCombinations=new HashSet<>();
        for(int i = 0; i < combinations.size(); ++i) {
            Parameters parameters = (Parameters)combinations.get(i);
            for(int j = 0; j < parameters.getParam().size(); ++j) {
                Param param = (Param)parameters.getParam().get(j);
                if(!param.getPath().equals(targetXmlPath)){
                    continue;
                }
                String xmlValue = param.getValue();
                PMCombinations.add(xmlValue);
            }
        }
        ArrayList<String> mainList=new ArrayList<String>();
        mainList.addAll(PMCombinations);
        return mainList;
    }

}
