/*
package com.cit.vericash.apis.RotatingSavings;

import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
@JsonSerialize(
        include = JsonSerialize.Inclusion.NON_NULL
)
public class DestinationPaymentMethod implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long paymentMethodCode;
    private String paymentMethodType;

    public DestinationPaymentMethod() {
    }

    public Long getPaymentMethodCode() {
        return paymentMethodCode;
    }

    public void setPaymentMethodCode(Long paymentMethodCode) {
        this.paymentMethodCode = paymentMethodCode;
    }

    public String getPaymentMethodType() {
        return paymentMethodType;
    }

    public void setPaymentMethodType(String paymentMethodType) {
        this.paymentMethodType = paymentMethodType;
    }

    @Override
    public String toString() {
        return "DestinationPaymentMethod{" +
                "paymentMethodCode=" + paymentMethodCode +
                ", paymentMethodType='" + paymentMethodType + '\'' +
                '}';
    }
}
*/
