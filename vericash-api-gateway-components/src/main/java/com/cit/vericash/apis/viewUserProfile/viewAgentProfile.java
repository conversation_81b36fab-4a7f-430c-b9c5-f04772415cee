package com.cit.vericash.apis.viewUserProfile;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.api.VericashAction;
import com.cit.vericash.apis.commons.exceptions.APIException;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.commons.util.pupulateSenderInfoUtil;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.apis.dto.request.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.viewUserProfile.viewAgentProfile")
public class viewAgentProfile implements VericashAction {
    private final String USERNAME = "sender_username";
    private final String BusinessEntityName = "business_Entity_Name";
    private final String Country = "Country";
    private final String City = "City";
    private final String CorporateName = "Corporate_Name";
    private final String RegistraionNumber = "Registration_Number";
    private final String ParentBusinessEntity = "Parent_Business_Entity";
    private final String WalletIdentifier = "Wallet_Identifier";
    private final String Msisdn = "MSISDN";
    private final String State = "State";
    private final String type = "type";
    private final String TYPEENUM = "type_Enum";
    private final String ISOCode = "iso_code";
    private final String STATECODE = "state_Code";
    @Autowired
    ConnectionUtil connectionUtil;
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;
    @Autowired
    private pupulateSenderInfoUtil populateSenderInfoUtil;

    public Object process(Request request) throws Exception {
        Message message = request.getMessage();
//		message = populateSenderInfoUtil.getUserInformation(message);

        Map<String, String> result = new HashMap<String, String>();
//		cacheableFieldParameters=CacheableFieldParametersImpl.getInstance();
        String userName = cacheableFieldParameters.getRequestParameters(USERNAME);
        String name = message.getPayload().getAttributeAsString(userName);
        String transactionStatusAsString = null;

        StringBuilder query = new StringBuilder(ServiceQueryEngine.getQueryStringToExecute("viewAgentProfile_process",this.getClass()));
        List<Parameter> parameters = new ArrayList<Parameter>();
        List<String> fields = new ArrayList<String>();
        fields.add("NAME");
        fields.add("MOBILE_PHONE");
        fields.add("BUSINESS_ENTITY_NAME");
        fields.add("COUNTRY");
        fields.add("CITY");
        fields.add("parent");
        fields.add("CORPORATE_NAME");
        fields.add("REGISTRATION_NUMBER");
        fields.add("ISO_CODE");
        fields.add("TypeEnum");
        fields.add("stataCode");
        fields.add("HM");
        fields.add("Type");

        parameters.add(new Parameter(1, name));

        List<Record> records = connectionUtil.executeSelect(query.toString(), parameters, fields);
        if (records.size() == 0) {
            throw new APIException("AGENT_IS_NOT_FOUND", ErrorCode.AGENT_IS_NOT_FOUND.getErrorCode());
        }
        //if (records.size() != 0) {
        //int transactionStatus = records.get(0).getValueAsBigDecimal("TRANSACTION_STATUS").intValue();
        //transactionStatusAsString=TransactionStatus.values()[transactionStatus].toString();
        String businessEntityName = records.get(0).getValueAsString("BUSINESS_ENTITY_NAME");
        String country = records.get(0).getValueAsString("COUNTRY");
        String city = records.get(0).getValueAsString("CITY");
        String typeBE = records.get(0).getValueAsString("Type");
        String corprateName = records.get(0).getValueAsString("CORPORATE_NAME");
        String registerNumber = records.get(0).getValueAsString("REGISTRATION_NUMBER");
        String businessEntityParent = records.get(0).getValueAsString("parent");
        String walletIdetifier = records.get(0).getValueAsString("HM");
        String msisdn = records.get(0).getValueAsString("MOBILE_PHONE");
        String state = records.get(0).getValueAsString("NAME");
        String stateCode = (BigDecimal) records.get(0).getValueAsBigDecimal("stataCode") == null ? ""
                : ((BigDecimal) records.get(0).getValueAsBigDecimal("stataCode")).toString();

        String countryIsoCode = records.get(0).getValueAsString("ISO_CODE");
        String enumTypeBE = (BigDecimal) records.get(0).getValueAsBigDecimal("TypeEnum") == null ? ""
                : ((BigDecimal) records.get(0).getValueAsBigDecimal("TypeEnum")).toString();

        result.put(cacheableFieldParameters.getResponseParameters(BusinessEntityName), businessEntityName);
        result.put(cacheableFieldParameters.getResponseParameters(Country), country);
        result.put(cacheableFieldParameters.getResponseParameters(ISOCode), countryIsoCode);
        result.put(cacheableFieldParameters.getResponseParameters(City), city);
        result.put(cacheableFieldParameters.getResponseParameters(type), typeBE);
        result.put(cacheableFieldParameters.getResponseParameters(TYPEENUM), enumTypeBE);
        result.put(cacheableFieldParameters.getResponseParameters(CorporateName), corprateName);
        result.put(cacheableFieldParameters.getResponseParameters(RegistraionNumber), registerNumber);
        result.put(cacheableFieldParameters.getResponseParameters(ParentBusinessEntity), businessEntityParent);
        result.put(cacheableFieldParameters.getResponseParameters(WalletIdentifier), walletIdetifier);
        result.put(cacheableFieldParameters.getResponseParameters(Msisdn), msisdn);
        result.put(cacheableFieldParameters.getResponseParameters(State), state);
        result.put(cacheableFieldParameters.getResponseParameters(STATECODE), stateCode);

        //}

        return result;


    }


}