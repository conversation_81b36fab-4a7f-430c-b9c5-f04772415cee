package com.cit.vericash.apis.EnquireBankAccount;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ExternalIntegrationResponse;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.EnquireBankAccount.EnquireBankAccountResponseTransformer")

public class EnquireBankAccountResponseTransformer implements MuleResponseTransformer {
    @Override
    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        Map<String, Object> successScreenData = new HashMap<String, Object>();
        if (businessMessage.getExternalIntegrationResponse() != null) {
            ExternalIntegrationResponse externalIntegrationResponse = businessMessage.getExternalIntegrationResponse();
            successScreenData.put("First Name", externalIntegrationResponse.getFirstName());
            successScreenData.put("Middle Name", externalIntegrationResponse.getMiddleName());
            successScreenData.put("Last Name", externalIntegrationResponse.getLastName());
            successScreenData.put("Customer Mobile Number", externalIntegrationResponse.getPhoneNumber());
            successScreenData.put("Account Number", businessMessage.getParameters().getAttributeAsString("accountNumber"));
            successScreenData.put("Country", externalIntegrationResponse.getCountryCode());
            successScreenData.put("Currency", externalIntegrationResponse.getCurrency());
            successScreenData.put("paymentMethodType", businessMessage.getSourcePaymentMethod().getPaymentMethodType());
            result.put("successScreenData", successScreenData);
        }


        return result;
    }
}
