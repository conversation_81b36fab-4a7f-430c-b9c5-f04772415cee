<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet failOnError="false" author="Abubakr" id="add LOOKUP_FRAMEWORK_CONFIG RecordOf BlockedUsersLockup">
        <sql>
			INSERT INTO LOOKUP_FRAMEWORK_CONFIG (ID, CACHING_KEY_PATTERN, CACHING_TYPE, IS_CACHED_LOOKUP, LOOKUP_NAME, LOOKUP_TYPE, RESPONSE_TRANSFORMER_MAPPING, CUSTOME_RESPONSE_TRANSFORMER, CUSTOM_TRANSFORMER_CONFIG_FILE, ENDPOINT_NAME, SERVICE_NAME) VALUES((select max(ID) +1 from LOOKUP_FRAMEWORK_CONFIG), '0', 0, 0, 'moneyRequest.viewBlockedUsers', 0, NULL, 'moneyRequestGetBlockedUsers', NULL, NULL, NULL);
		</sql>
    </changeSet>
	
	<changeSet failOnError="false" author="Abubakr" id="add PORTAL_LOOKUPS_CONFIG BlockedUsersLockup">
		<sql>
			INSERT INTO PORTAL_LOOKUPS_CONFIG (ID, LOOKUP_NAME, LOOKUP_KEY, LOOKUP_QUERY, CACHEABLE) VALUES((select max(ID) +1 from PORTAL_LOOKUPS_CONFIG),' Request Money View Blocked Users', 'moneyRequest.viewBlockedUsers','SELECT c.CUSTOMER_ID AS id, MRBS.ID AS blockRecordId, c.FIRST_NAME, c.MIDDLE_NAME, c.LAST_NAME, c.MSISDN AS mobileNumber, MRBS."TYPE" AS requestType FROM MONEY_REQUEST_BLOCK_SENDER MRBS INNER JOIN CUSTOMER c ON c.CUSTOMER_ID = MRBS.BLOCKED_USER_ID WHERE MRBS.USER_ID IN (SELECT ct.CUSTOMER_ID FROM CUSTOMER ct WHERE ct.USER_ID = ${filters.getAttributeAsString(''userId'')})', 0);
		</sql>
	</changeSet>


</databaseChangeLog>