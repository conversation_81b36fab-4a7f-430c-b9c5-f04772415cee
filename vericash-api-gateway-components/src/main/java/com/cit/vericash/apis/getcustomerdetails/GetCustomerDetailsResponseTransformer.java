package com.cit.vericash.apis.getcustomerdetails;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.PersonalDetails;
import com.cit.mpaymentapp.model.Address;
import com.cit.mpaymentapp.model.Enums;
import com.cit.mpaymentapp.model.customer.CustomerDocument;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.commons.exceptions.ErrorCode;
import com.cit.vericash.apis.commons.exceptions.QuickActionException;
import com.cit.vericash.apis.commons.util.CacheableFieldParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Component
@Qualifier("com.cit.vericash.apis.getcustomerdetails.GetCustomerDetailsResponseTransformer")
public class GetCustomerDetailsResponseTransformer implements MuleResponseTransformer {
    private final String CUSTOMER_ID = "customer_Id";
    private final String FULL_NAME = "full_Name";
    private final String PHONE = "phone";
    private final String EMAIL = "email";
    private final String DATE_OF_BIRTH = "DateOfBirth";
    private final String NATIONALITY = "nationality";
    private final String GENDER = "gender";
    private final String ADDRESS = "address";
    private final String ID_TYPE = "idType";
    private final String ID_NUMBER = "idNumber";
    private final String CITY = "City";
    private final String COUNTRY = "Country";
    private final String REGISTRATION_DATE = "registrationDate";
    private final String REGISTRATION_AGENT = "registrationAgent";
    private final String STATUS = "status";
    private final String SUBSCRIBER_PROFILE_CATEGORY = "subscriberProfileCategory";
    private final String ID_TYPE_CODE = "id_Type_Enum";//unify Enum
    private final String GENDERCODE = "gender_Enum";
    private final String STATUSCODE = "status_Enum";
    private final String NATIONALITYCODE = "nationality_Enum";
    private final String ISOCode = "iso_code";
    @Autowired
    private CacheableFieldParameters cacheableFieldParameters;

    public Object transform(BusinessMessage businessMessage) throws Exception {
//        cacheableFieldParameters = CacheableFieldParametersImpl.getInstance();
        String customerIdKey = cacheableFieldParameters.getResponseParameters(CUSTOMER_ID);
        String fullNameKey = cacheableFieldParameters.getResponseParameters(FULL_NAME);
        String phoneKey = cacheableFieldParameters.getResponseParameters(PHONE);
        String emailKey = cacheableFieldParameters.getResponseParameters(EMAIL);
        String dateOfBirthKey = cacheableFieldParameters.getResponseParameters(DATE_OF_BIRTH);
        String nationalityKey = cacheableFieldParameters.getResponseParameters(NATIONALITY);
        String genderKey = cacheableFieldParameters.getResponseParameters(GENDER);
        String addressKey = cacheableFieldParameters.getResponseParameters(ADDRESS);
        String idTypeKey = cacheableFieldParameters.getResponseParameters(ID_TYPE);
        String idNumberKey = cacheableFieldParameters.getResponseParameters(ID_NUMBER);
        String cityKey = cacheableFieldParameters.getResponseParameters(CITY);
        String countryKey = cacheableFieldParameters.getResponseParameters(COUNTRY);
        String registrationDateKey = cacheableFieldParameters.getResponseParameters(REGISTRATION_DATE);
        String registrationAgentKey = cacheableFieldParameters.getResponseParameters(REGISTRATION_AGENT);
        String statusKey = cacheableFieldParameters.getResponseParameters(STATUS);
        String idTypeCodeKey = cacheableFieldParameters.getResponseParameters(ID_TYPE_CODE);
        String subscriberProfileCategoryKey = cacheableFieldParameters.getResponseParameters(SUBSCRIBER_PROFILE_CATEGORY);
        String genderCodeKey = cacheableFieldParameters.getResponseParameters(GENDERCODE);
        String statusCodeKey = cacheableFieldParameters.getResponseParameters(STATUSCODE);
        String nationalityCodeKey = cacheableFieldParameters.getResponseParameters(NATIONALITYCODE);
        String isoCodeKey = cacheableFieldParameters.getResponseParameters(ISOCode);


        Map<String, String> result = new HashMap<String, String>();
        try {
            PersonalDetails customer = businessMessage.getPrimarySenderInfo().getPersonalDetails();
            String fullName = customer.getFullName() == null ? "" : customer.getFullName();
            String phone = customer.getPhone() == null ? "" : customer.getPhone();
            String email = customer.getEmail() == null ? "" : customer.getEmail();
            String DateOfBirth = customer.getBirthDate() == null ? "" : customer.getBirthDate().toString();
            String nationality = customer.getNationality() == null ? "" : customer.getNationality();
            String customerId = String.valueOf(customer.getCustomerID());
            String gender = customer.getGender() == null ? "" : customer.getGender().name();
            String genderCode = (customer.getGender()) == null ? "" : Integer.toString(customer.getGender().ordinal());

            Set<CustomerDocument> document = customer.getDocument();
            String idType = "";
            String idNumber = "";
          /*  if (document.size() > 0) {
                CustomerDocument it = document.iterator().next();
                idType = it.getType().getName();
                idNumber = it.getDocumentData();
            }*/
            idType = customer.getIdentifierType();
            idNumber = customer.getIdentifier();
            Long idTypeCode = customer.getIdentifierTypeCode();
            String country = customer.getCountryId() == null ? "" : customer.getCountryId();
            String city = customer.getCityName() == null ? "" : customer.getCityName();
            String registrationDate = customer.getRegistrationDate() == null ? "" : customer.getRegistrationDate().toString();
            String registrationAgent = customer.getRegistrationAgent();
            Enums.UserStatus status = customer.getStatus();
            if(status==null){
                throw new QuickActionException("mandatory value is missing", ErrorCode.MISSING_MANDATORY_VALUE);
            }
            String statusCode =Integer.toString(status.ordinal());
            String nationalityCode = String.valueOf(customer.getNationalityCode());
            String isoCode = customer.getCountryIsoCode();
            String cutomerTypeSubsubscriberCategoryName = customer.getCutomerTypeSubsubscriberCategoryName();
            Address fullAddress = customer.getFullAddress();
            result.put(customerIdKey, customerId);
            result.put(fullNameKey, fullName);
            result.put(phoneKey, phone);
            result.put(emailKey, email);
            result.put(dateOfBirthKey, DateOfBirth);
            result.put(nationalityKey, nationality);
            result.put(genderKey, gender);
            result.put(addressKey, fullAddress.toString());
            result.put(idTypeKey, idType);
            result.put(idTypeCodeKey, idTypeCode.toString());
            result.put(idNumberKey, idNumber);
            result.put(countryKey, country);
            result.put(cityKey, city);
            result.put(registrationDateKey, registrationDate);
            result.put(registrationAgentKey, registrationAgent);
            result.put(statusKey, status.name());
            result.put(subscriberProfileCategoryKey, cutomerTypeSubsubscriberCategoryName);
            /**
             * unify enum
             */
            result.put(genderCodeKey, genderCode);
            result.put(statusCodeKey, statusCode);
            result.put(nationalityCodeKey, nationalityCode);
            result.put(isoCodeKey, isoCode);


        } catch (Exception e) {
            throw e;
        }
        return result;
    }
}