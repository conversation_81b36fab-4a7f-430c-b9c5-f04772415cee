package com.cit.vericash.apis.selfregistration;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.vericash.apis.commons.api.MuleResponseTransformer;
import com.cit.vericash.apis.customerlogin.CustomerLoginResponseTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.selfregistration.CustomerSelfRegistrationResponseTransformer")
public class CustomerSelfRegistrationResponseTransformer implements MuleResponseTransformer {

    @Autowired
    CustomerLoginResponseTransformer customerLoginResponseTransformer;

    public Object transform(BusinessMessage businessMessage) throws Exception {
        Map<String, String> result = new HashMap<String, String>();
        return customerLoginResponseTransformer.transform(businessMessage);
    }

}
