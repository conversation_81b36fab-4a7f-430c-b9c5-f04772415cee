<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
   http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">


    <changeSet failOnError="true" author="Mohamed Orabi" id="add table ITEM_TYPE">
        <createTable tableName="ITEM_TYPE">
            <column name="ID" type="NUMBER">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ITEM_NAME" type="VARCHAR2(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="add STATIC_SCREEN in APP_MENUS">
        <addColumn tableName="APP_MENUS">
            <column name="STATIC_SCREEN" type="NUMBER(1,0)" defaultValue="NULL"/>
        </addColumn>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="add SCREEN_NAME in APP_MENUS">
        <addColumn tableName="APP_MENUS">
            <column name="SCREEN_NAME" type="VARCHAR(255)" defaultValue="NULL"/>
        </addColumn>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="add ITEM_TYPE in APP_MENUS">
        <addColumn tableName="APP_MENUS">
            <column name="ITEM_TYPE" type="NUMBER(1,0)" defaultValue="NULL"/>
        </addColumn>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="Add new column PASS_DATA into APP_MENUS">
        <addColumn tableName="APP_MENUS">
            <column name="PASS_DATA" type="VARCHAR2(4000)">
                <constraints checkConstraint="PASS_DATA IS JSON"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="Add fk_item_type to APP_MENUS">
        <addForeignKeyConstraint baseTableName="APP_MENUS"
                                 baseColumnNames="ITEM_TYPE"
                                 constraintName="fk_item_type"
                                 referencedTableName="ITEM_TYPE"
                                 referencedColumnNames="ID"/>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="insert values in ITEM_TYPE table">
        <insert tableName="ITEM_TYPE">
            <column name="ID" value="1"/>
            <column name="ITEM_NAME" value="ITEM"/>
        </insert>
        <insert tableName="ITEM_TYPE">
            <column name="ID" value="2"/>
            <column name="ITEM_NAME" value="GROUP"/>
        </insert>
        <insert tableName="ITEM_TYPE">
            <column name="ID" value="3"/>
            <column name="ITEM_NAME" value="LOGOUT"/>
        </insert>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="update ITEM_TYPE in APP_MENUS with enum (1,2,3)">
        <update tableName="APP_MENUS">
            <column name="ITEM_TYPE" valueComputed="CASE
                    WHEN MENU_ITEM_NAME = 'General' OR MENU_ITEM_NAME = 'Personal Details' THEN 2
                    WHEN MENU_ITEM_NAME = 'Logout' THEN 3
                    ELSE 1
                    END"/>
        </update>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="add-static-screen-column to SME_AGENCY_APP_MENUS">
        <addColumn tableName="SME_AGENCY_APP_MENUS">
            <column name="STATIC_SCREEN" type="NUMBER(1,0)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet failOnError="true" author="Mohamed Orabi" id="add-screen-name-column SME_AGENCY_APP_MENUS">
        <addColumn tableName="SME_AGENCY_APP_MENUS">
            <column name="SCREEN_NAME" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet failOnError="true" author="Mohamed Orabi" id="add-item-type-column to SME_AGENCY_APP_MENUS">
        <addColumn tableName="SME_AGENCY_APP_MENUS">
            <column name="ITEM_TYPE" type="NUMBER(1,0)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet failOnError="true" author="Mohamed Orabi" id="add-pass-data-column to SME_AGENCY_APP_MENUS">
        <addColumn tableName="SME_AGENCY_APP_MENUS">
            <column name="PASS_DATA" type="VARCHAR2(4000)">
                <constraints checkConstraint="PASS_DATA IS JSON"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet failOnError="true" author="Mohamed Orabi" id="add-fk-item-type-sme-app">
        <addForeignKeyConstraint baseTableName="SME_AGENCY_APP_MENUS"
                                 baseColumnNames="ITEM_TYPE"
                                 constraintName="fk_item_type_sme_app"
                                 referencedTableName="ITEM_TYPE"
                                 referencedColumnNames="ID"/>
    </changeSet>

    <changeSet failOnError="true" author="Abubakr Mohamed" id="Add records of PDF to API_CHANNEL_JSON_SCHEMA">
        <sql>
            INSERT INTO PRO_MI_NO_C.API_CHANNEL_JSON_SCHEMA (API_CODE, FILE_NAME, CHANNEL_ID) VALUES('2411341', 'GeneratePdf.json', 2);
            INSERT INTO PRO_MI_NO_C.API_CHANNEL_JSON_SCHEMA (API_CODE, FILE_NAME, CHANNEL_ID) VALUES('2411341', 'GeneratePdf.json', 1);
        </sql>
    </changeSet>
    <changeSet failOnError="true" author="Abubakr Mohamed" id="Add records of TransferToOtherBank to API_CHANNEL_JSON_SCHEMA">
        <sql>
            INSERT INTO PRO_MI_NO_C.API_CHANNEL_JSON_SCHEMA (API_CODE, FILE_NAME, CHANNEL_ID) VALUES('***********', 'TransferToBank.json', 1);
            INSERT INTO PRO_MI_NO_C.API_CHANNEL_JSON_SCHEMA (API_CODE, FILE_NAME, CHANNEL_ID) VALUES('***********', 'TransferToBank.json', 2);
        </sql>
    </changeSet>


</databaseChangeLog>

