package com.cit.vericash.transactions;

public class TopupViewDetailsDTO {
    private String transactionName;
    private String senderName;
    private String senderWalletShortCode;
    private String senderUserId;
    private String senderPaymentMethod;
    private String senderMsisdn;
    private Double transactionAmount;
    //    private BigDecimal transactionFees;
//    private BigDecimal transactionTotalAmount;
    private String purchaseType;
    private String paymentOption;
    private String topupPhoneNumber;
    private String mobileOperator;
    private String MobileOperatorProduct;

    public String getTransactionName() {
        return transactionName;
    }

    public void setTransactionName(String transactionName) {
        this.transactionName = transactionName;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getSenderPaymentMethod() {
        return senderPaymentMethod;
    }

    public void setSenderPaymentMethod(String senderPaymentMethod) {
        this.senderPaymentMethod = senderPaymentMethod;
    }

    public String getSenderWalletShortCode() {
        return senderWalletShortCode;
    }

    public void setSenderWalletShortCode(String senderWalletShortCode) {
        this.senderWalletShortCode = senderWalletShortCode;
    }

    public String getSenderUserId() {
        return senderUserId;
    }

    public void setSenderUserId(String senderUserId) {
        this.senderUserId = senderUserId;
    }

    public String getSenderMsisdn() {
        return senderMsisdn;
    }

    public void setSenderMsisdn(String senderMsisdn) {
        this.senderMsisdn = senderMsisdn;
    }

    public Double getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(Double transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

//    public BigDecimal getTransactionFees() {
//        return transactionFees;
//    }
//
//    public void setTransactionFees(BigDecimal transactionFees) {
//        this.transactionFees = transactionFees;
//    }
//
//    public BigDecimal getTransactionTotalAmount() {
//        return transactionTotalAmount;
//    }
//
//    public void setTransactionTotalAmount(BigDecimal transactionTotalAmount) {
//        this.transactionTotalAmount = transactionTotalAmount;
//    }

    public String getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(String purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getPaymentOption() {
        return paymentOption;
    }

    public void setPaymentOption(String paymentOption) {
        this.paymentOption = paymentOption;
    }

    public String getTopupPhoneNumber() {
        return topupPhoneNumber;
    }

    public void setTopupPhoneNumber(String topupPhoneNumber) {
        this.topupPhoneNumber = topupPhoneNumber;
    }

    public String getMobileOperator() {
        return mobileOperator;
    }

    public void setMobileOperator(String mobileOperator) {
        this.mobileOperator = mobileOperator;
    }

    public String getMobileOperatorProduct() {
        return MobileOperatorProduct;
    }

    public void setMobileOperatorProduct(String mobileOperatorProduct) {
        MobileOperatorProduct = mobileOperatorProduct;
    }

}
