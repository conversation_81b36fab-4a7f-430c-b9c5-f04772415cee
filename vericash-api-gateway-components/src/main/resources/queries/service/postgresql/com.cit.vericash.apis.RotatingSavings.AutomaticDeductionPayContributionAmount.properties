Process_1=SELECT * FROM (SELECT ROTATING_SAVINGS. ID, \
  ROTATING_SAVINGS.CURRENT_TURN,\
  \ ROTATING_SAVINGS_MEM_TURNS.TURN_DAY, \
  CUSTOMER.WALLET_SHORT_CODE,\
  \ ROTATING_SAVINGS_MEM_TURNS. ID AS \"ROT TURN ID\", \
  ROTATING_SAVINGS_MEM_TURNS.MEMBER_ID,\
  \ ROTATING_SAVINGS_MEM_TURNS.TURN_NUMBER, \
  ROTATING_SAVINGS_MEM_TURNS.PAY_FROM_ACCOUNT, \
  CUSTOMER_GAR.PAYMENT_METHOD_TYPE, \
  CUSTOMER.WALLET_ID, \
  CUSTOMER.CUSTOMER_ID, ROW_NUMBER () OVER \
  (ORDER BY ROTATING_SAVINGS. ID, \
  ROTATING_SAVINGS.CURRENT_TURN, \
  ROTATING_SAVINGS_MEM_TURNS.TURN_DAY, \
  CUSTOMER.WALLET_SHORT_CODE, \
  ROTATING_SAVINGS_MEM_TURNS.MEMBER_ID, \
  ROTATING_SAVINGS_MEM_TURNS. ID, \
  ROTATING_SAVINGS_MEM_TURNS.TURN_NUMBER,\
  \ ROTATING_SAVINGS_MEM_TURNS.PAY_FROM_ACCOUNT, \
  CUSTOMER_GAR.PAYMENT_METHOD_TYPE, \
  CUSTOMER.WALLET_ID, CUSTOMER.CUSTOMER_ID ) \
  \ rn \
  FROM ROTATING_SAVINGS_PAYMENT_HIST \
  INNER JOIN ROTATING_SAVINGS_MEMBERS ON ROTATING_SAVINGS_PAYMENT_HIST.MEMBER_ID = ROTATING_SAVINGS_MEMBERS. ID \
  INNER JOIN ROTATING_SAVINGS_MEM_TURNS ON ROTATING_SAVINGS_PAYMENT_HIST.MEMBER_ID = ROTATING_SAVINGS_MEM_TURNS.MEMBER_ID \
  INNER JOIN ROTATING_SAVINGS ON ROTATING_SAVINGS_MEMBERS.ROTATING_SAVINGS_ID = ROTATING_SAVINGS. ID \
  \ INNER JOIN CUSTOMER ON ROTATING_SAVINGS_MEMBERS.CUSTOMER_ID = CUSTOMER.CUSTOMER_ID \
  INNER JOIN CUSTOMER_GAR ON ROTATING_SAVINGS_MEM_TURNS.PAY_FROM_ACCOUNT = CUSTOMER_GAR.GARID \
  WHERE ROTATING_SAVINGS_MEM_TURNS.AUTOMATIC_DEDUCTION = 1 AND ROTATING_SAVINGS_PAYMENT_HIST.PAYMENT_STATUS IN (0, 3) \
  AND ROTATING_SAVINGS_MEMBERS.ROTATING_SAVINGS_ID = ROTATING_SAVINGS. ID AND ROTATING_SAVINGS.CURRENT_TURN = ROTATING_SAVINGS_PAYMENT_HIST.TURN_NUMBER \
  AND TO_CHAR ( ROTATING_SAVINGS_MEM_TURNS.TURN_DAY, 'yyyy-MM-dd' ) = TO_CHAR (CURRENT_DATE, 'yyyy-MM-dd') \
  GROUP BY ROTATING_SAVINGS. ID, ROTATING_SAVINGS.CURRENT_TURN, ROTATING_SAVINGS_MEM_TURNS.TURN_DAY, CUSTOMER.WALLET_SHORT_CODE, ROTATING_SAVINGS_MEM_TURNS.MEMBER_ID, ROTATING_SAVINGS_MEM_TURNS. ID, ROTATING_SAVINGS_MEM_TURNS.TURN_NUMBER, ROTATING_SAVINGS_MEM_TURNS.PAY_FROM_ACCOUNT, CUSTOMER_GAR.PAYMENT_METHOD_TYPE, CUSTOMER.WALLET_ID, CUSTOMER.CUSTOMER_ID ) \
  WHERE RN BETWEEN %1$s AND %2$s
getBusinessServiceId=SELECT ID FROM BUSINESS_SERVICE_CONFIG WHERE BUSINESS_SERVICE_TYPE= 100038