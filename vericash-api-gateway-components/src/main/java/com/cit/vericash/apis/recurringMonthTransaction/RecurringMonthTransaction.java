package com.cit.vericash.apis.recurringMonthTransaction;

import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.vericash.apis.commons.util.connection.ConnectionUtil;
import com.cit.vericash.apis.commons.util.connection.Parameter;
import com.cit.vericash.apis.commons.util.connection.Record;
import com.cit.vericash.apis.dto.request.Request;
import com.cit.vericash.apis.recurringTransaction.RecurringTransactionUtil;
import com.cit.vericash.apis.recurringTransaction.TransactionRecurringDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Qualifier("com.cit.vericash.apis.recurringMonthTransaction.RecurringMonthTransaction")
public class RecurringMonthTransaction {


    @Autowired
    RecurringTransactionUtil recurringTransactionUtil;

    @Autowired
    ConnectionUtil connectionUtil;

    public List<RecurringMonthTransactionDTO> listOfMonthTransaction(Request request) throws SQLException, JsonProcessingException {
        Map<Object, List<TransactionRecurringDTO>> dayMap = new HashMap<>();
        List<RecurringMonthTransactionDTO> recurringMonthTransactions = new ArrayList<>();

        Long customerID = request.getMessage().getPayload().getAttributeAsLong("customerId");
        int month = (int) (request.getMessage().getPayload().get("month"));
        int monthadd = month + 1;
        int year = (int) (request.getMessage().getPayload().get("year"));
        List<Parameter> parameters = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("listOfMonthTransaction" , this.getClass(),
                String.valueOf(customerID) , String.valueOf(year) , String.valueOf(month) , String.valueOf(year),String.valueOf(monthadd));
        /*String query = "SELECT SCHEDULER.ID,SCHEDULER.NAME,SCHEDULER_TYPE.NAME as TYPE,SCHEDULER.END_TIME,SCHEDULER.REPEAT_COUNT,SCHEDULER.START_TIME,\n" +
                "SCHEDULER_RECURRING_PATTERN.NAME as FREQUENCY,SCHEDULER_STATUS.NAME as STATUS,SCHEDULER.LAST_OCCURRENCE_STATUS,SCHEDULER_OCCURRENCE.DETAILS,SCHEDULER_OCCURRENCE.DATE_TIME as DATIUM, EXTRACT (day from DATE_TIME) \"Day\" from \"SCHEDULER\" \n" +
                "INNER JOIN  SCHEDULER_TYPE ON SCHEDULER.TYPE=SCHEDULER_TYPE.ID\n" +
                "INNER JOIN  SCHEDULER_RECURRING_PATTERN ON SCHEDULER.RECURRING_PATTERN=SCHEDULER_RECURRING_PATTERN.ID\n" +
                "INNER JOIN  SCHEDULER_STATUS ON SCHEDULER.STATUS=SCHEDULER_STATUS.ID\n" +
                "INNER JOIN  SCHEDULER_OCCURRENCE ON SCHEDULER.ID=SCHEDULER_OCCURRENCE.SCHEDULER_ID\n" +
                "\n" +
                "WHERE CUSTOMER_ID = " + customerID + " AND SCHEDULER_OCCURRENCE.DATE_TIME >= TO_DATE('" + year + "-" + month + "-01','YYYY-MM-DD') AND SCHEDULER_OCCURRENCE.DATE_TIME < TO_DATE('" + year + "-" + monthadd + "-01','YYYY-MM-DD')  order by DATIUM DESC";
*/
        fields = setFields(fields);
        List<Record> records = connectionUtil.executeSelect(sqlQuery, parameters, fields);
        for (Record record : records) {
            TransactionRecurringDTO recurringMonthTransactionDTO = new TransactionRecurringDTO();

            Timestamp timeStamps = (Timestamp) record.get("DATIUM");
            int day = timeStamps.getDate();
            if (dayMap.get(day) == null) {
                List<TransactionRecurringDTO> transactionRecurringDTOs = new ArrayList<>();
                TransactionRecurringDTO transactionRecurringDTO = recurringTransactionUtil.addTransactionRecurringDTO(record);
                System.out.println(recurringMonthTransactionDTO);
                System.out.println("------------------------------------------");
                transactionRecurringDTOs.add(transactionRecurringDTO);
                dayMap.put(day, transactionRecurringDTOs);
            } else {
                List<TransactionRecurringDTO> transactionRecurringDTOs = dayMap.get(day);
                TransactionRecurringDTO transactionRecurringDTO = recurringTransactionUtil.addTransactionRecurringDTO(record);
                transactionRecurringDTOs.add(transactionRecurringDTO);
                System.out.println(transactionRecurringDTO);
                System.out.println("------------------------------------------");
            }

        }

        Object[] keySet = dayMap.keySet().toArray();
        for (Object o : keySet) {
            RecurringMonthTransactionDTO recurringMonthTransaction = new RecurringMonthTransactionDTO();
            recurringMonthTransaction.setDay(o);
            recurringMonthTransaction.setCountOfRecurringTransactions(dayMap.get(o).size());
            recurringMonthTransaction.setRecurringTransactions(dayMap.get(o));
            recurringMonthTransactions.add(recurringMonthTransaction);
        }

        return recurringMonthTransactions;

    }


    public List<String> setFields(List<String> fields) {
        fields.add("ID");
        fields.add("NAME");
        fields.add("TYPE");
        fields.add("END_TIME");
        fields.add("REPEAT_COUNT");
        fields.add("START_TIME");
        fields.add("FREQUENCY");
        fields.add("STATUS");
        fields.add("LAST_OCCURRENCE_STATUS");
        fields.add("DATIUM");
        fields.add("Day");
        fields.add("DETAILS");


        return fields;
    }
}