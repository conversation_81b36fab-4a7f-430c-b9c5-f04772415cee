generateLoginSessionId=select SESSION_ID from API_USER_SESSION \
  LEFT JOIN API_CHANNEL ON API_USER_SESSION.CHANNEL_IDD = API_CHANNEL.ID \
  where API_USER_SESSION.USER_ID= '%1$s' and API_CHANNEL.CHANNEL_NAME= '%2$s' \
  and API_CHANNEL.WALLET_SHORT_CODE= '%3$s' 
saveUserSessionID=INSERT INTO API_USER_SESSION (ID,USER_ID,SESSION_ID,SESSION_KEY,CREATION_DATE,CHANNEL_IDD) \
  VALUES (%1$s,'%2$s','%3$s','%4$s',TO_DATE('%5$s','yyyy-MM-dd HH24:mi:ss'), \
  (SELECT ID from API_CHANNEL WHERE LOWER(CHANNEL_NAME)= '%6$s' and WALLET_SHORT_CODE= '%7$s'))
updateUserSessionID=UPDATE API_USER_SESSION SET SESSION_ID = '%1$s', \
  SESSION_KEY = '%2$s', CREATION_DATE = TO_DATE('%3$s','yyyy-MM-dd HH24:mi:ss')  \
  WHERE USER_ID = '%4$s' AND CHANNEL_IDD = \
  (SELECT ID from API_CHANNEL WHERE LOWER(CHANNEL_NAME)= '%5$s' \
  and WALLET_SHORT_CODE= '%6$s' )
getGeneratedId=SELECT API_USER_SESSION_SEQ.NEXTVAL FROM DUAL

