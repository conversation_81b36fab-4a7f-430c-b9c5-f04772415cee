CreatQuery_distination=SELECT PM. NAME "paymentMethodName",DPM. ID "paymentMethodType",PM."TYPE",\
  PMT. NAME , SPMS.API_CODE, SPMS.USER_TYPE ,PM.PAYMETN_METHOD_ORDER  FROM CUSTOMER C \
  JOIN DEST_PAYMENT_METHOD_CUST_PROF DPMC ON C.CUSTOMER_TYPE_ID = DPMC.CUSTOMER_TYPE_ID \
  JOIN PAYMENT_METHOD PM ON PM. ID = DPMC.PAYMENT_METHOD_ID \
  JOIN DESTINATION_PAYMENT_METHOD DPM ON DPM. ID = PM. ID \
  left join SERVICES_PAYMENTMETHOD  SPMS on  DPM.ID =SPMS.PAYMENMETHODTYPE  \
  LEFT JOIN PAYMENT_METHOD_TYPE PMT ON PMT. ID = PM.\"TYPE\" \
  where C.CUSTOMER_ID = ( select  BE.MAIN_CUSTOMER_ID  From  BE_CUSTOMER  BE \
  LEFT JOIN CUSTOMER C  ON C.BE_CUSTOMER_ID = BE.ID   where C.USER_ID= ? ) \
  AND   SPMS.LOOKUP_ENUM = ?  AND  SPMS.USER_TYPE = ? 
condition_1_distination=AND   SPMS.API_CODE = ?
condition_2_distination=AND   SPMS.API_CODE in (
forLoop_1_distination=? , 
forLoop_2_distination=? ) 
