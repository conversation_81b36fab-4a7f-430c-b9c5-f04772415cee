<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <changeSet id="1" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="100200"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="split by wallet base"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_TYPE" value="100200"/>
            <column name="SERVICE_CODE" value="100200"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="100117"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="2" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="100201"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="split by digital wallet base"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_TYPE" value="100201"/>
            <column name="SERVICE_CODE" value="100201"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="100117"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="3" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="100202"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="split by wallet base"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_TYPE" value="100202"/>
            <column name="SERVICE_CODE" value="100202"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="100109"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="4" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="100203"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="split by digital wallet base"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_TYPE" value="100203"/>
            <column name="SERVICE_CODE" value="100203"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="100109"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="5" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="100204"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="split by wallet base"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_TYPE" value="100204"/>
            <column name="SERVICE_CODE" value="100204"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="100122"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="6" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="100205"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="split by digital wallet base"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_TYPE" value="100205"/>
            <column name="SERVICE_CODE" value="100205"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="100122"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="7" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="100206"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="split by wallet base"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_TYPE" value="100206"/>
            <column name="SERVICE_CODE" value="100206"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="100125"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="8" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="100207"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="split by digital wallet base"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_TYPE" value="100207"/>
            <column name="SERVICE_CODE" value="100207"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="100125"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="9" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="100208"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="split by wallet base"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_TYPE" value="100208"/>
            <column name="SERVICE_CODE" value="100208"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="100111"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="10" author="khaled">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="100209"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="split by digital wallet base"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="SERVICE_TYPE" value="100209"/>
            <column name="SERVICE_CODE" value="100209"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="100111"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="11" author="khaled">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" value="166565657780022482"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="split by wallet base"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="100200"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" value="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" value="NULL"/>
            <column name="NARRATION" value="NULL"/>
        </insert>
    </changeSet>

    <changeSet id="12" author="khaled">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" value="166565657780022481"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="split by digital wallet base"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="100201"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" value="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" value="NULL"/>
            <column name="NARRATION" value="NULL"/>
        </insert>
    </changeSet>

    <changeSet id="13" author="khaled">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" value="166565657780022484"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="split by wallet base"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="100202"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" value="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" value="NULL"/>
            <column name="NARRATION" value="NULL"/>
        </insert>
    </changeSet>

    <changeSet id="14" author="khaled">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" value="166565657780022483"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="split by digital wallet base"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="100203"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" value="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" value="NULL"/>
            <column name="NARRATION" value="NULL"/>
        </insert>
    </changeSet>

    <changeSet id="15" author="khaled">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" value="166565657780022486"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="split by digital wallet base"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="100205"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" value="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" value="NULL"/>
            <column name="NARRATION" value="NULL"/>
        </insert>
    </changeSet>

    <changeSet id="16" author="khaled">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" value="166565657780022487"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="split by wallet base"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="100206"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" value="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" value="NULL"/>
            <column name="NARRATION" value="NULL"/>
        </insert>
    </changeSet>

    <changeSet id="17" author="khaled">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" value="166565657780022488"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="split by digital wallet base"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="100207"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" value="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" value="NULL"/>
            <column name="NARRATION" value="NULL"/>
        </insert>
    </changeSet>

    <changeSet id="18" author="khaled">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" value="166565657780022489"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="split by wallet base"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="100208"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" value="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" value="NULL"/>
            <column name="NARRATION" value="NULL"/>
        </insert>
    </changeSet>

    <changeSet id="19" author="khaled">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" value="166565657780022490"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="split by digital wallet base"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="100209"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" value="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" value="NULL"/>
            <column name="NARRATION" value="NULL"/>
        </insert>
    </changeSet>

</databaseChangeLog>