<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <!-- Insert Service Configurations -->
    <changeSet failOnError="false" author="Sheri<PERSON> <PERSON>" id="Insert Service Configurations 1">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (300000, 0, 'Transfer - Wallet to Wallet', 0, '300000', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 2">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (300001, 0, 'Transfer Money MKENTO Wallet Prepaied Card GTP', 0, '300001', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 3">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (300002, 0, 'Transfer Money Prepaied Card GTP To MKENTO Wallet', 0, '300002', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 4">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (300003, 0, 'Transfer Money Prepaied Card GTP To Prepaied Card GTP', 0, '300003', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 5">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878799929, 0, 'Transfer Money - From CIB To Dest Prepaid by Customer', 0, '878799929', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 6">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878799930, 0, 'Transfer Money - From Debit Card To Dest Prepaid by Customer', 0, '878799930', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 7">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878899998, 0, 'Transfer - Wallet to Source Debit', 0, '878899998', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 8">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878899999, 0, 'Transfer Money - Wallet to CIB', 0, '878899999', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
		
    </changeSet>
	<changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 9">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900000, 0, 'Transfer Money- Wallet to Source Virtual Card', 0, '878900000', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 10">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900001, 0, 'Transfer Money - From Debit to Wallet', 0, '878900001', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 11">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900002, 0, 'Transfer Money - From Debit to Debit card', 0, '878900002', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 12">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900003, 0, 'Transfer Money - From Debit to CIB', 0, '878900003', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 13">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900004, 0, 'Transfer Money - From Debit Source Prepaid', 0, '878900004', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 14">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, ENABLED_PROFILES, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900005, 0, 'Transfer Money - From Debit to Source Virtual', 0, '878900005', 1, 0, 0, 0, 0, 1, 5000, 0);
        </sql>
    </changeSet>
	
	 <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 15">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900006, 0, 'Transfer Money - From CIB To Wallet', 0, '878900006', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 16">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900007, 0, 'Transfer Money - From CIB to Debit Card', 0, '878900007', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 17">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900008, 0, 'Transfer Money - From CIB To CIB', 0, '878900008', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 18">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900009, 0, 'Transfer Money - From CIB To Prepaid', 0, '878900009', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 19">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900010, 0, 'Transfer Money - From CIB To Virtual Card', 0, '878900010', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>
	
	<changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 20">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900011, 0, 'Transfer Money - From Source prepaid to Debit', 0, '878900011', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 21">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900012, 0, 'Transfer Money - From Source prepaid to CIB', 0, '878900012', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 22">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900013, 0, 'Transfer Money - From Source prepaid to Source virtual card', 0, '878900013', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 23">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900014, 0, 'Transfer Money - From Source Virtual To Wallet', 0, '878900014', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 24">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900016, 0, 'Transfer Money - From Source Virtual To CIB', 0, '878900016', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 25">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900015, 0, 'Transfer Money - From Source Virtual To Debit', 0, '878900015', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 26">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900017, 0, 'Transfer Money - From Source Virtual To Source Prepaid', 0, '878900017', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

    <changeSet failOnError="false" author="Sherif Ahmed" id="Insert Service Configurations 27">
        <sql>
            INSERT INTO PRO_MI_NO_C.SERVICE_CONFIG_MAP (SERVICE_TYPE_ID, IS_GENERIC_SERVICE, SERVICE_TYPE_NAME, SERVICE_MODE, SERVICE_CODE, IS_INDEMNITY, SEPARATE_QUEUE, SEPARATE_FRAMEWORK_QUEUE, HAS_WORKFLOW, SERVICE_CATEGORY, PARENT_SERVICE_CODE, ROLE_BASED)
            VALUES (878900018, 0, 'Transfer Money - From Source Virtual To Source Virtual', 0, '878900018', 1, 0, 0, 0, 0, 5000, 0);
        </sql>
    </changeSet>

</databaseChangeLog>
