package com.cit.vericash.config;

import com.cit.vericash.api.interceptor.ProductServiceInterceptor;
import org.mule.runtime.extension.api.annotation.Configuration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;


@Configuration
   // @EnableWebMvc
    @EntityScan(basePackages = "com.cit.vericash.api.interceptor")
    public class ProductServiceInterceptorAppConfig implements WebMvcConfigurer{
    private static final String PATHS = "/vericashBackend";
    @Autowired
    ProductServiceInterceptor productServiceInterceptor;
//        @Bean
//        public ProductServiceInterceptor requestHandler() {
//            return new ProductServiceInterceptor();
//        }

//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(requestHandler()).addPathPatterns(PATHS);
//
//    }

        @Bean
        public WebMvcConfigurerAdapter adapter() {
            System.out.println("zoooooooooooooz");
            return new WebMvcConfigurerAdapter() {
                @Override
                public void addInterceptors(InterceptorRegistry registry) {
                    System.out.println("Adding interceptors");
                    registry.addInterceptor(productServiceInterceptor).addPathPatterns(PATHS);
                    super.addInterceptors(registry);
                }
            };
        }
}
